"""
Company service for the QueTeToca API.
"""
import logging
from datetime import datetime, date, timedelta
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, func, extract

from ..models import Company, Bar, User, QueueEntry
from ..models.enums import QueueStatus, UserRole
from ..schemas.company import CompanyCreate, CompanyUpdate

logger = logging.getLogger(__name__)


class CompanyService:
    """Service for company operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_company_by_id(self, company_id: str) -> Optional[Company]:
        """Get company by ID."""
        return self.db.query(Company).filter(Company.id == company_id).first()
    
    def get_companies(self, skip: int = 0, limit: int = 100, 
                     is_active: Optional[bool] = None) -> List[Company]:
        """Get list of companies with optional filters."""
        query = self.db.query(Company)
        
        if is_active is not None:
            active_value = "true" if is_active else "false"
            query = query.filter(Company.is_active == active_value)
        
        return query.offset(skip).limit(limit).all()
    
    def create_company(self, company_data: CompanyCreate) -> Company:
        """Create a new company."""
        company = Company(**company_data.model_dump())
        self.db.add(company)
        self.db.commit()
        self.db.refresh(company)
        
        logger.info(f"Company created: {company.name} (ID: {company.id})")
        return company
    
    def update_company(self, company_id: str, company_data: CompanyUpdate) -> Optional[Company]:
        """Update an existing company."""
        company = self.get_company_by_id(company_id)
        if not company:
            return None
        
        # Update fields
        update_data = company_data.model_dump(exclude_unset=True)
        
        # Handle is_active boolean to string conversion
        if 'is_active' in update_data:
            update_data['is_active'] = "true" if update_data['is_active'] else "false"
        
        for field, value in update_data.items():
            setattr(company, field, value)
        
        self.db.commit()
        self.db.refresh(company)
        
        logger.info(f"Company updated: {company.name} (ID: {company.id})")
        return company
    
    def delete_company(self, company_id: str) -> bool:
        """Delete a company."""
        company = self.get_company_by_id(company_id)
        if not company:
            return False
        
        # Check if company has active bars
        active_bars = self.db.query(Bar).filter(
            and_(Bar.company_id == company_id, Bar.is_active == "true")
        ).count()
        
        if active_bars > 0:
            raise ValueError(f"Cannot delete company with {active_bars} active bars")
        
        self.db.delete(company)
        self.db.commit()
        
        logger.info(f"Company deleted: {company.name} (ID: {company.id})")
        return True
    
    def add_admin(self, company_id: str, user_uid: str) -> Optional[Company]:
        """Add an admin to a company."""
        company = self.get_company_by_id(company_id)
        if not company:
            return None
        
        # Check if user exists
        user = self.db.query(User).filter(User.uid == user_uid).first()
        if not user:
            raise ValueError(f"User with UID {user_uid} not found")
        
        # Add admin
        company.add_admin(user_uid)
        
        # Update user role and company
        user.role = UserRole.COMPANY_ADMIN
        user.company_id = company_id
        
        self.db.commit()
        self.db.refresh(company)
        
        logger.info(f"Added admin {user_uid} to company {company_id}")
        return company
    
    def remove_admin(self, company_id: str, user_uid: str) -> Optional[Company]:
        """Remove an admin from a company."""
        company = self.get_company_by_id(company_id)
        if not company:
            return None
        
        # Remove admin
        company.remove_admin(user_uid)
        
        # Update user role
        user = self.db.query(User).filter(User.uid == user_uid).first()
        if user and user.company_id == company_id:
            user.role = UserRole.BAR_MANAGER  # Demote to manager
        
        self.db.commit()
        self.db.refresh(company)
        
        logger.info(f"Removed admin {user_uid} from company {company_id}")
        return company
    
    def get_company_stats(self, company_id: str, 
                         start_date: Optional[date] = None,
                         end_date: Optional[date] = None) -> Dict[str, Any]:
        """Get comprehensive statistics for a company."""
        company = self.get_company_by_id(company_id)
        if not company:
            return {}
        
        # Set default date range (last 30 days)
        if not end_date:
            end_date = date.today()
        if not start_date:
            start_date = end_date - timedelta(days=30)
        
        # Basic counts
        total_bars = self.db.query(Bar).filter(Bar.company_id == company_id).count()
        active_bars = self.db.query(Bar).filter(
            and_(Bar.company_id == company_id, Bar.is_active == "true")
        ).count()
        
        total_users = self.db.query(User).filter(User.company_id == company_id).count()
        
        # Queue statistics for the date range
        bar_ids = [bar.id for bar in self.db.query(Bar).filter(Bar.company_id == company_id).all()]
        
        if bar_ids:
            # Total queue entries in date range
            total_queue_entries = self.db.query(QueueEntry).filter(
                and_(
                    QueueEntry.bar_id.in_(bar_ids),
                    func.date(QueueEntry.created_at) >= start_date,
                    func.date(QueueEntry.created_at) <= end_date
                )
            ).count()
            
            # Active queue entries (current)
            active_queue_entries = self.db.query(QueueEntry).filter(
                and_(
                    QueueEntry.bar_id.in_(bar_ids),
                    QueueEntry.status.in_([QueueStatus.WAITING, QueueStatus.CALLED])
                )
            ).count()
            
            # Completed entries in date range
            completed_queue_entries = self.db.query(QueueEntry).filter(
                and_(
                    QueueEntry.bar_id.in_(bar_ids),
                    QueueEntry.status == QueueStatus.COMPLETED,
                    func.date(QueueEntry.created_at) >= start_date,
                    func.date(QueueEntry.created_at) <= end_date
                )
            ).count()
            
            # Cancelled entries in date range
            cancelled_queue_entries = self.db.query(QueueEntry).filter(
                and_(
                    QueueEntry.bar_id.in_(bar_ids),
                    QueueEntry.status == QueueStatus.CANCELLED,
                    func.date(QueueEntry.created_at) >= start_date,
                    func.date(QueueEntry.created_at) <= end_date
                )
            ).count()
            
            # Calculate average wait time
            completed_with_times = self.db.query(QueueEntry).filter(
                and_(
                    QueueEntry.bar_id.in_(bar_ids),
                    QueueEntry.status == QueueStatus.COMPLETED,
                    QueueEntry.called_at.isnot(None),
                    func.date(QueueEntry.created_at) >= start_date,
                    func.date(QueueEntry.created_at) <= end_date
                )
            ).all()
            
            average_wait_time = None
            if completed_with_times:
                wait_times = [entry.wait_time_minutes for entry in completed_with_times if entry.wait_time_minutes]
                if wait_times:
                    average_wait_time = sum(wait_times) / len(wait_times)
            
            # Peak hours analysis
            hourly_entries = self.db.query(
                extract('hour', QueueEntry.created_at).label('hour'),
                func.count(QueueEntry.id).label('count')
            ).filter(
                and_(
                    QueueEntry.bar_id.in_(bar_ids),
                    func.date(QueueEntry.created_at) >= start_date,
                    func.date(QueueEntry.created_at) <= end_date
                )
            ).group_by(extract('hour', QueueEntry.created_at)).all()
            
            peak_hours = []
            if hourly_entries:
                # Get top 3 peak hours
                sorted_hours = sorted(hourly_entries, key=lambda x: x.count, reverse=True)
                peak_hours = [int(hour.hour) for hour in sorted_hours[:3]]
        
        else:
            # No bars in company
            total_queue_entries = 0
            active_queue_entries = 0
            completed_queue_entries = 0
            cancelled_queue_entries = 0
            average_wait_time = None
            peak_hours = []
        
        # Bar performance
        bar_stats = []
        for bar in self.db.query(Bar).filter(Bar.company_id == company_id).all():
            bar_entries = self.db.query(QueueEntry).filter(
                and_(
                    QueueEntry.bar_id == bar.id,
                    func.date(QueueEntry.created_at) >= start_date,
                    func.date(QueueEntry.created_at) <= end_date
                )
            ).count()
            
            bar_stats.append({
                "bar_id": bar.id,
                "bar_name": bar.name,
                "total_entries": bar_entries,
                "is_active": bar.active
            })
        
        return {
            "company_id": company_id,
            "company_name": company.name,
            "date_range": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            },
            "total_bars": total_bars,
            "active_bars": active_bars,
            "total_users": total_users,
            "total_queue_entries": total_queue_entries,
            "active_queue_entries": active_queue_entries,
            "completed_queue_entries": completed_queue_entries,
            "cancelled_queue_entries": cancelled_queue_entries,
            "average_wait_time": average_wait_time,
            "peak_hours": peak_hours,
            "bar_performance": bar_stats
        }
    
    def get_company_settings(self, company_id: str) -> Dict[str, Any]:
        """Get company settings."""
        company = self.get_company_by_id(company_id)
        if not company:
            return {}
        
        return company.settings or {}
    
    def update_company_settings(self, company_id: str, settings: Dict[str, Any]) -> Optional[Company]:
        """Update company settings."""
        company = self.get_company_by_id(company_id)
        if not company:
            return None
        
        # Update settings
        for key, value in settings.items():
            company.set_setting(key, value)
        
        self.db.commit()
        self.db.refresh(company)
        
        logger.info(f"Settings updated for company: {company.name} (ID: {company.id})")
        return company
