# QueTeToca API

API REST completa para la gestión de colas en bares desarrollada con FastAPI y Python.

## Características

- 🚀 **FastAPI** con documentación automática
- 🔐 **Autenticación** con Firebase Admin SDK
- 🗄️ **Base de datos** PostgreSQL/SQLite con SQLAlchemy
- ⚡ **WebSockets** para actualizaciones en tiempo real
- 📊 **Estadísticas** de colas y empresas
- 🔒 **Rate limiting** y middleware de seguridad
- 🐳 **Docker** para containerización
- 🧪 **Tests** unitarios con pytest
- 📱 **Códigos QR** para acceso rápido

## Instalación

### Con Docker (Recomendado)

1. Clona el repositorio:
```bash
git clone <repository-url>
cd QueTeTocaApi
```

2. Copia el archivo de configuración:
```bash
cp .env.example .env
```

3. Configura las variables de entorno en `.env`

4. Coloca tu archivo de credenciales de Firebase como `firebase-credentials.json`

5. Ejecuta con Docker Compose:
```bash
docker-compose up -d
```

### Instalación Local

1. Instala Python 3.11+
2. Crea un entorno virtual:
```bash
python -m venv venv
source venv/bin/activate  # En Windows: venv\Scripts\activate
```

3. Instala dependencias:
```bash
pip install -r requirements.txt
```

4. Configura variables de entorno
5. Ejecuta la aplicación:
```bash
uvicorn api.app.main:app --reload
```

## Documentación API

Una vez ejecutándose, accede a:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## Endpoints Principales

### Autenticación
- `POST /auth/verify-token` - Verificar token de Firebase
- `GET /auth/me` - Obtener usuario actual

### Bares
- `GET /bars` - Listar bares
- `POST /bars` - Crear nuevo bar
- `GET /bars/{bar_id}` - Obtener bar específico
- `PUT /bars/{bar_id}` - Actualizar bar
- `DELETE /bars/{bar_id}` - Eliminar bar

### Colas
- `GET /bars/{bar_id}/queue` - Obtener cola de un bar
- `POST /bars/{bar_id}/queue` - Unirse a la cola
- `PUT /queue/{entry_id}/call` - Llamar cliente
- `PUT /queue/{entry_id}/complete` - Completar servicio

## Testing

```bash
pytest api/tests/
```

## Estructura del Proyecto

```
api/
├── app/
│   ├── main.py              # Aplicación principal
│   ├── config.py            # Configuración
│   ├── database.py          # Configuración de base de datos
│   ├── models/              # Modelos SQLAlchemy
│   ├── schemas/             # Esquemas Pydantic
│   ├── routers/             # Endpoints de la API
│   ├── services/            # Lógica de negocio
│   ├── middleware/          # Middleware personalizado
│   └── utils/               # Utilidades
└── tests/                   # Tests unitarios
```
