"""
Logging configuration for the QueTeToca API.
"""
import logging
import logging.config
import sys
from typing import Dict, Any
import structlog
from datetime import datetime

from ..config import get_settings

settings = get_settings()


def setup_logging():
    """Configure structured logging for the application."""
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer() if settings.log_format == "json" else structlog.dev.ConsoleRenderer(),
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Configure standard logging
    logging_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "json": {
                "()": structlog.stdlib.ProcessorFormatter,
                "processor": structlog.processors.JSONRenderer(),
            },
            "console": {
                "()": structlog.stdlib.ProcessorFormatter,
                "processor": structlog.dev.ConsoleRenderer(colors=True),
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "stream": sys.stdout,
                "formatter": "json" if settings.log_format == "json" else "console",
            },
        },
        "loggers": {
            "": {
                "handlers": ["console"],
                "level": settings.log_level,
                "propagate": True,
            },
            "uvicorn": {
                "handlers": ["console"],
                "level": "INFO",
                "propagate": False,
            },
            "uvicorn.error": {
                "handlers": ["console"],
                "level": "INFO",
                "propagate": False,
            },
            "uvicorn.access": {
                "handlers": ["console"],
                "level": "INFO",
                "propagate": False,
            },
            "sqlalchemy.engine": {
                "handlers": ["console"],
                "level": "WARNING",
                "propagate": False,
            },
        },
    }
    
    logging.config.dictConfig(logging_config)


class RequestLoggingMiddleware:
    """Middleware to log HTTP requests and responses."""
    
    def __init__(self, app):
        self.app = app
        self.logger = structlog.get_logger(__name__)
    
    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        start_time = datetime.utcnow()
        
        # Extract request information
        method = scope["method"]
        path = scope["path"]
        query_string = scope.get("query_string", b"").decode()
        client_ip = self._get_client_ip(scope)
        user_agent = self._get_header(scope, "user-agent")
        
        # Log request
        self.logger.info(
            "HTTP request started",
            method=method,
            path=path,
            query_string=query_string,
            client_ip=client_ip,
            user_agent=user_agent,
        )
        
        # Capture response
        response_info = {"status_code": None, "response_size": 0}
        
        async def send_wrapper(message):
            if message["type"] == "http.response.start":
                response_info["status_code"] = message["status"]
            elif message["type"] == "http.response.body":
                response_info["response_size"] += len(message.get("body", b""))
            
            await send(message)
        
        try:
            await self.app(scope, receive, send_wrapper)
        except Exception as exc:
            # Log error
            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()
            
            self.logger.error(
                "HTTP request failed",
                method=method,
                path=path,
                client_ip=client_ip,
                duration=duration,
                error=str(exc),
                exc_info=True,
            )
            raise
        else:
            # Log successful response
            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()
            
            self.logger.info(
                "HTTP request completed",
                method=method,
                path=path,
                client_ip=client_ip,
                status_code=response_info["status_code"],
                response_size=response_info["response_size"],
                duration=duration,
            )
    
    def _get_client_ip(self, scope: Dict[str, Any]) -> str:
        """Extract client IP from request scope."""
        # Check for forwarded headers first
        headers = dict(scope.get("headers", []))
        
        # X-Forwarded-For header
        forwarded_for = headers.get(b"x-forwarded-for")
        if forwarded_for:
            return forwarded_for.decode().split(",")[0].strip()
        
        # X-Real-IP header
        real_ip = headers.get(b"x-real-ip")
        if real_ip:
            return real_ip.decode()
        
        # Fall back to client address
        client = scope.get("client")
        if client:
            return client[0]
        
        return "unknown"
    
    def _get_header(self, scope: Dict[str, Any], header_name: str) -> str:
        """Extract header value from request scope."""
        headers = dict(scope.get("headers", []))
        header_value = headers.get(header_name.lower().encode())
        return header_value.decode() if header_value else ""


def get_logger(name: str = None) -> structlog.BoundLogger:
    """Get a structured logger instance."""
    return structlog.get_logger(name)


def log_user_action(user_id: str, action: str, resource: str = None, details: Dict[str, Any] = None):
    """Log user actions for audit purposes."""
    logger = get_logger("audit")
    
    logger.info(
        "User action",
        user_id=user_id,
        action=action,
        resource=resource,
        details=details or {},
        timestamp=datetime.utcnow().isoformat(),
    )


def log_queue_event(bar_id: str, event_type: str, entry_id: str = None, details: Dict[str, Any] = None):
    """Log queue-related events."""
    logger = get_logger("queue")
    
    logger.info(
        "Queue event",
        bar_id=bar_id,
        event_type=event_type,
        entry_id=entry_id,
        details=details or {},
        timestamp=datetime.utcnow().isoformat(),
    )


def log_security_event(event_type: str, client_ip: str, details: Dict[str, Any] = None):
    """Log security-related events."""
    logger = get_logger("security")
    
    logger.warning(
        "Security event",
        event_type=event_type,
        client_ip=client_ip,
        details=details or {},
        timestamp=datetime.utcnow().isoformat(),
    )
