"""
Queue router for the QueTeToca API.
"""
import logging
from typing import Annotated, Optional
from fastapi import APIRouter, Depends, HTTPException, status, WebSocket, Query
from sqlalchemy.orm import Session

from ..database import get_db
from ..schemas.queue import (
    QueueEntryCreate,
    QueueEntryUpdate,
    QueueEntryResponse,
    QueueListResponse,
    QueueEntryDetailResponse,
    QueueJoinRequest,
    QueueJoinResponse,
    QueueCallResponse,
    QueueCompleteResponse,
    QueueCancelResponse,
    QueueStatsResponse
)
from ..services.queue import QueueService
from ..services.websocket import websocket_handler, manager
from ..services.dependencies import (
    get_current_active_user,
    get_optional_current_user
)
from ..models import User

logger = logging.getLogger(__name__)

router = APIRouter(tags=["Queue"])


@router.get("/bars/{bar_id}/queue", response_model=QueueListResponse)
async def get_bar_queue(
    bar_id: str,
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[Optional[User], Depends(get_optional_current_user)],
    include_completed: bool = Query(False, description="Include completed entries")
):
    """
    Get the current queue for a bar.
    
    Args:
        bar_id: Bar ID
        db: Database session
        current_user: Current user (optional for public access)
        include_completed: Whether to include completed entries
        
    Returns:
        Current queue information
    """
    try:
        queue_service = QueueService(db)
        
        # Get queue entries
        entries = queue_service.get_bar_queue(bar_id, include_completed)
        
        # Filter sensitive information for non-staff users
        if not current_user or current_user.role.value == "CUSTOMER":
            # Customers can only see limited information
            for entry in entries:
                entry.phone_number = None  # Hide phone numbers
                if entry.customer_uid != (current_user.uid if current_user else None):
                    entry.customer_name = f"Customer #{entry.queue_number}"
        
        entry_responses = [QueueEntryResponse.model_validate(entry) for entry in entries]
        
        # Calculate statistics
        waiting_entries = [e for e in entry_responses if e.is_waiting]
        called_entries = [e for e in entry_responses if e.is_called]
        
        estimated_wait = 0
        if waiting_entries:
            estimated_wait = max(e.estimated_wait_time or 0 for e in waiting_entries)
        
        return QueueListResponse(
            success=True,
            message=f"Retrieved queue for bar {bar_id}",
            bar_id=bar_id,
            entries=entry_responses,
            total_waiting=len(waiting_entries),
            total_called=len(called_entries),
            estimated_wait_time=estimated_wait
        )
    
    except Exception as e:
        logger.error(f"Error getting queue for bar {bar_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving queue"
        )


@router.post("/bars/{bar_id}/queue", response_model=QueueJoinResponse)
async def join_queue(
    bar_id: str,
    queue_data: QueueJoinRequest,
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[Optional[User], Depends(get_optional_current_user)]
):
    """
    Join the queue for a bar.
    
    Args:
        bar_id: Bar ID
        queue_data: Queue entry data
        db: Database session
        current_user: Current user (optional)
        
    Returns:
        Queue entry information
    """
    try:
        queue_service = QueueService(db)
        
        # Create queue entry
        customer_uid = current_user.uid if current_user else None
        entry = queue_service.join_queue(bar_id, queue_data, customer_uid)
        
        entry_response = QueueEntryResponse.model_validate(entry)
        
        # Send WebSocket notification
        await manager.send_queue_join(bar_id, entry_response.model_dump())
        
        return QueueJoinResponse(
            success=True,
            message="Successfully joined the queue",
            entry=entry_response,
            estimated_wait_time=entry.estimated_wait_time or 0,
            position_in_queue=entry.get_position_in_queue()
        )
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error joining queue for bar {bar_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error joining queue"
        )


@router.put("/queue/{entry_id}/call", response_model=QueueCallResponse)
async def call_queue_entry(
    entry_id: str,
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    """
    Call a specific queue entry.
    
    Only staff members can call queue entries.
    
    Args:
        entry_id: Queue entry ID
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Updated queue entry
    """
    try:
        # Check permissions
        if current_user.role.value == "CUSTOMER":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to call queue entries"
            )
        
        queue_service = QueueService(db)
        entry = queue_service.call_entry(entry_id)
        
        if not entry:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Queue entry not found"
            )
        
        # Check bar access
        if (current_user.role.value in ["BAR_MANAGER", "COMPANY_ADMIN"] and
            current_user.company_id != entry.bar.company_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access to this bar is not allowed"
            )
        
        entry_response = QueueEntryResponse.model_validate(entry)
        
        # Send WebSocket notification
        await manager.send_queue_call(entry.bar_id, entry_response.model_dump())
        
        return QueueCallResponse(
            success=True,
            message="Queue entry called successfully",
            entry=entry_response,
            called_at=entry.called_at
        )
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error calling queue entry {entry_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error calling queue entry"
        )


@router.put("/queue/{entry_id}/complete", response_model=QueueCompleteResponse)
async def complete_queue_entry(
    entry_id: str,
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    """
    Mark a queue entry as completed.

    Only staff members can complete queue entries.

    Args:
        entry_id: Queue entry ID
        db: Database session
        current_user: Current authenticated user

    Returns:
        Completed queue entry
    """
    try:
        # Check permissions
        if current_user.role.value == "CUSTOMER":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to complete queue entries"
            )

        queue_service = QueueService(db)
        entry = queue_service.complete_entry(entry_id)

        if not entry:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Queue entry not found"
            )

        # Check bar access
        if (current_user.role.value in ["BAR_MANAGER", "COMPANY_ADMIN"] and
            current_user.company_id != entry.bar.company_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access to this bar is not allowed"
            )

        entry_response = QueueEntryResponse.model_validate(entry)

        # Send WebSocket notification
        await manager.send_queue_complete(entry.bar_id, entry_response.model_dump())

        return QueueCompleteResponse(
            success=True,
            message="Queue entry completed successfully",
            entry=entry_response,
            completed_at=entry.completed_at,
            total_time_minutes=entry.total_time_minutes or 0
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error completing queue entry {entry_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error completing queue entry"
        )


@router.delete("/queue/{entry_id}", response_model=QueueCancelResponse)
async def cancel_queue_entry(
    entry_id: str,
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    reason: Optional[str] = Query(None, description="Cancellation reason")
):
    """
    Cancel a queue entry.

    Customers can cancel their own entries, staff can cancel any entry.

    Args:
        entry_id: Queue entry ID
        db: Database session
        current_user: Current authenticated user
        reason: Optional cancellation reason

    Returns:
        Cancelled queue entry
    """
    try:
        queue_service = QueueService(db)
        entry = queue_service.get_queue_entry_by_id(entry_id)

        if not entry:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Queue entry not found"
            )

        # Check permissions
        if current_user.role.value == "CUSTOMER":
            # Customers can only cancel their own entries
            if entry.customer_uid != current_user.uid:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Can only cancel your own queue entries"
                )
        elif current_user.role.value in ["BAR_MANAGER", "COMPANY_ADMIN"]:
            # Check bar access
            if current_user.company_id != entry.bar.company_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access to this bar is not allowed"
                )

        cancelled_entry = queue_service.cancel_entry(entry_id, reason)
        entry_response = QueueEntryResponse.model_validate(cancelled_entry)

        # Send WebSocket notification
        await manager.send_queue_cancel(entry.bar_id, entry_response.model_dump())

        return QueueCancelResponse(
            success=True,
            message="Queue entry cancelled successfully",
            entry=entry_response,
            cancelled_at=cancelled_entry.cancelled_at,
            reason=reason
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling queue entry {entry_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error cancelling queue entry"
        )


@router.get("/queue/{entry_id}", response_model=QueueEntryDetailResponse)
async def get_queue_entry(
    entry_id: str,
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[Optional[User], Depends(get_optional_current_user)]
):
    """
    Get a specific queue entry.

    Args:
        entry_id: Queue entry ID
        db: Database session
        current_user: Current user (optional)

    Returns:
        Queue entry information
    """
    try:
        queue_service = QueueService(db)
        entry = queue_service.get_queue_entry_by_id(entry_id)

        if not entry:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Queue entry not found"
            )

        # Check access permissions
        if current_user:
            if current_user.role.value == "CUSTOMER":
                # Customers can only see their own entries
                if entry.customer_uid != current_user.uid:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="Access to this queue entry is not allowed"
                    )
            elif current_user.role.value in ["BAR_MANAGER", "COMPANY_ADMIN"]:
                # Check bar access
                if current_user.company_id != entry.bar.company_id:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="Access to this queue entry is not allowed"
                    )
        else:
            # Anonymous users can only see limited information
            entry.phone_number = None
            entry.customer_name = f"Customer #{entry.queue_number}"

        entry_response = QueueEntryResponse.model_validate(entry)

        return QueueEntryDetailResponse(
            success=True,
            message="Queue entry retrieved successfully",
            entry=entry_response
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting queue entry {entry_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving queue entry"
        )


@router.websocket("/bars/{bar_id}/queue/ws")
async def queue_websocket_endpoint(websocket: WebSocket, bar_id: str):
    """
    WebSocket endpoint for real-time queue updates.

    Args:
        websocket: WebSocket connection
        bar_id: Bar ID to subscribe to
    """
    # Note: In a real implementation, you might want to authenticate the WebSocket connection
    # For now, we'll accept all connections
    await websocket_handler.handle_connection(websocket, bar_id)


@router.get("/bars/{bar_id}/queue/stats", response_model=QueueStatsResponse)
async def get_queue_stats(
    bar_id: str,
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    date: Optional[str] = Query(None, description="Date in YYYY-MM-DD format")
):
    """
    Get queue statistics for a bar.

    Args:
        bar_id: Bar ID
        db: Database session
        current_user: Current authenticated user
        date: Optional date filter

    Returns:
        Queue statistics
    """
    try:
        # Check permissions
        if current_user.role.value == "CUSTOMER":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to view queue statistics"
            )

        # Check bar access
        if (current_user.role.value in ["BAR_MANAGER", "COMPANY_ADMIN"]):
            from ..services.bar import BarService
            bar_service = BarService(db)
            bar = bar_service.get_bar_by_id(bar_id)

            if not bar:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Bar not found"
                )

            if current_user.company_id != bar.company_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access to this bar is not allowed"
                )

        queue_service = QueueService(db)

        # Parse date if provided
        target_date = None
        if date:
            from datetime import datetime
            try:
                target_date = datetime.strptime(date, "%Y-%m-%d").date()
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid date format. Use YYYY-MM-DD"
                )

        stats = queue_service.get_queue_stats(bar_id, target_date)

        return QueueStatsResponse(
            success=True,
            message="Queue statistics retrieved successfully",
            **stats
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting queue stats for bar {bar_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving queue statistics"
        )
