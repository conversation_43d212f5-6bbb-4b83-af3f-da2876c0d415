{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/androidpublisher": {"description": "View and manage your Google Play Developer account"}}}}, "basePath": "", "baseUrl": "https://androidpublisher.googleapis.com/", "batchPath": "batch", "canonicalName": "Android Publisher", "description": "Lets Android application developers access their Google Play accounts. At a high level, the expected workflow is to \"insert\" an Edit, make changes as necessary, and then \"commit\" it. ", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/android-publisher", "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "androidpublisher:v3", "kind": "discovery#restDescription", "mtlsRootUrl": "https://androidpublisher.mtls.googleapis.com/", "name": "androidpublisher", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"applications": {"methods": {"dataSafety": {"description": "Writes the Safety Labels declaration of an app.", "flatPath": "androidpublisher/v3/applications/{packageName}/dataSafety", "httpMethod": "POST", "id": "androidpublisher.applications.dataSafety", "parameterOrder": ["packageName"], "parameters": {"packageName": {"description": "Required. Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/dataSafety", "request": {"$ref": "SafetyLabelsUpdateRequest"}, "response": {"$ref": "SafetyLabelsUpdateResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}}, "resources": {"deviceTierConfigs": {"methods": {"create": {"description": "Creates a new device tier config for an app.", "flatPath": "androidpublisher/v3/applications/{packageName}/deviceTierConfigs", "httpMethod": "POST", "id": "androidpublisher.applications.deviceTierConfigs.create", "parameterOrder": ["packageName"], "parameters": {"allowUnknownDevices": {"description": "Whether the service should accept device IDs that are unknown to Play's device catalog.", "location": "query", "type": "boolean"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/deviceTierConfigs", "request": {"$ref": "DeviceTierConfig"}, "response": {"$ref": "DeviceTierConfig"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "get": {"description": "Returns a particular device tier config.", "flatPath": "androidpublisher/v3/applications/{packageName}/deviceTierConfigs/{deviceTierConfigId}", "httpMethod": "GET", "id": "androidpublisher.applications.deviceTierConfigs.get", "parameterOrder": ["packageName", "deviceTierConfigId"], "parameters": {"deviceTierConfigId": {"description": "Required. Id of an existing device tier config.", "format": "int64", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/deviceTierConfigs/{deviceTierConfigId}", "response": {"$ref": "DeviceTierConfig"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "list": {"description": "Returns created device tier configs, ordered by descending creation time.", "flatPath": "androidpublisher/v3/applications/{packageName}/deviceTierConfigs", "httpMethod": "GET", "id": "androidpublisher.applications.deviceTierConfigs.list", "parameterOrder": ["packageName"], "parameters": {"packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of device tier configs to return. The service may return fewer than this value. If unspecified, at most 10 device tier configs will be returned. The maximum value for this field is 100; values above 100 will be coerced to 100. Device tier configs will be ordered by descending creation time.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListDeviceTierConfigs` call. Provide this to retrieve the subsequent page.", "location": "query", "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/deviceTierConfigs", "response": {"$ref": "ListDeviceTierConfigsResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}}}}}, "apprecovery": {"methods": {"addTargeting": {"description": "Incrementally update targeting for a recovery action. Note that only the criteria selected during the creation of recovery action can be expanded.", "flatPath": "androidpublisher/v3/applications/{packageName}/appRecoveries/{appRecoveryId}:addTargeting", "httpMethod": "POST", "id": "androidpublisher.apprecovery.addTargeting", "parameterOrder": ["packageName", "appRecoveryId"], "parameters": {"appRecoveryId": {"description": "Required. ID corresponding to the app recovery action.", "format": "int64", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Required. Package name of the app for which recovery action is to be updated.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/appRecoveries/{appRecoveryId}:addTargeting", "request": {"$ref": "AddTargetingRequest"}, "response": {"$ref": "AddTargetingResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "cancel": {"description": "Cancel an already executing app recovery action. Note that this action changes status of the recovery action to CANCELED.", "flatPath": "androidpublisher/v3/applications/{packageName}/appRecoveries/{appRecoveryId}:cancel", "httpMethod": "POST", "id": "androidpublisher.apprecovery.cancel", "parameterOrder": ["packageName", "appRecoveryId"], "parameters": {"appRecoveryId": {"description": "Required. ID corresponding to the app recovery action.", "format": "int64", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Required. Package name of the app for which recovery action cancellation is requested.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/appRecoveries/{appRecoveryId}:cancel", "request": {"$ref": "CancelAppRecoveryRequest"}, "response": {"$ref": "CancelAppRecoveryResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "create": {"description": "Create an app recovery action with recovery status as DRAFT. Note that this action does not execute the recovery action.", "flatPath": "androidpublisher/v3/applications/{packageName}/appRecoveries", "httpMethod": "POST", "id": "androidpublisher.apprecovery.create", "parameterOrder": ["packageName"], "parameters": {"packageName": {"description": "Required. Package name of the app on which recovery action is performed.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/appRecoveries", "request": {"$ref": "CreateDraftAppRecoveryRequest"}, "response": {"$ref": "AppRecoveryAction"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "deploy": {"description": "Deploy an already created app recovery action with recovery status DRAFT. Note that this action activates the recovery action for all targeted users and changes its status to ACTIVE.", "flatPath": "androidpublisher/v3/applications/{packageName}/appRecoveries/{appRecoveryId}:deploy", "httpMethod": "POST", "id": "androidpublisher.apprecovery.deploy", "parameterOrder": ["packageName", "appRecoveryId"], "parameters": {"appRecoveryId": {"description": "Required. ID corresponding to the app recovery action to deploy.", "format": "int64", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Required. Package name of the app for which recovery action is deployed.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/appRecoveries/{appRecoveryId}:deploy", "request": {"$ref": "DeployAppRecoveryRequest"}, "response": {"$ref": "DeployAppRecoveryResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "list": {"description": "List all app recovery action resources associated with a particular package name and app version.", "flatPath": "androidpublisher/v3/applications/{packageName}/appRecoveries", "httpMethod": "GET", "id": "androidpublisher.apprecovery.list", "parameterOrder": ["packageName"], "parameters": {"packageName": {"description": "Required. Package name of the app for which list of recovery actions is requested.", "location": "path", "required": true, "type": "string"}, "versionCode": {"description": "Required. Version code targeted by the list of recovery actions.", "format": "int64", "location": "query", "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/appRecoveries", "response": {"$ref": "ListAppRecoveriesResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}}}, "edits": {"methods": {"commit": {"description": "Commits an app edit.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}:commit", "httpMethod": "POST", "id": "androidpublisher.edits.commit", "parameterOrder": ["packageName", "editId"], "parameters": {"changesNotSentForReview": {"description": "When a rejection happens, the parameter will make sure that the changes in this edit won't be reviewed until they are explicitly sent for review from within the Google Play Console UI. These changes will be added to any other changes that are not yet sent for review.", "location": "query", "type": "boolean"}, "editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}:commit", "response": {"$ref": "AppEdit"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "delete": {"description": "Deletes an app edit.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}", "httpMethod": "DELETE", "id": "androidpublisher.edits.delete", "parameterOrder": ["packageName", "editId"], "parameters": {"editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}", "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "get": {"description": "Gets an app edit.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}", "httpMethod": "GET", "id": "androidpublisher.edits.get", "parameterOrder": ["packageName", "editId"], "parameters": {"editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}", "response": {"$ref": "AppEdit"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "insert": {"description": "Creates a new edit for an app.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits", "httpMethod": "POST", "id": "androidpublisher.edits.insert", "parameterOrder": ["packageName"], "parameters": {"packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits", "request": {"$ref": "AppEdit"}, "response": {"$ref": "AppEdit"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "validate": {"description": "Validates an app edit.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}:validate", "httpMethod": "POST", "id": "androidpublisher.edits.validate", "parameterOrder": ["packageName", "editId"], "parameters": {"editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}:validate", "response": {"$ref": "AppEdit"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}}, "resources": {"apks": {"methods": {"addexternallyhosted": {"description": "Creates a new APK without uploading the APK itself to Google Play, instead hosting the APK at a specified URL. This function is only available to organizations using Managed Play whose application is configured to restrict distribution to the organizations.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/apks/externallyHosted", "httpMethod": "POST", "id": "androidpublisher.edits.apks.addexternallyhosted", "parameterOrder": ["packageName", "editId"], "parameters": {"editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/apks/externallyHosted", "request": {"$ref": "ApksAddExternallyHostedRequest"}, "response": {"$ref": "ApksAddExternallyHostedResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "list": {"description": "Lists all current APKs of the app and edit.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/apks", "httpMethod": "GET", "id": "androidpublisher.edits.apks.list", "parameterOrder": ["packageName", "editId"], "parameters": {"editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/apks", "response": {"$ref": "ApksListResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "upload": {"description": "Uploads an APK and adds to the current edit.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/apks", "httpMethod": "POST", "id": "androidpublisher.edits.apks.upload", "mediaUpload": {"accept": ["application/octet-stream", "application/vnd.android.package-archive"], "maxSize": "10737418240", "protocols": {"resumable": {"multipart": true, "path": "/resumable/upload/androidpublisher/v3/applications/{packageName}/edits/{editId}/apks"}, "simple": {"multipart": true, "path": "/upload/androidpublisher/v3/applications/{packageName}/edits/{editId}/apks"}}}, "parameterOrder": ["packageName", "editId"], "parameters": {"editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/apks", "response": {"$ref": "Apk"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"], "supportsMediaUpload": true}}}, "bundles": {"methods": {"list": {"description": "Lists all current Android App Bundles of the app and edit.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/bundles", "httpMethod": "GET", "id": "androidpublisher.edits.bundles.list", "parameterOrder": ["packageName", "editId"], "parameters": {"editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/bundles", "response": {"$ref": "BundlesListResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "upload": {"description": "Uploads a new Android App Bundle to this edit. If you are using the Google API client libraries, please increase the timeout of the http request before calling this endpoint (a timeout of 2 minutes is recommended). See [Timeouts and Errors](https://developers.google.com/api-client-library/java/google-api-java-client/errors) for an example in java.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/bundles", "httpMethod": "POST", "id": "androidpublisher.edits.bundles.upload", "mediaUpload": {"accept": ["application/octet-stream"], "maxSize": "53687091200", "protocols": {"resumable": {"multipart": true, "path": "/resumable/upload/androidpublisher/v3/applications/{packageName}/edits/{editId}/bundles"}, "simple": {"multipart": true, "path": "/upload/androidpublisher/v3/applications/{packageName}/edits/{editId}/bundles"}}}, "parameterOrder": ["packageName", "editId"], "parameters": {"ackBundleInstallationWarning": {"deprecated": true, "description": "Deprecated. The installation warning has been removed, it's not necessary to set this field anymore.", "location": "query", "type": "boolean"}, "deviceTierConfigId": {"description": "Device tier config (DTC) to be used for generating deliverables (APKs). Contains id of the DTC or \"LATEST\" for last uploaded DTC.", "location": "query", "type": "string"}, "editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/bundles", "response": {"$ref": "Bundle"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"], "supportsMediaUpload": true}}}, "countryavailability": {"methods": {"get": {"description": "Gets country availability.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/countryAvailability/{track}", "httpMethod": "GET", "id": "androidpublisher.edits.countryavailability.get", "parameterOrder": ["packageName", "editId", "track"], "parameters": {"editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}, "track": {"description": "The track to read from.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/countryAvailability/{track}", "response": {"$ref": "TrackCountryAvailability"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}}}, "deobfuscationfiles": {"methods": {"upload": {"description": "Uploads a new deobfuscation file and attaches to the specified APK.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/apks/{apkVersionCode}/deobfuscationFiles/{deobfuscationFileType}", "httpMethod": "POST", "id": "androidpublisher.edits.deobfuscationfiles.upload", "mediaUpload": {"accept": ["application/octet-stream"], "maxSize": "1677721600", "protocols": {"resumable": {"multipart": true, "path": "/resumable/upload/androidpublisher/v3/applications/{packageName}/edits/{editId}/apks/{apkVersionCode}/deobfuscationFiles/{deobfuscationFileType}"}, "simple": {"multipart": true, "path": "/upload/androidpublisher/v3/applications/{packageName}/edits/{editId}/apks/{apkVersionCode}/deobfuscationFiles/{deobfuscationFileType}"}}}, "parameterOrder": ["packageName", "editId", "apkVersionCode", "deobfuscationFileType"], "parameters": {"apkVersionCode": {"description": "The version code of the APK whose Deobfuscation File is being uploaded.", "format": "int32", "location": "path", "required": true, "type": "integer"}, "deobfuscationFileType": {"description": "The type of the deobfuscation file.", "enum": ["deobfuscationFileTypeUnspecified", "proguard", "nativeCode"], "enumDescriptions": ["Unspecified deobfuscation file type.", "Proguard deobfuscation file type.", "Native debugging symbols file type."], "location": "path", "required": true, "type": "string"}, "editId": {"description": "Unique identifier for this edit.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Unique identifier for the Android app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/apks/{apkVersionCode}/deobfuscationFiles/{deobfuscationFileType}", "response": {"$ref": "DeobfuscationFilesUploadResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"], "supportsMediaUpload": true}}}, "details": {"methods": {"get": {"description": "Gets details of an app.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/details", "httpMethod": "GET", "id": "androidpublisher.edits.details.get", "parameterOrder": ["packageName", "editId"], "parameters": {"editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/details", "response": {"$ref": "AppDetails"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "patch": {"description": "Patches details of an app.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/details", "httpMethod": "PATCH", "id": "androidpublisher.edits.details.patch", "parameterOrder": ["packageName", "editId"], "parameters": {"editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/details", "request": {"$ref": "AppDetails"}, "response": {"$ref": "AppDetails"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "update": {"description": "Updates details of an app.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/details", "httpMethod": "PUT", "id": "androidpublisher.edits.details.update", "parameterOrder": ["packageName", "editId"], "parameters": {"editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/details", "request": {"$ref": "AppDetails"}, "response": {"$ref": "AppDetails"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}}}, "expansionfiles": {"methods": {"get": {"description": "Fetches the expansion file configuration for the specified APK.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/apks/{apkVersionCode}/expansionFiles/{expansionFileType}", "httpMethod": "GET", "id": "androidpublisher.edits.expansionfiles.get", "parameterOrder": ["packageName", "editId", "apkVersionCode", "expansionFileType"], "parameters": {"apkVersionCode": {"description": "The version code of the APK whose expansion file configuration is being read or modified.", "format": "int32", "location": "path", "required": true, "type": "integer"}, "editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "expansionFileType": {"description": "The file type of the file configuration which is being read or modified.", "enum": ["expansionFileTypeUnspecified", "main", "patch"], "enumDescriptions": ["Unspecified expansion file type.", "Main expansion file.", "Patch expansion file."], "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/apks/{apkVersionCode}/expansionFiles/{expansionFileType}", "response": {"$ref": "ExpansionFile"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "patch": {"description": "Patches the APK's expansion file configuration to reference another APK's expansion file. To add a new expansion file use the Upload method.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/apks/{apkVersionCode}/expansionFiles/{expansionFileType}", "httpMethod": "PATCH", "id": "androidpublisher.edits.expansionfiles.patch", "parameterOrder": ["packageName", "editId", "apkVersionCode", "expansionFileType"], "parameters": {"apkVersionCode": {"description": "The version code of the APK whose expansion file configuration is being read or modified.", "format": "int32", "location": "path", "required": true, "type": "integer"}, "editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "expansionFileType": {"description": "The file type of the expansion file configuration which is being updated.", "enum": ["expansionFileTypeUnspecified", "main", "patch"], "enumDescriptions": ["Unspecified expansion file type.", "Main expansion file.", "Patch expansion file."], "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/apks/{apkVersionCode}/expansionFiles/{expansionFileType}", "request": {"$ref": "ExpansionFile"}, "response": {"$ref": "ExpansionFile"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "update": {"description": "Updates the APK's expansion file configuration to reference another APK's expansion file. To add a new expansion file use the Upload method.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/apks/{apkVersionCode}/expansionFiles/{expansionFileType}", "httpMethod": "PUT", "id": "androidpublisher.edits.expansionfiles.update", "parameterOrder": ["packageName", "editId", "apkVersionCode", "expansionFileType"], "parameters": {"apkVersionCode": {"description": "The version code of the APK whose expansion file configuration is being read or modified.", "format": "int32", "location": "path", "required": true, "type": "integer"}, "editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "expansionFileType": {"description": "The file type of the file configuration which is being read or modified.", "enum": ["expansionFileTypeUnspecified", "main", "patch"], "enumDescriptions": ["Unspecified expansion file type.", "Main expansion file.", "Patch expansion file."], "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/apks/{apkVersionCode}/expansionFiles/{expansionFileType}", "request": {"$ref": "ExpansionFile"}, "response": {"$ref": "ExpansionFile"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "upload": {"description": "Uploads a new expansion file and attaches to the specified APK.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/apks/{apkVersionCode}/expansionFiles/{expansionFileType}", "httpMethod": "POST", "id": "androidpublisher.edits.expansionfiles.upload", "mediaUpload": {"accept": ["application/octet-stream"], "maxSize": "2147483648", "protocols": {"resumable": {"multipart": true, "path": "/resumable/upload/androidpublisher/v3/applications/{packageName}/edits/{editId}/apks/{apkVersionCode}/expansionFiles/{expansionFileType}"}, "simple": {"multipart": true, "path": "/upload/androidpublisher/v3/applications/{packageName}/edits/{editId}/apks/{apkVersionCode}/expansionFiles/{expansionFileType}"}}}, "parameterOrder": ["packageName", "editId", "apkVersionCode", "expansionFileType"], "parameters": {"apkVersionCode": {"description": "The version code of the APK whose expansion file configuration is being read or modified.", "format": "int32", "location": "path", "required": true, "type": "integer"}, "editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "expansionFileType": {"description": "The file type of the expansion file configuration which is being updated.", "enum": ["expansionFileTypeUnspecified", "main", "patch"], "enumDescriptions": ["Unspecified expansion file type.", "Main expansion file.", "Patch expansion file."], "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/apks/{apkVersionCode}/expansionFiles/{expansionFileType}", "response": {"$ref": "ExpansionFilesUploadResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"], "supportsMediaUpload": true}}}, "images": {"methods": {"delete": {"description": "Deletes the image (specified by id) from the edit.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/listings/{language}/{imageType}/{imageId}", "httpMethod": "DELETE", "id": "androidpublisher.edits.images.delete", "parameterOrder": ["packageName", "editId", "language", "imageType", "imageId"], "parameters": {"editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "imageId": {"description": "Unique identifier an image within the set of images attached to this edit.", "location": "path", "required": true, "type": "string"}, "imageType": {"description": "Type of the Image.", "enum": ["appImageTypeUnspecified", "phoneScreenshots", "sevenInchScreenshots", "tenInchScreenshots", "tvScreenshots", "wearScreenshots", "icon", "featureGraphic", "tvBanner"], "enumDescriptions": ["Unspecified type. Do not use.", "Phone screenshot.", "Seven inch screenshot.", "Ten inch screenshot.", "TV screenshot.", "Wear screenshot.", "Icon.", "Feature graphic.", "TV banner."], "location": "path", "required": true, "type": "string"}, "language": {"description": "Language localization code (a BCP-47 language tag; for example, \"de-AT\" for Austrian German).", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/listings/{language}/{imageType}/{imageId}", "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "deleteall": {"description": "Deletes all images for the specified language and image type. Returns an empty response if no images are found.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/listings/{language}/{imageType}", "httpMethod": "DELETE", "id": "androidpublisher.edits.images.deleteall", "parameterOrder": ["packageName", "editId", "language", "imageType"], "parameters": {"editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "imageType": {"description": "Type of the Image. Providing an image type that refers to no images is a no-op.", "enum": ["appImageTypeUnspecified", "phoneScreenshots", "sevenInchScreenshots", "tenInchScreenshots", "tvScreenshots", "wearScreenshots", "icon", "featureGraphic", "tvBanner"], "enumDescriptions": ["Unspecified type. Do not use.", "Phone screenshot.", "Seven inch screenshot.", "Ten inch screenshot.", "TV screenshot.", "Wear screenshot.", "Icon.", "Feature graphic.", "TV banner."], "location": "path", "required": true, "type": "string"}, "language": {"description": "Language localization code (a BCP-47 language tag; for example, \"de-AT\" for Austrian German). Providing a language that is not supported by the App is a no-op.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/listings/{language}/{imageType}", "response": {"$ref": "ImagesDeleteAllResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "list": {"description": "Lists all images. The response may be empty.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/listings/{language}/{imageType}", "httpMethod": "GET", "id": "androidpublisher.edits.images.list", "parameterOrder": ["packageName", "editId", "language", "imageType"], "parameters": {"editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "imageType": {"description": "Type of the Image. Providing an image type that refers to no images will return an empty response.", "enum": ["appImageTypeUnspecified", "phoneScreenshots", "sevenInchScreenshots", "tenInchScreenshots", "tvScreenshots", "wearScreenshots", "icon", "featureGraphic", "tvBanner"], "enumDescriptions": ["Unspecified type. Do not use.", "Phone screenshot.", "Seven inch screenshot.", "Ten inch screenshot.", "TV screenshot.", "Wear screenshot.", "Icon.", "Feature graphic.", "TV banner."], "location": "path", "required": true, "type": "string"}, "language": {"description": "Language localization code (a BCP-47 language tag; for example, \"de-AT\" for Austrian German). There must be a store listing for the specified language.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/listings/{language}/{imageType}", "response": {"$ref": "ImagesListResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "upload": {"description": "Uploads an image of the specified language and image type, and adds to the edit.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/listings/{language}/{imageType}", "httpMethod": "POST", "id": "androidpublisher.edits.images.upload", "mediaUpload": {"accept": ["image/*"], "maxSize": "15728640", "protocols": {"resumable": {"multipart": true, "path": "/resumable/upload/androidpublisher/v3/applications/{packageName}/edits/{editId}/listings/{language}/{imageType}"}, "simple": {"multipart": true, "path": "/upload/androidpublisher/v3/applications/{packageName}/edits/{editId}/listings/{language}/{imageType}"}}}, "parameterOrder": ["packageName", "editId", "language", "imageType"], "parameters": {"editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "imageType": {"description": "Type of the Image.", "enum": ["appImageTypeUnspecified", "phoneScreenshots", "sevenInchScreenshots", "tenInchScreenshots", "tvScreenshots", "wearScreenshots", "icon", "featureGraphic", "tvBanner"], "enumDescriptions": ["Unspecified type. Do not use.", "Phone screenshot.", "Seven inch screenshot.", "Ten inch screenshot.", "TV screenshot.", "Wear screenshot.", "Icon.", "Feature graphic.", "TV banner."], "location": "path", "required": true, "type": "string"}, "language": {"description": "Language localization code (a BCP-47 language tag; for example, \"de-AT\" for Austrian German). Providing a language that is not supported by the App is a no-op.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/listings/{language}/{imageType}", "response": {"$ref": "ImagesUploadResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"], "supportsMediaUpload": true}}}, "listings": {"methods": {"delete": {"description": "Deletes a localized store listing.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/listings/{language}", "httpMethod": "DELETE", "id": "androidpublisher.edits.listings.delete", "parameterOrder": ["packageName", "editId", "language"], "parameters": {"editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "language": {"description": "Language localization code (a BCP-47 language tag; for example, \"de-AT\" for Austrian German).", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/listings/{language}", "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "deleteall": {"description": "Deletes all store listings.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/listings", "httpMethod": "DELETE", "id": "androidpublisher.edits.listings.deleteall", "parameterOrder": ["packageName", "editId"], "parameters": {"editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/listings", "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "get": {"description": "Gets a localized store listing.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/listings/{language}", "httpMethod": "GET", "id": "androidpublisher.edits.listings.get", "parameterOrder": ["packageName", "editId", "language"], "parameters": {"editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "language": {"description": "Language localization code (a BCP-47 language tag; for example, \"de-AT\" for Austrian German).", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/listings/{language}", "response": {"$ref": "Listing"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "list": {"description": "Lists all localized store listings.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/listings", "httpMethod": "GET", "id": "androidpublisher.edits.listings.list", "parameterOrder": ["packageName", "editId"], "parameters": {"editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/listings", "response": {"$ref": "ListingsListResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "patch": {"description": "Patches a localized store listing.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/listings/{language}", "httpMethod": "PATCH", "id": "androidpublisher.edits.listings.patch", "parameterOrder": ["packageName", "editId", "language"], "parameters": {"editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "language": {"description": "Language localization code (a BCP-47 language tag; for example, \"de-AT\" for Austrian German).", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/listings/{language}", "request": {"$ref": "Listing"}, "response": {"$ref": "Listing"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "update": {"description": "Creates or updates a localized store listing.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/listings/{language}", "httpMethod": "PUT", "id": "androidpublisher.edits.listings.update", "parameterOrder": ["packageName", "editId", "language"], "parameters": {"editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "language": {"description": "Language localization code (a BCP-47 language tag; for example, \"de-AT\" for Austrian German).", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/listings/{language}", "request": {"$ref": "Listing"}, "response": {"$ref": "Listing"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}}}, "testers": {"methods": {"get": {"description": "Gets testers. Note: Testers resource does not support email lists.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/testers/{track}", "httpMethod": "GET", "id": "androidpublisher.edits.testers.get", "parameterOrder": ["packageName", "editId", "track"], "parameters": {"editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}, "track": {"description": "The track to read from.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/testers/{track}", "response": {"$ref": "Testers"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "patch": {"description": "Patches testers. Note: Testers resource does not support email lists.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/testers/{track}", "httpMethod": "PATCH", "id": "androidpublisher.edits.testers.patch", "parameterOrder": ["packageName", "editId", "track"], "parameters": {"editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}, "track": {"description": "The track to update.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/testers/{track}", "request": {"$ref": "Testers"}, "response": {"$ref": "Testers"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "update": {"description": "Updates testers. Note: Testers resource does not support email lists.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/testers/{track}", "httpMethod": "PUT", "id": "androidpublisher.edits.testers.update", "parameterOrder": ["packageName", "editId", "track"], "parameters": {"editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}, "track": {"description": "The track to update.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/testers/{track}", "request": {"$ref": "Testers"}, "response": {"$ref": "Testers"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}}}, "tracks": {"methods": {"create": {"description": "Creates a new track.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/tracks", "httpMethod": "POST", "id": "androidpublisher.edits.tracks.create", "parameterOrder": ["packageName", "editId"], "parameters": {"editId": {"description": "Required. Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Required. Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/tracks", "request": {"$ref": "TrackConfig"}, "response": {"$ref": "Track"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "get": {"description": "Gets a track.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/tracks/{track}", "httpMethod": "GET", "id": "androidpublisher.edits.tracks.get", "parameterOrder": ["packageName", "editId", "track"], "parameters": {"editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}, "track": {"description": "Identifier of the track. [More on track name](https://developers.google.com/android-publisher/tracks#ff-track-name)", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/tracks/{track}", "response": {"$ref": "Track"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "list": {"description": "Lists all tracks.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/tracks", "httpMethod": "GET", "id": "androidpublisher.edits.tracks.list", "parameterOrder": ["packageName", "editId"], "parameters": {"editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/tracks", "response": {"$ref": "TracksListResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "patch": {"description": "Patches a track.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/tracks/{track}", "httpMethod": "PATCH", "id": "androidpublisher.edits.tracks.patch", "parameterOrder": ["packageName", "editId", "track"], "parameters": {"editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}, "track": {"description": "Identifier of the track. [More on track name](https://developers.google.com/android-publisher/tracks#ff-track-name)", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/tracks/{track}", "request": {"$ref": "Track"}, "response": {"$ref": "Track"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "update": {"description": "Updates a track.", "flatPath": "androidpublisher/v3/applications/{packageName}/edits/{editId}/tracks/{track}", "httpMethod": "PUT", "id": "androidpublisher.edits.tracks.update", "parameterOrder": ["packageName", "editId", "track"], "parameters": {"editId": {"description": "Identifier of the edit.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}, "track": {"description": "Identifier of the track. [More on track name](https://developers.google.com/android-publisher/tracks#ff-track-name)", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/edits/{editId}/tracks/{track}", "request": {"$ref": "Track"}, "response": {"$ref": "Track"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}}}}}, "externaltransactions": {"methods": {"createexternaltransaction": {"description": "Creates a new external transaction.", "flatPath": "androidpublisher/v3/applications/{applicationsId}/externalTransactions", "httpMethod": "POST", "id": "androidpublisher.externaltransactions.createexternaltransaction", "parameterOrder": ["parent"], "parameters": {"externalTransactionId": {"description": "Required. The id to use for the external transaction. Must be unique across all other transactions for the app. This value should be 1-63 characters and valid characters are /a-zA-Z0-9_-/. Do not use this field to store any Personally Identifiable Information (PII) such as emails. Attempting to store PII in this field may result in requests being blocked.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource where this external transaction will be created. Format: applications/{package_name}", "location": "path", "pattern": "^applications/[^/]+$", "required": true, "type": "string"}}, "path": "androidpublisher/v3/{+parent}/externalTransactions", "request": {"$ref": "ExternalTransaction"}, "response": {"$ref": "ExternalTransaction"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "getexternaltransaction": {"description": "Gets an existing external transaction.", "flatPath": "androidpublisher/v3/applications/{applicationsId}/externalTransactions/{externalTransactionsId}", "httpMethod": "GET", "id": "androidpublisher.externaltransactions.getexternaltransaction", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the external transaction to retrieve. Format: applications/{package_name}/externalTransactions/{external_transaction}", "location": "path", "pattern": "^applications/[^/]+/externalTransactions/[^/]+$", "required": true, "type": "string"}}, "path": "androidpublisher/v3/{+name}", "response": {"$ref": "ExternalTransaction"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "refundexternaltransaction": {"description": "Refunds or partially refunds an existing external transaction.", "flatPath": "androidpublisher/v3/applications/{applicationsId}/externalTransactions/{externalTransactionsId}:refund", "httpMethod": "POST", "id": "androidpublisher.externaltransactions.refundexternaltransaction", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the external transaction that will be refunded. Format: applications/{package_name}/externalTransactions/{external_transaction}", "location": "path", "pattern": "^applications/[^/]+/externalTransactions/[^/]+$", "required": true, "type": "string"}}, "path": "androidpublisher/v3/{+name}:refund", "request": {"$ref": "RefundExternalTransactionRequest"}, "response": {"$ref": "ExternalTransaction"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}}}, "generatedapks": {"methods": {"download": {"description": "Downloads a single signed APK generated from an app bundle.", "flatPath": "androidpublisher/v3/applications/{packageName}/generatedApks/{versionCode}/downloads/{downloadId}:download", "httpMethod": "GET", "id": "androidpublisher.generatedapks.download", "parameterOrder": ["packageName", "versionCode", "downloadId"], "parameters": {"downloadId": {"description": "Download ID, which uniquely identifies the APK to download. Can be obtained from the response of `generatedapks.list` method.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}, "versionCode": {"description": "Version code of the app bundle.", "format": "int32", "location": "path", "required": true, "type": "integer"}}, "path": "androidpublisher/v3/applications/{packageName}/generatedApks/{versionCode}/downloads/{downloadId}:download", "scopes": ["https://www.googleapis.com/auth/androidpublisher"], "supportsMediaDownload": true, "useMediaDownloadService": true}, "list": {"description": "Returns download metadata for all APKs that were generated from a given app bundle.", "flatPath": "androidpublisher/v3/applications/{packageName}/generatedApks/{versionCode}", "httpMethod": "GET", "id": "androidpublisher.generatedapks.list", "parameterOrder": ["packageName", "versionCode"], "parameters": {"packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}, "versionCode": {"description": "Version code of the app bundle.", "format": "int32", "location": "path", "required": true, "type": "integer"}}, "path": "androidpublisher/v3/applications/{packageName}/generatedApks/{versionCode}", "response": {"$ref": "GeneratedApksListResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}}}, "grants": {"methods": {"create": {"description": "Grant access for a user to the given package.", "flatPath": "androidpublisher/v3/developers/{developersId}/users/{usersId}/grants", "httpMethod": "POST", "id": "androidpublisher.grants.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The user which needs permission. Format: developers/{developer}/users/{user}", "location": "path", "pattern": "^developers/[^/]+/users/[^/]+$", "required": true, "type": "string"}}, "path": "androidpublisher/v3/{+parent}/grants", "request": {"$ref": "<PERSON>"}, "response": {"$ref": "<PERSON>"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "delete": {"description": "Removes all access for the user to the given package or developer account.", "flatPath": "androidpublisher/v3/developers/{developersId}/users/{usersId}/grants/{grantsId}", "httpMethod": "DELETE", "id": "androidpublisher.grants.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the grant to delete. Format: developers/{developer}/users/{email}/grants/{package_name}", "location": "path", "pattern": "^developers/[^/]+/users/[^/]+/grants/[^/]+$", "required": true, "type": "string"}}, "path": "androidpublisher/v3/{+name}", "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "patch": {"description": "Updates access for the user to the given package.", "flatPath": "androidpublisher/v3/developers/{developersId}/users/{usersId}/grants/{grantsId}", "httpMethod": "PATCH", "id": "androidpublisher.grants.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name for this grant, following the pattern \"developers/{developer}/users/{email}/grants/{package_name}\". If this grant is for a draft app, the app ID will be used in this resource name instead of the package name.", "location": "path", "pattern": "^developers/[^/]+/users/[^/]+/grants/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "androidpublisher/v3/{+name}", "request": {"$ref": "<PERSON>"}, "response": {"$ref": "<PERSON>"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}}}, "inappproducts": {"methods": {"batchDelete": {"description": "Deletes in-app products (managed products or subscriptions). Set the latencyTolerance field on nested requests to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT to achieve maximum update throughput. This method should not be used to delete subscriptions. See [this article](https://android-developers.googleblog.com/2023/06/changes-to-google-play-developer-api-june-2023.html) for more information.", "flatPath": "androidpublisher/v3/applications/{packageName}/inappproducts:batchDelete", "httpMethod": "POST", "id": "androidpublisher.inappproducts.batchDelete", "parameterOrder": ["packageName"], "parameters": {"packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/inappproducts:batchDelete", "request": {"$ref": "InappproductsBatchDeleteRequest"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "batchGet": {"description": "Reads multiple in-app products, which can be managed products or subscriptions. This method should not be used to retrieve subscriptions. See [this article](https://android-developers.googleblog.com/2023/06/changes-to-google-play-developer-api-june-2023.html) for more information.", "flatPath": "androidpublisher/v3/applications/{packageName}/inappproducts:batchGet", "httpMethod": "GET", "id": "androidpublisher.inappproducts.batchGet", "parameterOrder": ["packageName"], "parameters": {"packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}, "sku": {"description": "Unique identifier for the in-app products.", "location": "query", "repeated": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/inappproducts:batchGet", "response": {"$ref": "InappproductsBatchGetResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "batchUpdate": {"description": "Updates or inserts one or more in-app products (managed products or subscriptions). Set the latencyTolerance field on nested requests to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT to achieve maximum update throughput. This method should no longer be used to update subscriptions. See [this article](https://android-developers.googleblog.com/2023/06/changes-to-google-play-developer-api-june-2023.html) for more information.", "flatPath": "androidpublisher/v3/applications/{packageName}/inappproducts:batchUpdate", "httpMethod": "POST", "id": "androidpublisher.inappproducts.batchUpdate", "parameterOrder": ["packageName"], "parameters": {"packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/inappproducts:batchUpdate", "request": {"$ref": "InappproductsBatchUpdateRequest"}, "response": {"$ref": "InappproductsBatchUpdateResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "delete": {"description": "Deletes an in-app product (a managed product or a subscription). This method should no longer be used to delete subscriptions. See [this article](https://android-developers.googleblog.com/2023/06/changes-to-google-play-developer-api-june-2023.html) for more information.", "flatPath": "androidpublisher/v3/applications/{packageName}/inappproducts/{sku}", "httpMethod": "DELETE", "id": "androidpublisher.inappproducts.delete", "parameterOrder": ["packageName", "sku"], "parameters": {"latencyTolerance": {"description": "Optional. The latency tolerance for the propagation of this product update. Defaults to latency-sensitive.", "enum": ["PRODUCT_UPDATE_LATENCY_TOLERANCE_UNSPECIFIED", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT"], "enumDescriptions": ["Defaults to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE.", "The update will propagate to clients within several minutes on average and up to a few hours in rare cases. Throughput is limited to 7,200 updates per app per hour.", "The update will propagate to clients within 24 hours. Supports high throughput of up to 720,000 updates per app per hour using batch modification methods."], "location": "query", "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}, "sku": {"description": "Unique identifier for the in-app product.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/inappproducts/{sku}", "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "get": {"description": "Gets an in-app product, which can be a managed product or a subscription. This method should no longer be used to retrieve subscriptions. See [this article](https://android-developers.googleblog.com/2023/06/changes-to-google-play-developer-api-june-2023.html) for more information.", "flatPath": "androidpublisher/v3/applications/{packageName}/inappproducts/{sku}", "httpMethod": "GET", "id": "androidpublisher.inappproducts.get", "parameterOrder": ["packageName", "sku"], "parameters": {"packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}, "sku": {"description": "Unique identifier for the in-app product.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/inappproducts/{sku}", "response": {"$ref": "InAppProduct"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "insert": {"description": "Creates an in-app product (a managed product or a subscription). This method should no longer be used to create subscriptions. See [this article](https://android-developers.googleblog.com/2023/06/changes-to-google-play-developer-api-june-2023.html) for more information.", "flatPath": "androidpublisher/v3/applications/{packageName}/inappproducts", "httpMethod": "POST", "id": "androidpublisher.inappproducts.insert", "parameterOrder": ["packageName"], "parameters": {"autoConvertMissingPrices": {"description": "If true the prices for all regions targeted by the parent app that don't have a price specified for this in-app product will be auto converted to the target currency based on the default price. Defaults to false.", "location": "query", "type": "boolean"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/inappproducts", "request": {"$ref": "InAppProduct"}, "response": {"$ref": "InAppProduct"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "list": {"description": "Lists all in-app products - both managed products and subscriptions. If an app has a large number of in-app products, the response may be paginated. In this case the response field `tokenPagination.nextPageToken` will be set and the caller should provide its value as a `token` request parameter to retrieve the next page. This method should no longer be used to retrieve subscriptions. See [this article](https://android-developers.googleblog.com/2023/06/changes-to-google-play-developer-api-june-2023.html) for more information.", "flatPath": "androidpublisher/v3/applications/{packageName}/inappproducts", "httpMethod": "GET", "id": "androidpublisher.inappproducts.list", "parameterOrder": ["packageName"], "parameters": {"maxResults": {"deprecated": true, "description": "Deprecated and ignored. The page size is determined by the server.", "format": "uint32", "location": "query", "type": "integer"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}, "startIndex": {"deprecated": true, "description": "Deprecated and ignored. Set the `token` parameter to retrieve the next page.", "format": "uint32", "location": "query", "type": "integer"}, "token": {"description": "Pagination token. If empty, list starts at the first product.", "location": "query", "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/inappproducts", "response": {"$ref": "InappproductsListResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "patch": {"description": "Patches an in-app product (a managed product or a subscription). This method should no longer be used to update subscriptions. See [this article](https://android-developers.googleblog.com/2023/06/changes-to-google-play-developer-api-june-2023.html) for more information.", "flatPath": "androidpublisher/v3/applications/{packageName}/inappproducts/{sku}", "httpMethod": "PATCH", "id": "androidpublisher.inappproducts.patch", "parameterOrder": ["packageName", "sku"], "parameters": {"autoConvertMissingPrices": {"description": "If true the prices for all regions targeted by the parent app that don't have a price specified for this in-app product will be auto converted to the target currency based on the default price. Defaults to false.", "location": "query", "type": "boolean"}, "latencyTolerance": {"description": "Optional. The latency tolerance for the propagation of this product update. Defaults to latency-sensitive.", "enum": ["PRODUCT_UPDATE_LATENCY_TOLERANCE_UNSPECIFIED", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT"], "enumDescriptions": ["Defaults to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE.", "The update will propagate to clients within several minutes on average and up to a few hours in rare cases. Throughput is limited to 7,200 updates per app per hour.", "The update will propagate to clients within 24 hours. Supports high throughput of up to 720,000 updates per app per hour using batch modification methods."], "location": "query", "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}, "sku": {"description": "Unique identifier for the in-app product.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/inappproducts/{sku}", "request": {"$ref": "InAppProduct"}, "response": {"$ref": "InAppProduct"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "update": {"description": "Updates an in-app product (a managed product or a subscription). This method should no longer be used to update subscriptions. See [this article](https://android-developers.googleblog.com/2023/06/changes-to-google-play-developer-api-june-2023.html) for more information.", "flatPath": "androidpublisher/v3/applications/{packageName}/inappproducts/{sku}", "httpMethod": "PUT", "id": "androidpublisher.inappproducts.update", "parameterOrder": ["packageName", "sku"], "parameters": {"allowMissing": {"description": "If set to true, and the in-app product with the given package_name and sku doesn't exist, the in-app product will be created.", "location": "query", "type": "boolean"}, "autoConvertMissingPrices": {"description": "If true the prices for all regions targeted by the parent app that don't have a price specified for this in-app product will be auto converted to the target currency based on the default price. Defaults to false.", "location": "query", "type": "boolean"}, "latencyTolerance": {"description": "Optional. The latency tolerance for the propagation of this product update. Defaults to latency-sensitive.", "enum": ["PRODUCT_UPDATE_LATENCY_TOLERANCE_UNSPECIFIED", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT"], "enumDescriptions": ["Defaults to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE.", "The update will propagate to clients within several minutes on average and up to a few hours in rare cases. Throughput is limited to 7,200 updates per app per hour.", "The update will propagate to clients within 24 hours. Supports high throughput of up to 720,000 updates per app per hour using batch modification methods."], "location": "query", "type": "string"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}, "sku": {"description": "Unique identifier for the in-app product.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/inappproducts/{sku}", "request": {"$ref": "InAppProduct"}, "response": {"$ref": "InAppProduct"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}}}, "internalappsharingartifacts": {"methods": {"uploadapk": {"description": "Uploads an APK to internal app sharing. If you are using the Google API client libraries, please increase the timeout of the http request before calling this endpoint (a timeout of 2 minutes is recommended). See [Timeouts and Errors](https://developers.google.com/api-client-library/java/google-api-java-client/errors) for an example in java.", "flatPath": "androidpublisher/v3/applications/internalappsharing/{packageName}/artifacts/apk", "httpMethod": "POST", "id": "androidpublisher.internalappsharingartifacts.uploadapk", "mediaUpload": {"accept": ["application/octet-stream", "application/vnd.android.package-archive"], "maxSize": "1073741824", "protocols": {"resumable": {"multipart": true, "path": "/resumable/upload/androidpublisher/v3/applications/internalappsharing/{packageName}/artifacts/apk"}, "simple": {"multipart": true, "path": "/upload/androidpublisher/v3/applications/internalappsharing/{packageName}/artifacts/apk"}}}, "parameterOrder": ["packageName"], "parameters": {"packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/internalappsharing/{packageName}/artifacts/apk", "response": {"$ref": "InternalAppSharingArtifact"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"], "supportsMediaUpload": true}, "uploadbundle": {"description": "Uploads an app bundle to internal app sharing. If you are using the Google API client libraries, please increase the timeout of the http request before calling this endpoint (a timeout of 2 minutes is recommended). See [Timeouts and Errors](https://developers.google.com/api-client-library/java/google-api-java-client/errors) for an example in java.", "flatPath": "androidpublisher/v3/applications/internalappsharing/{packageName}/artifacts/bundle", "httpMethod": "POST", "id": "androidpublisher.internalappsharingartifacts.uploadbundle", "mediaUpload": {"accept": ["application/octet-stream"], "maxSize": "10737418240", "protocols": {"resumable": {"multipart": true, "path": "/resumable/upload/androidpublisher/v3/applications/internalappsharing/{packageName}/artifacts/bundle"}, "simple": {"multipart": true, "path": "/upload/androidpublisher/v3/applications/internalappsharing/{packageName}/artifacts/bundle"}}}, "parameterOrder": ["packageName"], "parameters": {"packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/internalappsharing/{packageName}/artifacts/bundle", "response": {"$ref": "InternalAppSharingArtifact"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"], "supportsMediaUpload": true}}}, "monetization": {"methods": {"convertRegionPrices": {"description": "Calculates the region prices, using today's exchange rate and country-specific pricing patterns, based on the price in the request for a set of regions.", "flatPath": "androidpublisher/v3/applications/{packageName}/pricing:convertRegionPrices", "httpMethod": "POST", "id": "androidpublisher.monetization.convertRegionPrices", "parameterOrder": ["packageName"], "parameters": {"packageName": {"description": "Required. The app package name.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/pricing:convertRegionPrices", "request": {"$ref": "ConvertRegionPricesRequest"}, "response": {"$ref": "ConvertRegionPricesResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}}, "resources": {"onetimeproducts": {"methods": {"batchDelete": {"description": "Deletes one or more one-time products.", "flatPath": "androidpublisher/v3/applications/{packageName}/oneTimeProducts:batchDelete", "httpMethod": "POST", "id": "androidpublisher.monetization.onetimeproducts.batchDelete", "parameterOrder": ["packageName"], "parameters": {"packageName": {"description": "Required. The parent app (package name) for which the one-time products should be deleted. Must be equal to the package_name field on all the OneTimeProduct resources.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/oneTimeProducts:batchDelete", "request": {"$ref": "BatchDeleteOneTimeProductsRequest"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "batchGet": {"description": "Reads one or more one-time products.", "flatPath": "androidpublisher/v3/applications/{packageName}/oneTimeProducts:batchGet", "httpMethod": "GET", "id": "androidpublisher.monetization.onetimeproducts.batchGet", "parameterOrder": ["packageName"], "parameters": {"packageName": {"description": "Required. The parent app (package name) for which the products should be retrieved. Must be equal to the package_name field on all requests.", "location": "path", "required": true, "type": "string"}, "productIds": {"description": "Required. A list of up to 100 product IDs to retrieve. All IDs must be different.", "location": "query", "repeated": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/oneTimeProducts:batchGet", "response": {"$ref": "BatchGetOneTimeProductsResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "batchUpdate": {"description": "Creates or updates one or more one-time products.", "flatPath": "androidpublisher/v3/applications/{packageName}/oneTimeProducts:batchUpdate", "httpMethod": "POST", "id": "androidpublisher.monetization.onetimeproducts.batchUpdate", "parameterOrder": ["packageName"], "parameters": {"packageName": {"description": "Required. The parent app (package name) for which the one-time products should be updated. Must be equal to the package_name field on all the OneTimeProduct resources.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/oneTimeProducts:batchUpdate", "request": {"$ref": "BatchUpdateOneTimeProductsRequest"}, "response": {"$ref": "BatchUpdateOneTimeProductsResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "delete": {"description": "Deletes a one-time product.", "flatPath": "androidpublisher/v3/applications/{packageName}/oneTimeProducts/{productId}", "httpMethod": "DELETE", "id": "androidpublisher.monetization.onetimeproducts.delete", "parameterOrder": ["packageName", "productId"], "parameters": {"latencyTolerance": {"description": "Optional. The latency tolerance for the propagation of this product update. Defaults to latency-sensitive.", "enum": ["PRODUCT_UPDATE_LATENCY_TOLERANCE_UNSPECIFIED", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT"], "enumDescriptions": ["Defaults to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE.", "The update will propagate to clients within several minutes on average and up to a few hours in rare cases. Throughput is limited to 7,200 updates per app per hour.", "The update will propagate to clients within 24 hours. Supports high throughput of up to 720,000 updates per app per hour using batch modification methods."], "location": "query", "type": "string"}, "packageName": {"description": "Required. The parent app (package name) of the one-time product to delete.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. The one-time product ID of the one-time product to delete.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/oneTimeProducts/{productId}", "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "get": {"description": "Reads a single one-time product.", "flatPath": "androidpublisher/v3/applications/{packageName}/oneTimeProducts/{productId}", "httpMethod": "GET", "id": "androidpublisher.monetization.onetimeproducts.get", "parameterOrder": ["packageName", "productId"], "parameters": {"packageName": {"description": "Required. The parent app (package name) of the product to retrieve.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. The product ID of the product to retrieve.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/oneTimeProducts/{productId}", "response": {"$ref": "OneTimeProduct"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "list": {"description": "Lists all one-time products under a given app.", "flatPath": "androidpublisher/v3/applications/{packageName}/oneTimeProducts", "httpMethod": "GET", "id": "androidpublisher.monetization.onetimeproducts.list", "parameterOrder": ["packageName"], "parameters": {"packageName": {"description": "Required. The parent app (package name) for which the one-time product should be read.", "location": "path", "required": true, "type": "string"}, "pageSize": {"description": "Optional. The maximum number of one-time product to return. The service may return fewer than this value. If unspecified, at most 50 one-time products will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListOneTimeProducts` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListOneTimeProducts` must match the call that provided the page token.", "location": "query", "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/oneTimeProducts", "response": {"$ref": "ListOneTimeProductsResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "patch": {"description": "Creates or updates a one-time product.", "flatPath": "androidpublisher/v3/applications/{packageName}/onetimeproducts/{productId}", "httpMethod": "PATCH", "id": "androidpublisher.monetization.onetimeproducts.patch", "parameterOrder": ["packageName", "productId"], "parameters": {"allowMissing": {"description": "Optional. If set to true, and the one-time product with the given package_name and product_id doesn't exist, the one-time product will be created. If a new one-time product is created, update_mask is ignored.", "location": "query", "type": "boolean"}, "latencyTolerance": {"description": "Optional. The latency tolerance for the propagation of this product upsert. Defaults to latency-sensitive.", "enum": ["PRODUCT_UPDATE_LATENCY_TOLERANCE_UNSPECIFIED", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT"], "enumDescriptions": ["Defaults to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE.", "The update will propagate to clients within several minutes on average and up to a few hours in rare cases. Throughput is limited to 7,200 updates per app per hour.", "The update will propagate to clients within 24 hours. Supports high throughput of up to 720,000 updates per app per hour using batch modification methods."], "location": "query", "type": "string"}, "packageName": {"description": "Required. Immutable. Package name of the parent app.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. Immutable. Unique product ID of the product. Unique within the parent app. Product IDs must start with a number or lowercase letter, and can contain numbers (0-9), lowercase letters (a-z), underscores (_), and periods (.).", "location": "path", "required": true, "type": "string"}, "regionsVersion.version": {"description": "Required. A string representing the version of available regions being used for the specified resource. Regional prices and latest supported version for the resource have to be specified according to the information published in [this article](https://support.google.com/googleplay/android-developer/answer/10532353). Each time the supported locations substantially change, the version will be incremented. Using this field will ensure that creating and updating the resource with an older region's version and set of regional prices and currencies will succeed even though a new version is available.", "location": "query", "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/onetimeproducts/{productId}", "request": {"$ref": "OneTimeProduct"}, "response": {"$ref": "OneTimeProduct"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}}, "resources": {"purchaseOptions": {"methods": {"batchDelete": {"description": "Deletes purchase options across one or multiple one-time products. By default this operation will fail if there are any existing offers under the deleted purchase options. Use the force parameter to override the default behavior.", "flatPath": "androidpublisher/v3/applications/{packageName}/oneTimeProducts/{productId}/purchaseOptions:batchDelete", "httpMethod": "POST", "id": "androidpublisher.monetization.onetimeproducts.purchaseOptions.batchDelete", "parameterOrder": ["packageName", "productId"], "parameters": {"packageName": {"description": "Required. The parent app (package name) of the purchase options to delete.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. The product ID of the parent one-time product, if all purchase options to delete belong to the same one-time product. If this batch delete spans multiple one-time products, set this field to \"-\".", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/oneTimeProducts/{productId}/purchaseOptions:batchDelete", "request": {"$ref": "BatchDeletePurchaseOptionsRequest"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "batchUpdateStates": {"description": "Activates or deactivates purchase options across one or multiple one-time products.", "flatPath": "androidpublisher/v3/applications/{packageName}/oneTimeProducts/{productId}/purchaseOptions:batchUpdateStates", "httpMethod": "POST", "id": "androidpublisher.monetization.onetimeproducts.purchaseOptions.batchUpdateStates", "parameterOrder": ["packageName", "productId"], "parameters": {"packageName": {"description": "Required. The parent app (package name) of the updated purchase options.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. The product ID of the parent one-time product, if all updated purchase options belong to the same one-time product. If this batch update spans multiple one-time products, set this field to \"-\".", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/oneTimeProducts/{productId}/purchaseOptions:batchUpdateStates", "request": {"$ref": "BatchUpdatePurchaseOptionStatesRequest"}, "response": {"$ref": "BatchUpdatePurchaseOptionStatesResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}}, "resources": {"offers": {"methods": {"activate": {"description": "Activates a one-time product offer.", "flatPath": "androidpublisher/v3/applications/{packageName}/oneTimeProducts/{productId}/purchaseOptions/{purchaseOptionId}/offers/{offerId}:activate", "httpMethod": "POST", "id": "androidpublisher.monetization.onetimeproducts.purchaseOptions.offers.activate", "parameterOrder": ["packageName", "productId", "purchaseOptionId", "offerId"], "parameters": {"offerId": {"description": "Required. The offer ID of the offer to activate.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Required. The parent app (package name) of the offer to activate.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. The parent one-time product (ID) of the offer to activate.", "location": "path", "required": true, "type": "string"}, "purchaseOptionId": {"description": "Required. The parent purchase option (ID) of the offer to activate.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/oneTimeProducts/{productId}/purchaseOptions/{purchaseOptionId}/offers/{offerId}:activate", "request": {"$ref": "ActivateOneTimeProductOfferRequest"}, "response": {"$ref": "OneTimeProductOffer"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "batchDelete": {"description": "Deletes one or more one-time product offers.", "flatPath": "androidpublisher/v3/applications/{packageName}/oneTimeProducts/{productId}/purchaseOptions/{purchaseOptionId}/offers:batchDelete", "httpMethod": "POST", "id": "androidpublisher.monetization.onetimeproducts.purchaseOptions.offers.batchDelete", "parameterOrder": ["packageName", "productId", "purchaseOptionId"], "parameters": {"packageName": {"description": "Required. The parent app (package name) of the offers to delete. Must be equal to the package_name field on all the OneTimeProductOffer resources.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. The product ID of the parent one-time product, if all offers to delete belong to the same product. If this request spans multiple one-time products, set this field to \"-\".", "location": "path", "required": true, "type": "string"}, "purchaseOptionId": {"description": "Required. The parent purchase option (ID) for which the offers should be deleted. May be specified as '-' to update offers from multiple purchase options.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/oneTimeProducts/{productId}/purchaseOptions/{purchaseOptionId}/offers:batchDelete", "request": {"$ref": "BatchDeleteOneTimeProductOffersRequest"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "batchGet": {"description": "Reads one or more one-time product offers.", "flatPath": "androidpublisher/v3/applications/{packageName}/oneTimeProducts/{productId}/purchaseOptions/{purchaseOptionId}/offers:batchGet", "httpMethod": "POST", "id": "androidpublisher.monetization.onetimeproducts.purchaseOptions.offers.batchGet", "parameterOrder": ["packageName", "productId", "purchaseOptionId"], "parameters": {"packageName": {"description": "Required. The parent app (package name) of the updated offers. Must be equal to the package_name field on all the updated OneTimeProductOffer resources.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. The product ID of the parent one-time product, if all updated offers belong to the same product. If this request spans multiple one-time products, set this field to \"-\".", "location": "path", "required": true, "type": "string"}, "purchaseOptionId": {"description": "Required. The parent purchase option (ID) for which the offers should be updated. May be specified as '-' to update offers from multiple purchase options.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/oneTimeProducts/{productId}/purchaseOptions/{purchaseOptionId}/offers:batchGet", "request": {"$ref": "BatchGetOneTimeProductOffersRequest"}, "response": {"$ref": "BatchGetOneTimeProductOffersResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "batchUpdate": {"description": "Creates or updates one or more one-time product offers.", "flatPath": "androidpublisher/v3/applications/{packageName}/oneTimeProducts/{productId}/purchaseOptions/{purchaseOptionId}/offers:batchUpdate", "httpMethod": "POST", "id": "androidpublisher.monetization.onetimeproducts.purchaseOptions.offers.batchUpdate", "parameterOrder": ["packageName", "productId", "purchaseOptionId"], "parameters": {"packageName": {"description": "Required. The parent app (package name) of the updated offers. Must be equal to the package_name field on all the updated OneTimeProductOffer resources.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. The product ID of the parent one-time product, if all updated offers belong to the same product. If this request spans multiple one-time products, set this field to \"-\".", "location": "path", "required": true, "type": "string"}, "purchaseOptionId": {"description": "Required. The parent purchase option (ID) for which the offers should be updated. May be specified as '-' to update offers from multiple purchase options.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/oneTimeProducts/{productId}/purchaseOptions/{purchaseOptionId}/offers:batchUpdate", "request": {"$ref": "BatchUpdateOneTimeProductOffersRequest"}, "response": {"$ref": "BatchUpdateOneTimeProductOffersResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "batchUpdateStates": {"description": "Updates a batch of one-time product offer states.", "flatPath": "androidpublisher/v3/applications/{packageName}/oneTimeProducts/{productId}/purchaseOptions/{purchaseOptionId}/offers:batchUpdateStates", "httpMethod": "POST", "id": "androidpublisher.monetization.onetimeproducts.purchaseOptions.offers.batchUpdateStates", "parameterOrder": ["packageName", "productId", "purchaseOptionId"], "parameters": {"packageName": {"description": "Required. The parent app (package name) of the updated one-time product offers.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. The product ID of the parent one-time product, if all updated offers belong to the same one-time product. If this batch update spans multiple one-time products, set this field to \"-\".", "location": "path", "required": true, "type": "string"}, "purchaseOptionId": {"description": "Required. The purchase option ID of the parent purchase option, if all updated offers belong to the same purchase option. If this batch update spans multiple purchase options, set this field to \"-\".", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/oneTimeProducts/{productId}/purchaseOptions/{purchaseOptionId}/offers:batchUpdateStates", "request": {"$ref": "BatchUpdateOneTimeProductOfferStatesRequest"}, "response": {"$ref": "BatchUpdateOneTimeProductOfferStatesResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "cancel": {"description": "Cancels a one-time product offer.", "flatPath": "androidpublisher/v3/applications/{packageName}/oneTimeProducts/{productId}/purchaseOptions/{purchaseOptionId}/offers/{offerId}:cancel", "httpMethod": "POST", "id": "androidpublisher.monetization.onetimeproducts.purchaseOptions.offers.cancel", "parameterOrder": ["packageName", "productId", "purchaseOptionId", "offerId"], "parameters": {"offerId": {"description": "Required. The offer ID of the offer to cancel.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Required. The parent app (package name) of the offer to cancel.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. The parent one-time product (ID) of the offer to cancel.", "location": "path", "required": true, "type": "string"}, "purchaseOptionId": {"description": "Required. The parent purchase option (ID) of the offer to cancel.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/oneTimeProducts/{productId}/purchaseOptions/{purchaseOptionId}/offers/{offerId}:cancel", "request": {"$ref": "CancelOneTimeProductOfferRequest"}, "response": {"$ref": "OneTimeProductOffer"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "deactivate": {"description": "Deactivates a one-time product offer.", "flatPath": "androidpublisher/v3/applications/{packageName}/oneTimeProducts/{productId}/purchaseOptions/{purchaseOptionId}/offers/{offerId}:deactivate", "httpMethod": "POST", "id": "androidpublisher.monetization.onetimeproducts.purchaseOptions.offers.deactivate", "parameterOrder": ["packageName", "productId", "purchaseOptionId", "offerId"], "parameters": {"offerId": {"description": "Required. The offer ID of the offer to deactivate.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Required. The parent app (package name) of the offer to deactivate.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. The parent one-time product (ID) of the offer to deactivate.", "location": "path", "required": true, "type": "string"}, "purchaseOptionId": {"description": "Required. The parent purchase option (ID) of the offer to deactivate.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/oneTimeProducts/{productId}/purchaseOptions/{purchaseOptionId}/offers/{offerId}:deactivate", "request": {"$ref": "DeactivateOneTimeProductOfferRequest"}, "response": {"$ref": "OneTimeProductOffer"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "list": {"description": "Lists all offers under a given app, product, or purchase option.", "flatPath": "androidpublisher/v3/applications/{packageName}/oneTimeProducts/{productId}/purchaseOptions/{purchaseOptionId}/offers", "httpMethod": "GET", "id": "androidpublisher.monetization.onetimeproducts.purchaseOptions.offers.list", "parameterOrder": ["packageName", "productId", "purchaseOptionId"], "parameters": {"packageName": {"description": "Required. The parent app (package name) for which the offers should be read.", "location": "path", "required": true, "type": "string"}, "pageSize": {"description": "Optional. The maximum number of offers to return. The service may return fewer than this value. If unspecified, at most 50 offers will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListOneTimeProductsOffers` call. Provide this to retrieve the subsequent page. When paginating, product_id, package_name and purchase_option_id provided to `ListOneTimeProductsOffersRequest` must match the call that provided the page token.", "location": "query", "type": "string"}, "productId": {"description": "Required. The parent one-time product (ID) for which the offers should be read. May be specified as '-' to read all offers under an app.", "location": "path", "required": true, "type": "string"}, "purchaseOptionId": {"description": "Required. The parent purchase option (ID) for which the offers should be read. May be specified as '-' to read all offers under a one-time product or an app. Must be specified as '-' if product_id is specified as '-'.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/oneTimeProducts/{productId}/purchaseOptions/{purchaseOptionId}/offers", "response": {"$ref": "ListOneTimeProductOffersResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}}}}}}}, "subscriptions": {"methods": {"archive": {"deprecated": true, "description": "Deprecated: subscription archiving is not supported.", "flatPath": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}:archive", "httpMethod": "POST", "id": "androidpublisher.monetization.subscriptions.archive", "parameterOrder": ["packageName", "productId"], "parameters": {"packageName": {"description": "Required. The parent app (package name) of the app of the subscription to delete.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. The unique product ID of the subscription to delete.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}:archive", "request": {"$ref": "ArchiveSubscriptionRequest"}, "response": {"$ref": "Subscription"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "batchGet": {"description": "Reads one or more subscriptions.", "flatPath": "androidpublisher/v3/applications/{packageName}/subscriptions:batchGet", "httpMethod": "GET", "id": "androidpublisher.monetization.subscriptions.batchGet", "parameterOrder": ["packageName"], "parameters": {"packageName": {"description": "Required. The parent app (package name) for which the subscriptions should be retrieved. Must be equal to the package_name field on all the requests.", "location": "path", "required": true, "type": "string"}, "productIds": {"description": "Required. A list of up to 100 subscription product IDs to retrieve. All the IDs must be different.", "location": "query", "repeated": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/subscriptions:batchGet", "response": {"$ref": "BatchGetSubscriptionsResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "batchUpdate": {"description": "Updates a batch of subscriptions. Set the latencyTolerance field on nested requests to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT to achieve maximum update throughput.", "flatPath": "androidpublisher/v3/applications/{packageName}/subscriptions:batchUpdate", "httpMethod": "POST", "id": "androidpublisher.monetization.subscriptions.batchUpdate", "parameterOrder": ["packageName"], "parameters": {"packageName": {"description": "Required. The parent app (package name) for which the subscriptions should be updated. Must be equal to the package_name field on all the Subscription resources.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/subscriptions:batchUpdate", "request": {"$ref": "BatchUpdateSubscriptionsRequest"}, "response": {"$ref": "BatchUpdateSubscriptionsResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "create": {"description": "Creates a new subscription. Newly added base plans will remain in draft state until activated.", "flatPath": "androidpublisher/v3/applications/{packageName}/subscriptions", "httpMethod": "POST", "id": "androidpublisher.monetization.subscriptions.create", "parameterOrder": ["packageName"], "parameters": {"packageName": {"description": "Required. The parent app (package name) for which the subscription should be created. Must be equal to the package_name field on the Subscription resource.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. The ID to use for the subscription. For the requirements on this format, see the documentation of the product_id field on the Subscription resource.", "location": "query", "type": "string"}, "regionsVersion.version": {"description": "Required. A string representing the version of available regions being used for the specified resource. Regional prices and latest supported version for the resource have to be specified according to the information published in [this article](https://support.google.com/googleplay/android-developer/answer/10532353). Each time the supported locations substantially change, the version will be incremented. Using this field will ensure that creating and updating the resource with an older region's version and set of regional prices and currencies will succeed even though a new version is available.", "location": "query", "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/subscriptions", "request": {"$ref": "Subscription"}, "response": {"$ref": "Subscription"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "delete": {"description": "Deletes a subscription. A subscription can only be deleted if it has never had a base plan published.", "flatPath": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}", "httpMethod": "DELETE", "id": "androidpublisher.monetization.subscriptions.delete", "parameterOrder": ["packageName", "productId"], "parameters": {"packageName": {"description": "Required. The parent app (package name) of the app of the subscription to delete.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. The unique product ID of the subscription to delete.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}", "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "get": {"description": "Reads a single subscription.", "flatPath": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}", "httpMethod": "GET", "id": "androidpublisher.monetization.subscriptions.get", "parameterOrder": ["packageName", "productId"], "parameters": {"packageName": {"description": "Required. The parent app (package name) of the subscription to get.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. The unique product ID of the subscription to get.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}", "response": {"$ref": "Subscription"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "list": {"description": "Lists all subscriptions under a given app.", "flatPath": "androidpublisher/v3/applications/{packageName}/subscriptions", "httpMethod": "GET", "id": "androidpublisher.monetization.subscriptions.list", "parameterOrder": ["packageName"], "parameters": {"packageName": {"description": "Required. The parent app (package name) for which the subscriptions should be read.", "location": "path", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of subscriptions to return. The service may return fewer than this value. If unspecified, at most 50 subscriptions will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListSubscriptions` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListSubscriptions` must match the call that provided the page token.", "location": "query", "type": "string"}, "showArchived": {"deprecated": true, "description": "Deprecated: subscription archiving is not supported.", "location": "query", "type": "boolean"}}, "path": "androidpublisher/v3/applications/{packageName}/subscriptions", "response": {"$ref": "ListSubscriptionsResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "patch": {"description": "Updates an existing subscription.", "flatPath": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}", "httpMethod": "PATCH", "id": "androidpublisher.monetization.subscriptions.patch", "parameterOrder": ["packageName", "productId"], "parameters": {"allowMissing": {"description": "Optional. If set to true, and the subscription with the given package_name and product_id doesn't exist, the subscription will be created. If a new subscription is created, update_mask is ignored.", "location": "query", "type": "boolean"}, "latencyTolerance": {"description": "Optional. The latency tolerance for the propagation of this product update. Defaults to latency-sensitive.", "enum": ["PRODUCT_UPDATE_LATENCY_TOLERANCE_UNSPECIFIED", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT"], "enumDescriptions": ["Defaults to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE.", "The update will propagate to clients within several minutes on average and up to a few hours in rare cases. Throughput is limited to 7,200 updates per app per hour.", "The update will propagate to clients within 24 hours. Supports high throughput of up to 720,000 updates per app per hour using batch modification methods."], "location": "query", "type": "string"}, "packageName": {"description": "Immutable. Package name of the parent app.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Immutable. Unique product ID of the product. Unique within the parent app. Product IDs must be composed of lower-case letters (a-z), numbers (0-9), underscores (_) and dots (.). It must start with a lower-case letter or number, and be between 1 and 40 (inclusive) characters in length.", "location": "path", "required": true, "type": "string"}, "regionsVersion.version": {"description": "Required. A string representing the version of available regions being used for the specified resource. Regional prices and latest supported version for the resource have to be specified according to the information published in [this article](https://support.google.com/googleplay/android-developer/answer/10532353). Each time the supported locations substantially change, the version will be incremented. Using this field will ensure that creating and updating the resource with an older region's version and set of regional prices and currencies will succeed even though a new version is available.", "location": "query", "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}", "request": {"$ref": "Subscription"}, "response": {"$ref": "Subscription"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}}, "resources": {"basePlans": {"methods": {"activate": {"description": "Activates a base plan. Once activated, base plans will be available to new subscribers.", "flatPath": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans/{basePlanId}:activate", "httpMethod": "POST", "id": "androidpublisher.monetization.subscriptions.basePlans.activate", "parameterOrder": ["packageName", "productId", "basePlanId"], "parameters": {"basePlanId": {"description": "Required. The unique base plan ID of the base plan to activate.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Required. The parent app (package name) of the base plan to activate.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. The parent subscription (ID) of the base plan to activate.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans/{basePlanId}:activate", "request": {"$ref": "ActivateBasePlanRequest"}, "response": {"$ref": "Subscription"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "batchMigratePrices": {"description": "Batch variant of the MigrateBasePlanPrices endpoint. Set the latencyTolerance field on nested requests to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT to achieve maximum update throughput.", "flatPath": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans:batchMigratePrices", "httpMethod": "POST", "id": "androidpublisher.monetization.subscriptions.basePlans.batchMigratePrices", "parameterOrder": ["packageName", "productId"], "parameters": {"packageName": {"description": "Required. The parent app (package name) for which the subscriptions should be created or updated. Must be equal to the package_name field on all the Subscription resources.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. The product ID of the parent subscription, if all updated offers belong to the same subscription. If this batch update spans multiple subscriptions, set this field to \"-\". Must be set.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans:batchMigratePrices", "request": {"$ref": "BatchMigrateBasePlanPricesRequest"}, "response": {"$ref": "BatchMigrateBasePlanPricesResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "batchUpdateStates": {"description": "Activates or deactivates base plans across one or multiple subscriptions. Set the latencyTolerance field on nested requests to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT to achieve maximum update throughput.", "flatPath": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans:batchUpdateStates", "httpMethod": "POST", "id": "androidpublisher.monetization.subscriptions.basePlans.batchUpdateStates", "parameterOrder": ["packageName", "productId"], "parameters": {"packageName": {"description": "Required. The parent app (package name) of the updated base plans.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. The product ID of the parent subscription, if all updated base plans belong to the same subscription. If this batch update spans multiple subscriptions, set this field to \"-\". Must be set.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans:batchUpdateStates", "request": {"$ref": "BatchUpdateBasePlanStatesRequest"}, "response": {"$ref": "BatchUpdateBasePlanStatesResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "deactivate": {"description": "Deactivates a base plan. Once deactivated, the base plan will become unavailable to new subscribers, but existing subscribers will maintain their subscription", "flatPath": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans/{basePlanId}:deactivate", "httpMethod": "POST", "id": "androidpublisher.monetization.subscriptions.basePlans.deactivate", "parameterOrder": ["packageName", "productId", "basePlanId"], "parameters": {"basePlanId": {"description": "Required. The unique base plan ID of the base plan to deactivate.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Required. The parent app (package name) of the base plan to deactivate.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. The parent subscription (ID) of the base plan to deactivate.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans/{basePlanId}:deactivate", "request": {"$ref": "DeactivateBasePlanRequest"}, "response": {"$ref": "Subscription"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "delete": {"description": "Deletes a base plan. Can only be done for draft base plans. This action is irreversible.", "flatPath": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans/{basePlanId}", "httpMethod": "DELETE", "id": "androidpublisher.monetization.subscriptions.basePlans.delete", "parameterOrder": ["packageName", "productId", "basePlanId"], "parameters": {"basePlanId": {"description": "Required. The unique offer ID of the base plan to delete.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Required. The parent app (package name) of the base plan to delete.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. The parent subscription (ID) of the base plan to delete.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans/{basePlanId}", "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "migratePrices": {"description": "Migrates subscribers from one or more legacy price cohorts to the current price. Requests result in Google Play notifying affected subscribers. Only up to 250 simultaneous legacy price cohorts are supported.", "flatPath": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans/{basePlanId}:migratePrices", "httpMethod": "POST", "id": "androidpublisher.monetization.subscriptions.basePlans.migratePrices", "parameterOrder": ["packageName", "productId", "basePlanId"], "parameters": {"basePlanId": {"description": "Required. The unique base plan ID of the base plan to update prices on.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Required. Package name of the parent app. Must be equal to the package_name field on the Subscription resource.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. The ID of the subscription to update. Must be equal to the product_id field on the Subscription resource.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans/{basePlanId}:migratePrices", "request": {"$ref": "MigrateBasePlanPricesRequest"}, "response": {"$ref": "MigrateBasePlanPricesResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}}, "resources": {"offers": {"methods": {"activate": {"description": "Activates a subscription offer. Once activated, subscription offers will be available to new subscribers.", "flatPath": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans/{basePlanId}/offers/{offerId}:activate", "httpMethod": "POST", "id": "androidpublisher.monetization.subscriptions.basePlans.offers.activate", "parameterOrder": ["packageName", "productId", "basePlanId", "offerId"], "parameters": {"basePlanId": {"description": "Required. The parent base plan (ID) of the offer to activate.", "location": "path", "required": true, "type": "string"}, "offerId": {"description": "Required. The unique offer ID of the offer to activate.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Required. The parent app (package name) of the offer to activate.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. The parent subscription (ID) of the offer to activate.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans/{basePlanId}/offers/{offerId}:activate", "request": {"$ref": "ActivateSubscriptionOfferRequest"}, "response": {"$ref": "SubscriptionOffer"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "batchGet": {"description": "Reads one or more subscription offers.", "flatPath": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans/{basePlanId}/offers:batchGet", "httpMethod": "POST", "id": "androidpublisher.monetization.subscriptions.basePlans.offers.batchGet", "parameterOrder": ["packageName", "productId", "basePlanId"], "parameters": {"basePlanId": {"description": "Required. The parent base plan (ID) for which the offers should be read. May be specified as '-' to read offers from multiple base plans.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Required. The parent app (package name) for which the subscriptions should be created or updated. Must be equal to the package_name field on all the requests.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. The product ID of the parent subscription, if all updated offers belong to the same subscription. If this request spans multiple subscriptions, set this field to \"-\". Must be set.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans/{basePlanId}/offers:batchGet", "request": {"$ref": "BatchGetSubscriptionOffersRequest"}, "response": {"$ref": "BatchGetSubscriptionOffersResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "batchUpdate": {"description": "Updates a batch of subscription offers. Set the latencyTolerance field on nested requests to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT to achieve maximum update throughput.", "flatPath": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans/{basePlanId}/offers:batchUpdate", "httpMethod": "POST", "id": "androidpublisher.monetization.subscriptions.basePlans.offers.batchUpdate", "parameterOrder": ["packageName", "productId", "basePlanId"], "parameters": {"basePlanId": {"description": "Required. The parent base plan (ID) for which the offers should be updated. May be specified as '-' to update offers from multiple base plans.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Required. The parent app (package name) of the updated subscription offers. Must be equal to the package_name field on all the updated SubscriptionOffer resources.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. The product ID of the parent subscription, if all updated offers belong to the same subscription. If this request spans multiple subscriptions, set this field to \"-\". Must be set.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans/{basePlanId}/offers:batchUpdate", "request": {"$ref": "BatchUpdateSubscriptionOffersRequest"}, "response": {"$ref": "BatchUpdateSubscriptionOffersResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "batchUpdateStates": {"description": "Updates a batch of subscription offer states. Set the latencyTolerance field on nested requests to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT to achieve maximum update throughput.", "flatPath": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans/{basePlanId}/offers:batchUpdateStates", "httpMethod": "POST", "id": "androidpublisher.monetization.subscriptions.basePlans.offers.batchUpdateStates", "parameterOrder": ["packageName", "productId", "basePlanId"], "parameters": {"basePlanId": {"description": "Required. The parent base plan (ID) for which the offers should be updated. May be specified as '-' to update offers from multiple base plans.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Required. The parent app (package name) of the updated subscription offers. Must be equal to the package_name field on all the updated SubscriptionOffer resources.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. The product ID of the parent subscription, if all updated offers belong to the same subscription. If this request spans multiple subscriptions, set this field to \"-\". Must be set.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans/{basePlanId}/offers:batchUpdateStates", "request": {"$ref": "BatchUpdateSubscriptionOfferStatesRequest"}, "response": {"$ref": "BatchUpdateSubscriptionOfferStatesResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "create": {"description": "Creates a new subscription offer. Only auto-renewing base plans can have subscription offers. The offer state will be DRAFT until it is activated.", "flatPath": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans/{basePlanId}/offers", "httpMethod": "POST", "id": "androidpublisher.monetization.subscriptions.basePlans.offers.create", "parameterOrder": ["packageName", "productId", "basePlanId"], "parameters": {"basePlanId": {"description": "Required. The parent base plan (ID) for which the offer should be created. Must be equal to the base_plan_id field on the SubscriptionOffer resource.", "location": "path", "required": true, "type": "string"}, "offerId": {"description": "Required. The ID to use for the offer. For the requirements on this format, see the documentation of the offer_id field on the SubscriptionOffer resource.", "location": "query", "type": "string"}, "packageName": {"description": "Required. The parent app (package name) for which the offer should be created. Must be equal to the package_name field on the Subscription resource.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. The parent subscription (ID) for which the offer should be created. Must be equal to the product_id field on the SubscriptionOffer resource.", "location": "path", "required": true, "type": "string"}, "regionsVersion.version": {"description": "Required. A string representing the version of available regions being used for the specified resource. Regional prices and latest supported version for the resource have to be specified according to the information published in [this article](https://support.google.com/googleplay/android-developer/answer/10532353). Each time the supported locations substantially change, the version will be incremented. Using this field will ensure that creating and updating the resource with an older region's version and set of regional prices and currencies will succeed even though a new version is available.", "location": "query", "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans/{basePlanId}/offers", "request": {"$ref": "SubscriptionOffer"}, "response": {"$ref": "SubscriptionOffer"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "deactivate": {"description": "Deactivates a subscription offer. Once deactivated, existing subscribers will maintain their subscription, but the offer will become unavailable to new subscribers.", "flatPath": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans/{basePlanId}/offers/{offerId}:deactivate", "httpMethod": "POST", "id": "androidpublisher.monetization.subscriptions.basePlans.offers.deactivate", "parameterOrder": ["packageName", "productId", "basePlanId", "offerId"], "parameters": {"basePlanId": {"description": "Required. The parent base plan (ID) of the offer to deactivate.", "location": "path", "required": true, "type": "string"}, "offerId": {"description": "Required. The unique offer ID of the offer to deactivate.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Required. The parent app (package name) of the offer to deactivate.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. The parent subscription (ID) of the offer to deactivate.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans/{basePlanId}/offers/{offerId}:deactivate", "request": {"$ref": "DeactivateSubscriptionOfferRequest"}, "response": {"$ref": "SubscriptionOffer"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "delete": {"description": "Deletes a subscription offer. Can only be done for draft offers. This action is irreversible.", "flatPath": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans/{basePlanId}/offers/{offerId}", "httpMethod": "DELETE", "id": "androidpublisher.monetization.subscriptions.basePlans.offers.delete", "parameterOrder": ["packageName", "productId", "basePlanId", "offerId"], "parameters": {"basePlanId": {"description": "Required. The parent base plan (ID) of the offer to delete.", "location": "path", "required": true, "type": "string"}, "offerId": {"description": "Required. The unique offer ID of the offer to delete.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Required. The parent app (package name) of the offer to delete.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. The parent subscription (ID) of the offer to delete.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans/{basePlanId}/offers/{offerId}", "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "get": {"description": "Reads a single offer", "flatPath": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans/{basePlanId}/offers/{offerId}", "httpMethod": "GET", "id": "androidpublisher.monetization.subscriptions.basePlans.offers.get", "parameterOrder": ["packageName", "productId", "basePlanId", "offerId"], "parameters": {"basePlanId": {"description": "Required. The parent base plan (ID) of the offer to get.", "location": "path", "required": true, "type": "string"}, "offerId": {"description": "Required. The unique offer ID of the offer to get.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Required. The parent app (package name) of the offer to get.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. The parent subscription (ID) of the offer to get.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans/{basePlanId}/offers/{offerId}", "response": {"$ref": "SubscriptionOffer"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "list": {"description": "Lists all offers under a given subscription.", "flatPath": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans/{basePlanId}/offers", "httpMethod": "GET", "id": "androidpublisher.monetization.subscriptions.basePlans.offers.list", "parameterOrder": ["packageName", "productId", "basePlanId"], "parameters": {"basePlanId": {"description": "Required. The parent base plan (ID) for which the offers should be read. May be specified as '-' to read all offers under a subscription or an app. Must be specified as '-' if product_id is specified as '-'.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Required. The parent app (package name) for which the subscriptions should be read.", "location": "path", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of subscriptions to return. The service may return fewer than this value. If unspecified, at most 50 subscriptions will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListSubscriptionsOffers` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListSubscriptionOffers` must match the call that provided the page token.", "location": "query", "type": "string"}, "productId": {"description": "Required. The parent subscription (ID) for which the offers should be read. May be specified as '-' to read all offers under an app.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans/{basePlanId}/offers", "response": {"$ref": "ListSubscriptionOffersResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "patch": {"description": "Updates an existing subscription offer.", "flatPath": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans/{basePlanId}/offers/{offerId}", "httpMethod": "PATCH", "id": "androidpublisher.monetization.subscriptions.basePlans.offers.patch", "parameterOrder": ["packageName", "productId", "basePlanId", "offerId"], "parameters": {"allowMissing": {"description": "Optional. If set to true, and the subscription offer with the given package_name, product_id, base_plan_id and offer_id doesn't exist, an offer will be created. If a new offer is created, update_mask is ignored.", "location": "query", "type": "boolean"}, "basePlanId": {"description": "Required. Immutable. The ID of the base plan to which this offer is an extension.", "location": "path", "required": true, "type": "string"}, "latencyTolerance": {"description": "Optional. The latency tolerance for the propagation of this product update. Defaults to latency-sensitive.", "enum": ["PRODUCT_UPDATE_LATENCY_TOLERANCE_UNSPECIFIED", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT"], "enumDescriptions": ["Defaults to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE.", "The update will propagate to clients within several minutes on average and up to a few hours in rare cases. Throughput is limited to 7,200 updates per app per hour.", "The update will propagate to clients within 24 hours. Supports high throughput of up to 720,000 updates per app per hour using batch modification methods."], "location": "query", "type": "string"}, "offerId": {"description": "Required. Immutable. Unique ID of this subscription offer. Must be unique within the base plan.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Required. Immutable. The package name of the app the parent subscription belongs to.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "Required. Immutable. The ID of the parent subscription this offer belongs to.", "location": "path", "required": true, "type": "string"}, "regionsVersion.version": {"description": "Required. A string representing the version of available regions being used for the specified resource. Regional prices and latest supported version for the resource have to be specified according to the information published in [this article](https://support.google.com/googleplay/android-developer/answer/10532353). Each time the supported locations substantially change, the version will be incremented. Using this field will ensure that creating and updating the resource with an older region's version and set of regional prices and currencies will succeed even though a new version is available.", "location": "query", "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/subscriptions/{productId}/basePlans/{basePlanId}/offers/{offerId}", "request": {"$ref": "SubscriptionOffer"}, "response": {"$ref": "SubscriptionOffer"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}}}}}}}}}, "orders": {"methods": {"batchget": {"description": "Get order details for a list of orders.", "flatPath": "androidpublisher/v3/applications/{packageName}/orders:batchGet", "httpMethod": "GET", "id": "androidpublisher.orders.batchget", "parameterOrder": ["packageName"], "parameters": {"orderIds": {"description": "Required. The list of order IDs to retrieve order details for. There must be between 1 and 1000 (inclusive) order IDs per request. If any order ID is not found or does not match the provided package, the entire request will fail with an error. The order IDs must be distinct.", "location": "query", "repeated": true, "type": "string"}, "packageName": {"description": "Required. The package name of the application for which this subscription or in-app item was purchased (for example, 'com.some.thing').", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/orders:batchGet", "response": {"$ref": "BatchGetOrdersResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "get": {"description": "Get order details for a single order.", "flatPath": "androidpublisher/v3/applications/{packageName}/orders/{orderId}", "httpMethod": "GET", "id": "androidpublisher.orders.get", "parameterOrder": ["packageName", "orderId"], "parameters": {"orderId": {"description": "Required. The order ID provided to the user when the subscription or in-app order was purchased.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "Required. The package name of the application for which this subscription or in-app item was purchased (for example, 'com.some.thing').", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/orders/{orderId}", "response": {"$ref": "Order"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "refund": {"description": "Refunds a user's subscription or in-app purchase order. Orders older than 3 years cannot be refunded.", "flatPath": "androidpublisher/v3/applications/{packageName}/orders/{orderId}:refund", "httpMethod": "POST", "id": "androidpublisher.orders.refund", "parameterOrder": ["packageName", "orderId"], "parameters": {"orderId": {"description": "The order ID provided to the user when the subscription or in-app order was purchased.", "location": "path", "required": true, "type": "string"}, "packageName": {"description": "The package name of the application for which this subscription or in-app item was purchased (for example, 'com.some.thing').", "location": "path", "required": true, "type": "string"}, "revoke": {"description": "Whether to revoke the purchased item. If set to true, access to the subscription or in-app item will be terminated immediately. If the item is a recurring subscription, all future payments will also be terminated. Consumed in-app items need to be handled by developer's app. (optional).", "location": "query", "type": "boolean"}}, "path": "androidpublisher/v3/applications/{packageName}/orders/{orderId}:refund", "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}}}, "purchases": {"resources": {"products": {"methods": {"acknowledge": {"description": "Acknowledges a purchase of an inapp item.", "flatPath": "androidpublisher/v3/applications/{packageName}/purchases/products/{productId}/tokens/{token}:acknowledge", "httpMethod": "POST", "id": "androidpublisher.purchases.products.acknowledge", "parameterOrder": ["packageName", "productId", "token"], "parameters": {"packageName": {"description": "The package name of the application the inapp product was sold in (for example, 'com.some.thing').", "location": "path", "required": true, "type": "string"}, "productId": {"description": "The inapp product SKU (for example, 'com.some.thing.inapp1').", "location": "path", "required": true, "type": "string"}, "token": {"description": "The token provided to the user's device when the inapp product was purchased.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/purchases/products/{productId}/tokens/{token}:acknowledge", "request": {"$ref": "ProductPurchasesAcknowledgeRequest"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "consume": {"description": "Consumes a purchase for an inapp item.", "flatPath": "androidpublisher/v3/applications/{packageName}/purchases/products/{productId}/tokens/{token}:consume", "httpMethod": "POST", "id": "androidpublisher.purchases.products.consume", "parameterOrder": ["packageName", "productId", "token"], "parameters": {"packageName": {"description": "The package name of the application the inapp product was sold in (for example, 'com.some.thing').", "location": "path", "required": true, "type": "string"}, "productId": {"description": "The inapp product SKU (for example, 'com.some.thing.inapp1').", "location": "path", "required": true, "type": "string"}, "token": {"description": "The token provided to the user's device when the inapp product was purchased.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/purchases/products/{productId}/tokens/{token}:consume", "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "get": {"description": "Checks the purchase and consumption status of an inapp item.", "flatPath": "androidpublisher/v3/applications/{packageName}/purchases/products/{productId}/tokens/{token}", "httpMethod": "GET", "id": "androidpublisher.purchases.products.get", "parameterOrder": ["packageName", "productId", "token"], "parameters": {"packageName": {"description": "The package name of the application the inapp product was sold in (for example, 'com.some.thing').", "location": "path", "required": true, "type": "string"}, "productId": {"description": "The inapp product SKU (for example, 'com.some.thing.inapp1').", "location": "path", "required": true, "type": "string"}, "token": {"description": "The token provided to the user's device when the inapp product was purchased.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/purchases/products/{productId}/tokens/{token}", "response": {"$ref": "ProductPurchase"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}}}, "productsv2": {"methods": {"getproductpurchasev2": {"description": "Checks the purchase and consumption status of an inapp item.", "flatPath": "androidpublisher/v3/applications/{packageName}/purchases/productsv2/tokens/{token}", "httpMethod": "GET", "id": "androidpublisher.purchases.productsv2.getproductpurchasev2", "parameterOrder": ["packageName", "token"], "parameters": {"packageName": {"description": "The package name of the application the inapp product was sold in (for example, 'com.some.thing').", "location": "path", "required": true, "type": "string"}, "token": {"description": "The token provided to the user's device when the inapp product was purchased.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/purchases/productsv2/tokens/{token}", "response": {"$ref": "ProductPurchaseV2"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}}}, "subscriptions": {"methods": {"acknowledge": {"description": "Acknowledges a subscription purchase.", "flatPath": "androidpublisher/v3/applications/{packageName}/purchases/subscriptions/{subscriptionId}/tokens/{token}:acknowledge", "httpMethod": "POST", "id": "androidpublisher.purchases.subscriptions.acknowledge", "parameterOrder": ["packageName", "subscriptionId", "token"], "parameters": {"packageName": {"description": "The package name of the application for which this subscription was purchased (for example, 'com.some.thing').", "location": "path", "required": true, "type": "string"}, "subscriptionId": {"description": "Note: Since May 21, 2025, subscription_id is not required, and not recommended for subscription with add-ons. The purchased subscription ID (for example, 'monthly001').", "location": "path", "required": true, "type": "string"}, "token": {"description": "The token provided to the user's device when the subscription was purchased.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/purchases/subscriptions/{subscriptionId}/tokens/{token}:acknowledge", "request": {"$ref": "SubscriptionPurchasesAcknowledgeRequest"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "cancel": {"description": "Cancels a user's subscription purchase. The subscription remains valid until its expiration time. Newer version is available at purchases.subscriptionsv2.cancel for better client library support.", "flatPath": "androidpublisher/v3/applications/{packageName}/purchases/subscriptions/{subscriptionId}/tokens/{token}:cancel", "httpMethod": "POST", "id": "androidpublisher.purchases.subscriptions.cancel", "parameterOrder": ["packageName", "subscriptionId", "token"], "parameters": {"packageName": {"description": "The package name of the application for which this subscription was purchased (for example, 'com.some.thing').", "location": "path", "required": true, "type": "string"}, "subscriptionId": {"description": "Note: Since May 21, 2025, subscription_id is not required, and not recommended for subscription with add-ons. The purchased subscription ID (for example, 'monthly001').", "location": "path", "required": true, "type": "string"}, "token": {"description": "The token provided to the user's device when the subscription was purchased.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/purchases/subscriptions/{subscriptionId}/tokens/{token}:cancel", "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "defer": {"description": "Defers a user's subscription purchase until a specified future expiration time.", "flatPath": "androidpublisher/v3/applications/{packageName}/purchases/subscriptions/{subscriptionId}/tokens/{token}:defer", "httpMethod": "POST", "id": "androidpublisher.purchases.subscriptions.defer", "parameterOrder": ["packageName", "subscriptionId", "token"], "parameters": {"packageName": {"description": "The package name of the application for which this subscription was purchased (for example, 'com.some.thing').", "location": "path", "required": true, "type": "string"}, "subscriptionId": {"description": "The purchased subscription ID (for example, 'monthly001').", "location": "path", "required": true, "type": "string"}, "token": {"description": "The token provided to the user's device when the subscription was purchased.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/purchases/subscriptions/{subscriptionId}/tokens/{token}:defer", "request": {"$ref": "SubscriptionPurchasesDeferRequest"}, "response": {"$ref": "SubscriptionPurchasesDeferResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "get": {"deprecated": true, "description": "Deprecated: Use purchases.subscriptionsv2.get instead. Checks whether a user's subscription purchase is valid and returns its expiry time.", "flatPath": "androidpublisher/v3/applications/{packageName}/purchases/subscriptions/{subscriptionId}/tokens/{token}", "httpMethod": "GET", "id": "androidpublisher.purchases.subscriptions.get", "parameterOrder": ["packageName", "subscriptionId", "token"], "parameters": {"packageName": {"description": "The package name of the application for which this subscription was purchased (for example, 'com.some.thing').", "location": "path", "required": true, "type": "string"}, "subscriptionId": {"description": "The purchased subscription ID (for example, 'monthly001').", "location": "path", "required": true, "type": "string"}, "token": {"description": "The token provided to the user's device when the subscription was purchased.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/purchases/subscriptions/{subscriptionId}/tokens/{token}", "response": {"$ref": "SubscriptionPurchase"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "refund": {"deprecated": true, "description": "Deprecated: Use orders.refund instead. Refunds a user's subscription purchase, but the subscription remains valid until its expiration time and it will continue to recur.", "flatPath": "androidpublisher/v3/applications/{packageName}/purchases/subscriptions/{subscriptionId}/tokens/{token}:refund", "httpMethod": "POST", "id": "androidpublisher.purchases.subscriptions.refund", "parameterOrder": ["packageName", "subscriptionId", "token"], "parameters": {"packageName": {"description": "The package name of the application for which this subscription was purchased (for example, 'com.some.thing').", "location": "path", "required": true, "type": "string"}, "subscriptionId": {"description": "\"The purchased subscription ID (for example, 'monthly001').", "location": "path", "required": true, "type": "string"}, "token": {"description": "The token provided to the user's device when the subscription was purchased.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/purchases/subscriptions/{subscriptionId}/tokens/{token}:refund", "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "revoke": {"deprecated": true, "description": "Deprecated: Use purchases.subscriptionsv2.revoke instead. Refunds and immediately revokes a user's subscription purchase. Access to the subscription will be terminated immediately and it will stop recurring.", "flatPath": "androidpublisher/v3/applications/{packageName}/purchases/subscriptions/{subscriptionId}/tokens/{token}:revoke", "httpMethod": "POST", "id": "androidpublisher.purchases.subscriptions.revoke", "parameterOrder": ["packageName", "subscriptionId", "token"], "parameters": {"packageName": {"description": "The package name of the application for which this subscription was purchased (for example, 'com.some.thing').", "location": "path", "required": true, "type": "string"}, "subscriptionId": {"description": "The purchased subscription ID (for example, 'monthly001').", "location": "path", "required": true, "type": "string"}, "token": {"description": "The token provided to the user's device when the subscription was purchased.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/purchases/subscriptions/{subscriptionId}/tokens/{token}:revoke", "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}}}, "subscriptionsv2": {"methods": {"get": {"description": "Get metadata about a subscription", "flatPath": "androidpublisher/v3/applications/{packageName}/purchases/subscriptionsv2/tokens/{token}", "httpMethod": "GET", "id": "androidpublisher.purchases.subscriptionsv2.get", "parameterOrder": ["packageName", "token"], "parameters": {"packageName": {"description": "The package of the application for which this subscription was purchased (for example, 'com.some.thing').", "location": "path", "required": true, "type": "string"}, "token": {"description": "Required. The token provided to the user's device when the subscription was purchased.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/purchases/subscriptionsv2/tokens/{token}", "response": {"$ref": "SubscriptionPurchaseV2"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "revoke": {"description": "Revoke a subscription purchase for the user.", "flatPath": "androidpublisher/v3/applications/{packageName}/purchases/subscriptionsv2/tokens/{token}:revoke", "httpMethod": "POST", "id": "androidpublisher.purchases.subscriptionsv2.revoke", "parameterOrder": ["packageName", "token"], "parameters": {"packageName": {"description": "Required. The package of the application for which this subscription was purchased (for example, 'com.some.thing').", "location": "path", "required": true, "type": "string"}, "token": {"description": "Required. The token provided to the user's device when the subscription was purchased.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/purchases/subscriptionsv2/tokens/{token}:revoke", "request": {"$ref": "RevokeSubscriptionPurchaseRequest"}, "response": {"$ref": "RevokeSubscriptionPurchaseResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}}}, "voidedpurchases": {"methods": {"list": {"description": "Lists the purchases that were canceled, refunded or charged-back.", "flatPath": "androidpublisher/v3/applications/{packageName}/purchases/voidedpurchases", "httpMethod": "GET", "id": "androidpublisher.purchases.voidedpurchases.list", "parameterOrder": ["packageName"], "parameters": {"endTime": {"description": "The time, in milliseconds since the Epoch, of the newest voided purchase that you want to see in the response. The value of this parameter cannot be greater than the current time and is ignored if a pagination token is set. Default value is current time. Note: This filter is applied on the time at which the record is seen as voided by our systems and not the actual voided time returned in the response.", "format": "int64", "location": "query", "type": "string"}, "includeQuantityBasedPartialRefund": {"description": "Optional. Whether to include voided purchases of quantity-based partial refunds, which are applicable only to multi-quantity purchases. If true, additional voided purchases may be returned with voidedQuantity that indicates the refund quantity of a quantity-based partial refund. The default value is false.", "location": "query", "type": "boolean"}, "maxResults": {"description": "Defines how many results the list operation should return. The default number depends on the resource collection.", "format": "uint32", "location": "query", "type": "integer"}, "packageName": {"description": "The package name of the application for which voided purchases need to be returned (for example, 'com.some.thing').", "location": "path", "required": true, "type": "string"}, "startIndex": {"description": "Defines the index of the first element to return. This can only be used if indexed paging is enabled.", "format": "uint32", "location": "query", "type": "integer"}, "startTime": {"description": "The time, in milliseconds since the Epoch, of the oldest voided purchase that you want to see in the response. The value of this parameter cannot be older than 30 days and is ignored if a pagination token is set. Default value is current time minus 30 days. Note: This filter is applied on the time at which the record is seen as voided by our systems and not the actual voided time returned in the response.", "format": "int64", "location": "query", "type": "string"}, "token": {"description": "Defines the token of the page to return, usually taken from TokenPagination. This can only be used if token paging is enabled.", "location": "query", "type": "string"}, "type": {"description": "The type of voided purchases that you want to see in the response. Possible values are: 0. Only voided in-app product purchases will be returned in the response. This is the default value. 1. Both voided in-app purchases and voided subscription purchases will be returned in the response. Note: Before requesting to receive voided subscription purchases, you must switch to use orderId in the response which uniquely identifies one-time purchases and subscriptions. Otherwise, you will receive multiple subscription orders with the same PurchaseToken, because subscription renewal orders share the same PurchaseToken.", "format": "int32", "location": "query", "type": "integer"}}, "path": "androidpublisher/v3/applications/{packageName}/purchases/voidedpurchases", "response": {"$ref": "VoidedPurchasesListResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}}}}}, "reviews": {"methods": {"get": {"description": "Gets a single review.", "flatPath": "androidpublisher/v3/applications/{packageName}/reviews/{reviewId}", "httpMethod": "GET", "id": "androidpublisher.reviews.get", "parameterOrder": ["packageName", "reviewId"], "parameters": {"packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}, "reviewId": {"description": "Unique identifier for a review.", "location": "path", "required": true, "type": "string"}, "translationLanguage": {"description": "Language localization code.", "location": "query", "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/reviews/{reviewId}", "response": {"$ref": "Review"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "list": {"description": "Lists all reviews.", "flatPath": "androidpublisher/v3/applications/{packageName}/reviews", "httpMethod": "GET", "id": "androidpublisher.reviews.list", "parameterOrder": ["packageName"], "parameters": {"maxResults": {"description": "How many results the list operation should return.", "format": "uint32", "location": "query", "type": "integer"}, "packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}, "startIndex": {"description": "The index of the first element to return.", "format": "uint32", "location": "query", "type": "integer"}, "token": {"description": "Pagination token. If empty, list starts at the first review.", "location": "query", "type": "string"}, "translationLanguage": {"description": "Language localization code.", "location": "query", "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/reviews", "response": {"$ref": "ReviewsListResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "reply": {"description": "Replies to a single review, or updates an existing reply.", "flatPath": "androidpublisher/v3/applications/{packageName}/reviews/{reviewId}:reply", "httpMethod": "POST", "id": "androidpublisher.reviews.reply", "parameterOrder": ["packageName", "reviewId"], "parameters": {"packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}, "reviewId": {"description": "Unique identifier for a review.", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/reviews/{reviewId}:reply", "request": {"$ref": "ReviewsReplyRequest"}, "response": {"$ref": "ReviewsReplyResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}}}, "systemapks": {"resources": {"variants": {"methods": {"create": {"description": "Creates an APK which is suitable for inclusion in a system image from an already uploaded Android App Bundle.", "flatPath": "androidpublisher/v3/applications/{packageName}/systemApks/{versionCode}/variants", "httpMethod": "POST", "id": "androidpublisher.systemapks.variants.create", "parameterOrder": ["packageName", "versionCode"], "parameters": {"packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}, "versionCode": {"description": "The version code of the App Bundle.", "format": "int64", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/systemApks/{versionCode}/variants", "request": {"$ref": "<PERSON><PERSON><PERSON>"}, "response": {"$ref": "<PERSON><PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "download": {"description": "Downloads a previously created system APK which is suitable for inclusion in a system image.", "flatPath": "androidpublisher/v3/applications/{packageName}/systemApks/{versionCode}/variants/{variantId}:download", "httpMethod": "GET", "id": "androidpublisher.systemapks.variants.download", "parameterOrder": ["packageName", "versionCode", "variantId"], "parameters": {"packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}, "variantId": {"description": "The ID of a previously created system APK variant.", "format": "uint32", "location": "path", "required": true, "type": "integer"}, "versionCode": {"description": "The version code of the App Bundle.", "format": "int64", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/systemApks/{versionCode}/variants/{variantId}:download", "scopes": ["https://www.googleapis.com/auth/androidpublisher"], "supportsMediaDownload": true, "useMediaDownloadService": true}, "get": {"description": "Returns a previously created system APK variant.", "flatPath": "androidpublisher/v3/applications/{packageName}/systemApks/{versionCode}/variants/{variantId}", "httpMethod": "GET", "id": "androidpublisher.systemapks.variants.get", "parameterOrder": ["packageName", "versionCode", "variantId"], "parameters": {"packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}, "variantId": {"description": "The ID of a previously created system APK variant.", "format": "uint32", "location": "path", "required": true, "type": "integer"}, "versionCode": {"description": "The version code of the App Bundle.", "format": "int64", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/systemApks/{versionCode}/variants/{variantId}", "response": {"$ref": "<PERSON><PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "list": {"description": "Returns the list of previously created system APK variants.", "flatPath": "androidpublisher/v3/applications/{packageName}/systemApks/{versionCode}/variants", "httpMethod": "GET", "id": "androidpublisher.systemapks.variants.list", "parameterOrder": ["packageName", "versionCode"], "parameters": {"packageName": {"description": "Package name of the app.", "location": "path", "required": true, "type": "string"}, "versionCode": {"description": "The version code of the App Bundle.", "format": "int64", "location": "path", "required": true, "type": "string"}}, "path": "androidpublisher/v3/applications/{packageName}/systemApks/{versionCode}/variants", "response": {"$ref": "SystemApksListResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}}}}}, "users": {"methods": {"create": {"description": "Grant access for a user to the given developer account.", "flatPath": "androidpublisher/v3/developers/{developersId}/users", "httpMethod": "POST", "id": "androidpublisher.users.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The developer account to add the user to. Format: developers/{developer}", "location": "path", "pattern": "^developers/[^/]+$", "required": true, "type": "string"}}, "path": "androidpublisher/v3/{+parent}/users", "request": {"$ref": "User"}, "response": {"$ref": "User"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "delete": {"description": "Removes all access for the user to the given developer account.", "flatPath": "androidpublisher/v3/developers/{developersId}/users/{usersId}", "httpMethod": "DELETE", "id": "androidpublisher.users.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the user to delete. Format: developers/{developer}/users/{email}", "location": "path", "pattern": "^developers/[^/]+/users/[^/]+$", "required": true, "type": "string"}}, "path": "androidpublisher/v3/{+name}", "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "list": {"description": "Lists all users with access to a developer account.", "flatPath": "androidpublisher/v3/developers/{developersId}/users", "httpMethod": "GET", "id": "androidpublisher.users.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of results to return. This must be set to -1 to disable pagination.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token received from a previous call to this method, in order to retrieve further results.", "location": "query", "type": "string"}, "parent": {"description": "Required. The developer account to fetch users from. Format: developers/{developer}", "location": "path", "pattern": "^developers/[^/]+$", "required": true, "type": "string"}}, "path": "androidpublisher/v3/{+parent}/users", "response": {"$ref": "ListUsersResponse"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}, "patch": {"description": "Updates access for the user to the developer account.", "flatPath": "androidpublisher/v3/developers/{developersId}/users/{usersId}", "httpMethod": "PATCH", "id": "androidpublisher.users.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name for this user, following the pattern \"developers/{developer}/users/{email}\".", "location": "path", "pattern": "^developers/[^/]+/users/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "androidpublisher/v3/{+name}", "request": {"$ref": "User"}, "response": {"$ref": "User"}, "scopes": ["https://www.googleapis.com/auth/androidpublisher"]}}}}, "revision": "********", "rootUrl": "https://androidpublisher.googleapis.com/", "schemas": {"Abi": {"description": "Represents an Abi.", "id": "<PERSON><PERSON>", "properties": {"alias": {"description": "<PERSON><PERSON> for an abi.", "enum": ["UNSPECIFIED_CPU_ARCHITECTURE", "ARMEABI", "ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64", "RISCV64"], "enumDescriptions": ["Unspecified abi.", "ARMEABI abi.", "ARMEABI_V7A abi.", "ARM64_V8A abi.", "X86 abi.", "X86_64 abi.", "RISCV64 abi."], "type": "string"}}, "type": "object"}, "AbiTargeting": {"description": "Targeting based on Abi.", "id": "AbiTargeting", "properties": {"alternatives": {"description": "Targeting of other sibling directories that were in the Bundle. For main splits this is targeting of other main splits.", "items": {"$ref": "<PERSON><PERSON>"}, "type": "array"}, "value": {"description": "Value of an abi.", "items": {"$ref": "<PERSON><PERSON>"}, "type": "array"}}, "type": "object"}, "AcquisitionTargetingRule": {"description": "Represents a targeting rule of the form: User never had {scope} before.", "id": "AcquisitionTargetingRule", "properties": {"scope": {"$ref": "TargetingRuleScope", "description": "Required. The scope of subscriptions this rule considers. Only allows \"this subscription\" and \"any subscription in app\"."}}, "type": "object"}, "ActivateBasePlanRequest": {"description": "Request message for ActivateBasePlan.", "id": "ActivateBasePlanRequest", "properties": {"basePlanId": {"description": "Required. The unique base plan ID of the base plan to activate.", "type": "string"}, "latencyTolerance": {"description": "Optional. The latency tolerance for the propagation of this product update. Defaults to latency-sensitive.", "enum": ["PRODUCT_UPDATE_LATENCY_TOLERANCE_UNSPECIFIED", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT"], "enumDescriptions": ["Defaults to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE.", "The update will propagate to clients within several minutes on average and up to a few hours in rare cases. Throughput is limited to 7,200 updates per app per hour.", "The update will propagate to clients within 24 hours. Supports high throughput of up to 720,000 updates per app per hour using batch modification methods."], "type": "string"}, "packageName": {"description": "Required. The parent app (package name) of the base plan to activate.", "type": "string"}, "productId": {"description": "Required. The parent subscription (ID) of the base plan to activate.", "type": "string"}}, "type": "object"}, "ActivateOneTimeProductOfferRequest": {"description": "Request message for ActivateOneTimeProductOffer.", "id": "ActivateOneTimeProductOfferRequest", "properties": {"latencyTolerance": {"description": "Optional. The latency tolerance for the propagation of this update. Defaults to latency-sensitive.", "enum": ["PRODUCT_UPDATE_LATENCY_TOLERANCE_UNSPECIFIED", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT"], "enumDescriptions": ["Defaults to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE.", "The update will propagate to clients within several minutes on average and up to a few hours in rare cases. Throughput is limited to 7,200 updates per app per hour.", "The update will propagate to clients within 24 hours. Supports high throughput of up to 720,000 updates per app per hour using batch modification methods."], "type": "string"}, "offerId": {"description": "Required. The offer ID of the offer to activate.", "type": "string"}, "packageName": {"description": "Required. The parent app (package name) of the offer to activate.", "type": "string"}, "productId": {"description": "Required. The parent one-time product (ID) of the offer to activate.", "type": "string"}, "purchaseOptionId": {"description": "Required. The parent purchase option (ID) of the offer to activate.", "type": "string"}}, "type": "object"}, "ActivatePurchaseOptionRequest": {"description": "Request message for UpdatePurchaseOptionState.", "id": "ActivatePurchaseOptionRequest", "properties": {"latencyTolerance": {"description": "Optional. The latency tolerance for the propagation of this product update. Defaults to latency-sensitive.", "enum": ["PRODUCT_UPDATE_LATENCY_TOLERANCE_UNSPECIFIED", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT"], "enumDescriptions": ["Defaults to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE.", "The update will propagate to clients within several minutes on average and up to a few hours in rare cases. Throughput is limited to 7,200 updates per app per hour.", "The update will propagate to clients within 24 hours. Supports high throughput of up to 720,000 updates per app per hour using batch modification methods."], "type": "string"}, "packageName": {"description": "Required. The parent app (package name) of the purchase option to activate.", "type": "string"}, "productId": {"description": "Required. The parent one-time product (ID) of the purchase option to activate.", "type": "string"}, "purchaseOptionId": {"description": "Required. The purchase option ID of the purchase option to activate.", "type": "string"}}, "type": "object"}, "ActivateSubscriptionOfferRequest": {"description": "Request message for ActivateSubscriptionOffer.", "id": "ActivateSubscriptionOfferRequest", "properties": {"basePlanId": {"description": "Required. The parent base plan (ID) of the offer to activate.", "type": "string"}, "latencyTolerance": {"description": "Optional. The latency tolerance for the propagation of this product update. Defaults to latency-sensitive.", "enum": ["PRODUCT_UPDATE_LATENCY_TOLERANCE_UNSPECIFIED", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT"], "enumDescriptions": ["Defaults to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE.", "The update will propagate to clients within several minutes on average and up to a few hours in rare cases. Throughput is limited to 7,200 updates per app per hour.", "The update will propagate to clients within 24 hours. Supports high throughput of up to 720,000 updates per app per hour using batch modification methods."], "type": "string"}, "offerId": {"description": "Required. The unique offer ID of the offer to activate.", "type": "string"}, "packageName": {"description": "Required. The parent app (package name) of the offer to activate.", "type": "string"}, "productId": {"description": "Required. The parent subscription (ID) of the offer to activate.", "type": "string"}}, "type": "object"}, "AddTargetingRequest": {"description": "Request message for AddTargeting.", "id": "AddTargetingRequest", "properties": {"targetingUpdate": {"$ref": "TargetingUpdate", "description": "Specifies targeting updates such as regions, android sdk versions etc."}}, "type": "object"}, "AddTargetingResponse": {"description": "Response message for AddTargeting.", "id": "AddTargetingResponse", "properties": {}, "type": "object"}, "AllUsers": {"description": "Object representation to describe all set of users.", "id": "AllUsers", "properties": {"isAllUsersRequested": {"description": "Required. Set to true if all set of users are needed.", "type": "boolean"}}, "type": "object"}, "AndroidSdks": {"description": "Android api level targeting data for app recovery action targeting.", "id": "AndroidSdks", "properties": {"sdkLevels": {"description": "Android api levels of devices targeted by recovery action. See https://developer.android.com/guide/topics/manifest/uses-sdk-element#ApiLevels for different api levels in android.", "items": {"format": "int64", "type": "string"}, "type": "array"}}, "type": "object"}, "Apk": {"description": "Information about an APK. The resource for ApksService.", "id": "Apk", "properties": {"binary": {"$ref": "ApkBinary", "description": "Information about the binary payload of this APK."}, "versionCode": {"description": "The version code of the APK, as specified in the manifest file.", "format": "int32", "type": "integer"}}, "type": "object"}, "ApkBinary": {"description": "Represents the binary payload of an APK.", "id": "ApkBinary", "properties": {"sha1": {"description": "A sha1 hash of the APK payload, encoded as a hex string and matching the output of the sha1sum command.", "type": "string"}, "sha256": {"description": "A sha256 hash of the APK payload, encoded as a hex string and matching the output of the sha256sum command.", "type": "string"}}, "type": "object"}, "ApkDescription": {"description": "Description of the created apks.", "id": "ApkDescription", "properties": {"assetSliceMetadata": {"$ref": "SplitApkMetadata", "description": "Set only for asset slices."}, "instantApkMetadata": {"$ref": "SplitApkMetadata", "description": "Set only for Instant split APKs."}, "path": {"description": "Path of the Apk, will be in the following format: .apk where DownloadId is the ID used to download the apk using GeneratedApks.Download API.", "type": "string"}, "splitApkMetadata": {"$ref": "SplitApkMetadata", "description": "Set only for Split APKs."}, "standaloneApkMetadata": {"$ref": "StandaloneApkMetadata", "description": "Set only for standalone APKs."}, "targeting": {"$ref": "ApkTargeting", "description": "Apk-level targeting."}}, "type": "object"}, "ApkSet": {"description": "A set of apks representing a module.", "id": "ApkSet", "properties": {"apkDescription": {"description": "Description of the generated apks.", "items": {"$ref": "ApkDescription"}, "type": "array"}, "moduleMetadata": {"$ref": "ModuleMetadata", "description": "Metadata about the module represented by this ApkSet"}}, "type": "object"}, "ApkTargeting": {"description": "Represents a set of apk-level targetings.", "id": "ApkTargeting", "properties": {"abiTargeting": {"$ref": "AbiTargeting", "description": "The abi that the apk targets"}, "languageTargeting": {"$ref": "LanguageTargeting", "description": "The language that the apk targets"}, "multiAbiTargeting": {"$ref": "MultiAbiTargeting", "description": "Multi-api-level targeting."}, "screenDensityTargeting": {"$ref": "ScreenDensityTargeting", "description": "The screen density that this apk supports."}, "sdkVersionTargeting": {"$ref": "SdkVersionTargeting", "description": "The sdk version that the apk targets"}, "textureCompressionFormatTargeting": {"$ref": "TextureCompressionFormatTargeting", "description": "Texture-compression-format-level targeting"}}, "type": "object"}, "ApksAddExternallyHostedRequest": {"description": "Request to create a new externally hosted APK.", "id": "ApksAddExternallyHostedRequest", "properties": {"externallyHostedApk": {"$ref": "ExternallyHostedApk", "description": "The definition of the externally-hosted APK and where it is located."}}, "type": "object"}, "ApksAddExternallyHostedResponse": {"description": "Response for creating a new externally hosted APK.", "id": "ApksAddExternallyHostedResponse", "properties": {"externallyHostedApk": {"$ref": "ExternallyHostedApk", "description": "The definition of the externally-hosted APK and where it is located."}}, "type": "object"}, "ApksListResponse": {"description": "Response listing all APKs.", "id": "ApksListResponse", "properties": {"apks": {"description": "All APKs.", "items": {"$ref": "Apk"}, "type": "array"}, "kind": {"description": "The kind of this response (\"androidpublisher#apksListResponse\").", "type": "string"}}, "type": "object"}, "AppDetails": {"description": "The app details. The resource for DetailsService.", "id": "AppDetails", "properties": {"contactEmail": {"description": "The user-visible support email for this app.", "type": "string"}, "contactPhone": {"description": "The user-visible support telephone number for this app.", "type": "string"}, "contactWebsite": {"description": "The user-visible website for this app.", "type": "string"}, "defaultLanguage": {"description": "Default language code, in BCP 47 format (eg \"en-US\").", "type": "string"}}, "type": "object"}, "AppEdit": {"description": "An app edit. The resource for EditsService.", "id": "AppEdit", "properties": {"expiryTimeSeconds": {"description": "Output only. The time (as seconds since Epoch) at which the edit will expire and will be no longer valid for use.", "readOnly": true, "type": "string"}, "id": {"description": "Output only. Identifier of the edit. Can be used in subsequent API calls.", "readOnly": true, "type": "string"}}, "type": "object"}, "AppRecoveryAction": {"description": "Information about an app recovery action.", "id": "AppRecoveryAction", "properties": {"appRecoveryId": {"description": "ID corresponding to the app recovery action.", "format": "int64", "type": "string"}, "cancelTime": {"description": "Timestamp of when the app recovery action is canceled by the developer. Only set if the recovery action has been canceled.", "format": "google-datetime", "type": "string"}, "createTime": {"description": "Timestamp of when the app recovery action is created by the developer. It is always set after creation of the recovery action.", "format": "google-datetime", "type": "string"}, "deployTime": {"description": "Timestamp of when the app recovery action is deployed to the users. Only set if the recovery action has been deployed.", "format": "google-datetime", "type": "string"}, "lastUpdateTime": {"description": "Timestamp of when the developer last updated recovery action. In case the action is cancelled, it corresponds to cancellation time. It is always set after creation of the recovery action.", "format": "google-datetime", "type": "string"}, "remoteInAppUpdateData": {"$ref": "RemoteInAppUpdateData", "description": "Data about the remote in-app update action such as such as recovered user base, recoverable user base etc. Set only if the recovery action type is Remote In-App Update."}, "status": {"description": "The status of the recovery action.", "enum": ["RECOVERY_STATUS_UNSPECIFIED", "RECOVERY_STATUS_ACTIVE", "RECOVERY_STATUS_CANCELED", "RECOVERY_STATUS_DRAFT", "RECOVERY_STATUS_GENERATION_IN_PROGRESS", "RECOVERY_STATUS_GENERATION_FAILED"], "enumDescriptions": ["RecoveryStatus is unspecified.", "The app recovery action has not been canceled since it has been created.", "The recovery action has been canceled. The action cannot be resumed.", "The recovery action is in the draft state and has not yet been deployed to users.", "The recovery action is generating recovery apks.", "The app recovery action generation has failed."], "type": "string"}, "targeting": {"$ref": "Targeting", "description": "Specifies targeting criteria for the recovery action such as regions, android sdk versions, app versions etc."}}, "type": "object"}, "AppVersionList": {"description": "Data format for a list of app versions.", "id": "AppVersionList", "properties": {"versionCodes": {"description": "List of app version codes.", "items": {"format": "int64", "type": "string"}, "type": "array"}}, "type": "object"}, "AppVersionRange": {"description": "Data format for a continuous range of app versions.", "id": "AppVersionRange", "properties": {"versionCodeEnd": {"description": "Highest app version in the range, inclusive.", "format": "int64", "type": "string"}, "versionCodeStart": {"description": "Lowest app version in the range, inclusive.", "format": "int64", "type": "string"}}, "type": "object"}, "ArchiveSubscriptionRequest": {"deprecated": true, "description": "Deprecated: subscription archiving is not supported.", "id": "ArchiveSubscriptionRequest", "properties": {}, "type": "object"}, "AssetModuleMetadata": {"description": "Metadata of an asset module.", "id": "AssetModuleMetadata", "properties": {"deliveryType": {"description": "Indicates the delivery type for persistent install.", "enum": ["UNKNOWN_DELIVERY_TYPE", "INSTALL_TIME", "ON_DEMAND", "FAST_FOLLOW"], "enumDescriptions": ["Unspecified delivery type.", "This module will always be downloaded as part of the initial install of the app.", "This module is requested on-demand, which means it will not be part of the initial install, and will only be sent when requested by the client.", "This module will be downloaded immediately after initial install finishes. The app can be opened before these modules are downloaded."], "type": "string"}, "name": {"description": "Module name.", "type": "string"}}, "type": "object"}, "AssetSliceSet": {"description": "Set of asset slices belonging to a single asset module.", "id": "AssetSliceSet", "properties": {"apkDescription": {"description": "Asset slices.", "items": {"$ref": "ApkDescription"}, "type": "array"}, "assetModuleMetadata": {"$ref": "AssetModuleMetadata", "description": "Module level metadata."}}, "type": "object"}, "AutoRenewingBasePlanType": {"description": "Represents a base plan that automatically renews at the end of its subscription period.", "id": "AutoRenewingBasePlanType", "properties": {"accountHoldDuration": {"description": "Optional. Account hold period of the subscription, specified in ISO 8601 format. Acceptable values must be in days and between P0D and P60D. If not specified, the default value is P30D. The sum of gracePeriodDuration and accountHoldDuration must be between P30D and P60D days, inclusive.", "type": "string"}, "billingPeriodDuration": {"description": "Required. Immutable. Subscription period, specified in ISO 8601 format. For a list of acceptable billing periods, refer to the help center. The duration is immutable after the base plan is created.", "type": "string"}, "gracePeriodDuration": {"description": "Grace period of the subscription, specified in ISO 8601 format. Acceptable values must be in days and between P0D and the lesser of 30D and base plan billing period. If not specified, a default value will be used based on the billing period. The sum of gracePeriodDuration and accountHoldDuration must be between P30D and P60D days, inclusive.", "type": "string"}, "legacyCompatible": {"description": "Whether the renewing base plan is backward compatible. The backward compatible base plan is returned by the Google Play Billing Library deprecated method querySkuDetailsAsync(). Only one renewing base plan can be marked as legacy compatible for a given subscription.", "type": "boolean"}, "legacyCompatibleSubscriptionOfferId": {"description": "Subscription offer id which is legacy compatible. The backward compatible subscription offer is returned by the Google Play Billing Library deprecated method querySkuDetailsAsync(). Only one subscription offer can be marked as legacy compatible for a given renewing base plan. To have no Subscription offer as legacy compatible set this field as empty string.", "type": "string"}, "prorationMode": {"description": "The proration mode for the base plan determines what happens when a user switches to this plan from another base plan. If unspecified, defaults to CHARGE_ON_NEXT_BILLING_DATE.", "enum": ["SUBSCRIPTION_PRORATION_MODE_UNSPECIFIED", "SUBSCRIPTION_PRORATION_MODE_CHARGE_ON_NEXT_BILLING_DATE", "SUBSCRIPTION_PRORATION_MODE_CHARGE_FULL_PRICE_IMMEDIATELY"], "enumDescriptions": ["Unspecified mode.", "Users will be charged for their new base plan at the end of their current billing period.", "Users will be charged for their new base plan immediately and in full. Any remaining period of their existing subscription will be used to extend the duration of the new billing plan."], "type": "string"}, "resubscribeState": {"description": "Whether users should be able to resubscribe to this base plan in Google Play surfaces. Defaults to RESUBSCRIBE_STATE_ACTIVE if not specified.", "enum": ["RESUBSCRIBE_STATE_UNSPECIFIED", "RESUBSCRIBE_STATE_ACTIVE", "RESUBSCRIBE_STATE_INACTIVE"], "enumDescriptions": ["Unspecified state.", "Resubscribe is active.", "Resubscribe is inactive."], "type": "string"}}, "type": "object"}, "AutoRenewingPlan": {"description": "Information related to an auto renewing plan.", "id": "AutoRenewingPlan", "properties": {"autoRenewEnabled": {"description": "If the subscription is currently set to auto-renew, e.g. the user has not canceled the subscription", "type": "boolean"}, "installmentDetails": {"$ref": "InstallmentPlan", "description": "The installment plan commitment and state related info for the auto renewing plan."}, "priceChangeDetails": {"$ref": "SubscriptionItemPriceChangeDetails", "description": "The information of the last price change for the item since subscription signup."}, "recurringPrice": {"$ref": "Money", "description": "The current recurring price of the auto renewing plan. Note that the price does not take into account discounts and does not include taxes for tax-exclusive pricing, please call orders.get API instead if transaction details are needed."}}, "type": "object"}, "BasePlan": {"description": "A single base plan for a subscription.", "id": "BasePlan", "properties": {"autoRenewingBasePlanType": {"$ref": "AutoRenewingBasePlanType", "description": "Set when the base plan automatically renews at a regular interval."}, "basePlanId": {"description": "Required. Immutable. The unique identifier of this base plan. Must be unique within the subscription, and conform with RFC-1034. That is, this ID can only contain lower-case letters (a-z), numbers (0-9), and hyphens (-), and be at most 63 characters.", "type": "string"}, "installmentsBasePlanType": {"$ref": "InstallmentsBasePlanType", "description": "Set for installments base plans where a user is committed to a specified number of payments."}, "offerTags": {"description": "List of up to 20 custom tags specified for this base plan, and returned to the app through the billing library. Subscription offers for this base plan will also receive these offer tags in the billing library.", "items": {"$ref": "OfferTag"}, "type": "array"}, "otherRegionsConfig": {"$ref": "OtherRegionsBasePlanConfig", "description": "Pricing information for any new locations Play may launch in the future. If omitted, the BasePlan will not be automatically available any new locations Play may launch in the future."}, "prepaidBasePlanType": {"$ref": "PrepaidBasePlanType", "description": "Set when the base plan does not automatically renew at the end of the billing period."}, "regionalConfigs": {"description": "Region-specific information for this base plan.", "items": {"$ref": "RegionalBasePlanConfig"}, "type": "array"}, "state": {"description": "Output only. The state of the base plan, i.e. whether it's active. Draft and inactive base plans can be activated or deleted. Active base plans can be made inactive. Inactive base plans can be canceled. This field cannot be changed by updating the resource. Use the dedicated endpoints instead.", "enum": ["STATE_UNSPECIFIED", "DRAFT", "ACTIVE", "INACTIVE"], "enumDescriptions": ["Unspecified state.", "The base plan is currently in a draft state, and hasn't been activated. It can be safely deleted at this point.", "The base plan is active and available for new subscribers.", "The base plan is inactive and only available for existing subscribers."], "readOnly": true, "type": "string"}}, "type": "object"}, "BatchDeleteOneTimeProductOffersRequest": {"description": "Request message for BatchDeleteOneTimeProductOffers.", "id": "BatchDeleteOneTimeProductOffersRequest", "properties": {"requests": {"description": "Required. A list of update requests of up to 100 elements. All requests must correspond to different offers.", "items": {"$ref": "DeleteOneTimeProductOfferRequest"}, "type": "array"}}, "type": "object"}, "BatchDeleteOneTimeProductsRequest": {"description": "Request message for BatchDeleteOneTimeProduct.", "id": "BatchDeleteOneTimeProductsRequest", "properties": {"requests": {"description": "Required. A list of delete requests of up to 100 elements. All requests must delete different one-time products.", "items": {"$ref": "DeleteOneTimeProductRequest"}, "type": "array"}}, "type": "object"}, "BatchDeletePurchaseOptionsRequest": {"description": "Request message for BatchDeletePurchaseOption.", "id": "BatchDeletePurchaseOptionsRequest", "properties": {"requests": {"description": "Required. A list of delete requests of up to 100 elements. All requests must delete purchase options from different one-time products.", "items": {"$ref": "DeletePurchaseOptionRequest"}, "type": "array"}}, "type": "object"}, "BatchGetOneTimeProductOffersRequest": {"description": "Request message for the BatchGetOneTimeProductOffers endpoint.", "id": "BatchGetOneTimeProductOffersRequest", "properties": {"requests": {"description": "Required. A list of get requests of up to 100 elements. All requests must retrieve different offers.", "items": {"$ref": "GetOneTimeProductOfferRequest"}, "type": "array"}}, "type": "object"}, "BatchGetOneTimeProductOffersResponse": {"description": "Response message for the BatchGetOneTimeProductOffers endpoint.", "id": "BatchGetOneTimeProductOffersResponse", "properties": {"oneTimeProductOffers": {"description": "The list of updated one-time product offers, in the same order as the request.", "items": {"$ref": "OneTimeProductOffer"}, "type": "array"}}, "type": "object"}, "BatchGetOneTimeProductsResponse": {"description": "Response message for the BatchGetOneTimeProducts endpoint.", "id": "BatchGetOneTimeProductsResponse", "properties": {"oneTimeProducts": {"description": "The list of requested one-time products, in the same order as the request.", "items": {"$ref": "OneTimeProduct"}, "type": "array"}}, "type": "object"}, "BatchGetOrdersResponse": {"description": "Response for the orders.batchGet API.", "id": "BatchGetOrdersResponse", "properties": {"orders": {"description": "Details for the requested order IDs.", "items": {"$ref": "Order"}, "type": "array"}}, "type": "object"}, "BatchGetSubscriptionOffersRequest": {"description": "Request message for BatchGetSubscriptionOffers endpoint.", "id": "BatchGetSubscriptionOffersRequest", "properties": {"requests": {"description": "Required. A list of update requests of up to 100 elements. All requests must update different subscriptions.", "items": {"$ref": "GetSubscriptionOfferRequest"}, "type": "array"}}, "type": "object"}, "BatchGetSubscriptionOffersResponse": {"description": "Response message for BatchGetSubscriptionOffers endpoint.", "id": "BatchGetSubscriptionOffersResponse", "properties": {"subscriptionOffers": {"items": {"$ref": "SubscriptionOffer"}, "type": "array"}}, "type": "object"}, "BatchGetSubscriptionsResponse": {"description": "Response message for BatchGetSubscriptions endpoint.", "id": "BatchGetSubscriptionsResponse", "properties": {"subscriptions": {"description": "The list of requested subscriptions, in the same order as the request.", "items": {"$ref": "Subscription"}, "type": "array"}}, "type": "object"}, "BatchMigrateBasePlanPricesRequest": {"description": "Request message for BatchMigrateBasePlanPrices.", "id": "BatchMigrateBasePlanPricesRequest", "properties": {"requests": {"description": "Required. Up to 100 price migration requests. All requests must update different base plans.", "items": {"$ref": "MigrateBasePlanPricesRequest"}, "type": "array"}}, "type": "object"}, "BatchMigrateBasePlanPricesResponse": {"description": "Response message for BatchMigrateBasePlanPrices.", "id": "BatchMigrateBasePlanPricesResponse", "properties": {"responses": {"description": "Contains one response per requested price migration, in the same order as the request.", "items": {"$ref": "MigrateBasePlanPricesResponse"}, "type": "array"}}, "type": "object"}, "BatchUpdateBasePlanStatesRequest": {"description": "Request message for BatchUpdateBasePlanStates.", "id": "BatchUpdateBasePlanStatesRequest", "properties": {"requests": {"description": "Required. The update request list of up to 100 elements. All requests must update different base plans.", "items": {"$ref": "UpdateBasePlanStateRequest"}, "type": "array"}}, "type": "object"}, "BatchUpdateBasePlanStatesResponse": {"description": "Response message for BatchUpdateBasePlanStates.", "id": "BatchUpdateBasePlanStatesResponse", "properties": {"subscriptions": {"description": "The list of updated subscriptions. This list will match the requests one to one, in the same order.", "items": {"$ref": "Subscription"}, "type": "array"}}, "type": "object"}, "BatchUpdateOneTimeProductOfferStatesRequest": {"description": "Request message for BatchUpdateOneTimeProductOfferStates.", "id": "BatchUpdateOneTimeProductOfferStatesRequest", "properties": {"requests": {"description": "Required. The update request list of up to 100 elements. All requests must update different offers.", "items": {"$ref": "UpdateOneTimeProductOfferStateRequest"}, "type": "array"}}, "type": "object"}, "BatchUpdateOneTimeProductOfferStatesResponse": {"description": "Response message for BatchUpdateOneTimeProductOfferStates.", "id": "BatchUpdateOneTimeProductOfferStatesResponse", "properties": {"oneTimeProductOffers": {"description": "The updated one-time product offers list, in the same order as the request.", "items": {"$ref": "OneTimeProductOffer"}, "type": "array"}}, "type": "object"}, "BatchUpdateOneTimeProductOffersRequest": {"description": "Request message for BatchUpdateOneTimeProductOffers.", "id": "BatchUpdateOneTimeProductOffersRequest", "properties": {"requests": {"description": "Required. A list of update requests of up to 100 elements. All requests must update different offers.", "items": {"$ref": "UpdateOneTimeProductOfferRequest"}, "type": "array"}}, "type": "object"}, "BatchUpdateOneTimeProductOffersResponse": {"description": "Response message for BatchUpdateOneTimeProductOffers.", "id": "BatchUpdateOneTimeProductOffersResponse", "properties": {"oneTimeProductOffers": {"description": "The list of updated one-time product offers, in the same order as the request.", "items": {"$ref": "OneTimeProductOffer"}, "type": "array"}}, "type": "object"}, "BatchUpdateOneTimeProductsRequest": {"description": "Request message for BatchUpdateOneTimeProduct.", "id": "BatchUpdateOneTimeProductsRequest", "properties": {"requests": {"description": "Required. A list of update requests of up to 100 elements. All requests must update different one-time products.", "items": {"$ref": "UpdateOneTimeProductRequest"}, "type": "array"}}, "type": "object"}, "BatchUpdateOneTimeProductsResponse": {"description": "Response message for BatchUpdateOneTimeProduct.", "id": "BatchUpdateOneTimeProductsResponse", "properties": {"oneTimeProducts": {"description": "The list of updated one-time products list, in the same order as the request.", "items": {"$ref": "OneTimeProduct"}, "type": "array"}}, "type": "object"}, "BatchUpdatePurchaseOptionStatesRequest": {"description": "Request message for BatchUpdatePurchaseOptionStates.", "id": "BatchUpdatePurchaseOptionStatesRequest", "properties": {"requests": {"description": "Required. The update request list of up to 100 elements. All requests must update different purchase options.", "items": {"$ref": "UpdatePurchaseOptionStateRequest"}, "type": "array"}}, "type": "object"}, "BatchUpdatePurchaseOptionStatesResponse": {"description": "Response message for BatchUpdatePurchaseOptionStates.", "id": "BatchUpdatePurchaseOptionStatesResponse", "properties": {"oneTimeProducts": {"description": "The list of updated one-time products. This list will match the requests one to one, in the same order.", "items": {"$ref": "OneTimeProduct"}, "type": "array"}}, "type": "object"}, "BatchUpdateSubscriptionOfferStatesRequest": {"description": "Request message for BatchUpdateSubscriptionOfferStates.", "id": "BatchUpdateSubscriptionOfferStatesRequest", "properties": {"requests": {"description": "Required. The update request list of up to 100 elements. All requests must update different offers.", "items": {"$ref": "UpdateSubscriptionOfferStateRequest"}, "type": "array"}}, "type": "object"}, "BatchUpdateSubscriptionOfferStatesResponse": {"description": "Response message for BatchUpdateSubscriptionOfferStates.", "id": "BatchUpdateSubscriptionOfferStatesResponse", "properties": {"subscriptionOffers": {"description": "The updated subscription offers list.", "items": {"$ref": "SubscriptionOffer"}, "type": "array"}}, "type": "object"}, "BatchUpdateSubscriptionOffersRequest": {"description": "Request message for BatchUpdateSubscriptionOffers.", "id": "BatchUpdateSubscriptionOffersRequest", "properties": {"requests": {"description": "Required. A list of update requests of up to 100 elements. All requests must update different subscription offers.", "items": {"$ref": "UpdateSubscriptionOfferRequest"}, "type": "array"}}, "type": "object"}, "BatchUpdateSubscriptionOffersResponse": {"description": "Response message for BatchUpdateSubscriptionOffers.", "id": "BatchUpdateSubscriptionOffersResponse", "properties": {"subscriptionOffers": {"description": "The updated subscription offers list.", "items": {"$ref": "SubscriptionOffer"}, "type": "array"}}, "type": "object"}, "BatchUpdateSubscriptionsRequest": {"description": "Request message for BatchUpdateSubscription.", "id": "BatchUpdateSubscriptionsRequest", "properties": {"requests": {"description": "Required. A list of update requests of up to 100 elements. All requests must update different subscriptions.", "items": {"$ref": "UpdateSubscriptionRequest"}, "type": "array"}}, "type": "object"}, "BatchUpdateSubscriptionsResponse": {"description": "Response message for BatchUpdateSubscription.", "id": "BatchUpdateSubscriptionsResponse", "properties": {"subscriptions": {"description": "The updated subscriptions list.", "items": {"$ref": "Subscription"}, "type": "array"}}, "type": "object"}, "Bundle": {"description": "Information about an app bundle. The resource for BundlesService.", "id": "Bundle", "properties": {"sha1": {"description": "A sha1 hash of the upload payload, encoded as a hex string and matching the output of the sha1sum command.", "type": "string"}, "sha256": {"description": "A sha256 hash of the upload payload, encoded as a hex string and matching the output of the sha256sum command.", "type": "string"}, "versionCode": {"description": "The version code of the Android App Bundle, as specified in the Android App Bundle's base module APK manifest file.", "format": "int32", "type": "integer"}}, "type": "object"}, "BundlesListResponse": {"description": "Response listing all app bundles.", "id": "BundlesListResponse", "properties": {"bundles": {"description": "All app bundles.", "items": {"$ref": "Bundle"}, "type": "array"}, "kind": {"description": "The kind of this response (\"androidpublisher#bundlesListResponse\").", "type": "string"}}, "type": "object"}, "BuyerAddress": {"description": "Address information for the customer, for use in tax computation.", "id": "BuyerAddress", "properties": {"buyerCountry": {"description": "Two letter country code based on ISO-3166-1 Alpha-2 (UN country codes).", "type": "string"}, "buyerPostcode": {"description": "Postal code of an address. When Google is the Merchant of Record for the order, this information is not included.", "type": "string"}, "buyerState": {"description": "Top-level administrative subdivision of the buyer address country. When Google is the Merchant of Record for the order, this information is not included.", "type": "string"}}, "type": "object"}, "CancelAppRecoveryRequest": {"description": "Request message for CancelAppRecovery.", "id": "CancelAppRecoveryRequest", "properties": {}, "type": "object"}, "CancelAppRecoveryResponse": {"description": "Response message for CancelAppRecovery.", "id": "CancelAppRecoveryResponse", "properties": {}, "type": "object"}, "CancelOneTimeProductOfferRequest": {"description": "Request message for CancelOneTimeProductOffer.", "id": "CancelOneTimeProductOfferRequest", "properties": {"latencyTolerance": {"description": "Optional. The latency tolerance for the propagation of this update. Defaults to latency-sensitive.", "enum": ["PRODUCT_UPDATE_LATENCY_TOLERANCE_UNSPECIFIED", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT"], "enumDescriptions": ["Defaults to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE.", "The update will propagate to clients within several minutes on average and up to a few hours in rare cases. Throughput is limited to 7,200 updates per app per hour.", "The update will propagate to clients within 24 hours. Supports high throughput of up to 720,000 updates per app per hour using batch modification methods."], "type": "string"}, "offerId": {"description": "Required. The offer ID of the offer to cancel.", "type": "string"}, "packageName": {"description": "Required. The parent app (package name) of the offer to cancel.", "type": "string"}, "productId": {"description": "Required. The parent one-time product (ID) of the offer to cancel.", "type": "string"}, "purchaseOptionId": {"description": "Required. The parent purchase option (ID) of the offer to cancel.", "type": "string"}}, "type": "object"}, "CancelSurveyResult": {"description": "Result of the cancel survey when the subscription was canceled by the user.", "id": "CancelSurveyResult", "properties": {"reason": {"description": "The reason the user selected in the cancel survey.", "enum": ["CANCEL_SURVEY_REASON_UNSPECIFIED", "CANCEL_SURVEY_REASON_NOT_ENOUGH_USAGE", "CANCEL_SURVEY_REASON_TECHNICAL_ISSUES", "CANCEL_SURVEY_REASON_COST_RELATED", "CANCEL_SURVEY_REASON_FOUND_BETTER_APP", "CANCEL_SURVEY_REASON_OTHERS"], "enumDescriptions": ["Unspecified cancel survey reason.", "Not enough usage of the subscription.", "Technical issues while using the app.", "Cost related issues.", "The user found a better app.", "Other reasons."], "type": "string"}, "reasonUserInput": {"description": "Only set for CANCEL_SURVEY_REASON_OTHERS. This is the user's freeform response to the survey.", "type": "string"}}, "type": "object"}, "CanceledStateContext": {"description": "Information specific to a subscription in the SUBSCRIPTION_STATE_CANCELED or SUBSCRIPTION_STATE_EXPIRED state.", "id": "CanceledStateContext", "properties": {"developerInitiatedCancellation": {"$ref": "DeveloperInitiatedCancellation", "description": "Subscription was canceled by the developer."}, "replacementCancellation": {"$ref": "ReplacementCancellation", "description": "Subscription was replaced by a new subscription."}, "systemInitiatedCancellation": {"$ref": "SystemInitiatedCancellation", "description": "Subscription was canceled by the system, for example because of a billing problem."}, "userInitiatedCancellation": {"$ref": "UserInitiatedCancellation", "description": "Subscription was canceled by user."}}, "type": "object"}, "CancellationEvent": {"description": "Details of when the order was canceled.", "id": "CancellationEvent", "properties": {"eventTime": {"description": "The time when the order was canceled.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "Comment": {"description": "An entry of conversation between user and developer.", "id": "Comment", "properties": {"developerComment": {"$ref": "DeveloperComment", "description": "A comment from a developer."}, "userComment": {"$ref": "UserComment", "description": "A comment from a user."}}, "type": "object"}, "ConvertRegionPricesRequest": {"description": "Request message for ConvertRegionPrices.", "id": "ConvertRegionPricesRequest", "properties": {"price": {"$ref": "Money", "description": "The intital price to convert other regions from. Tax exclusive."}}, "type": "object"}, "ConvertRegionPricesResponse": {"description": "Response message for ConvertRegionPrices.", "id": "ConvertRegionPricesResponse", "properties": {"convertedOtherRegionsPrice": {"$ref": "ConvertedOtherRegionsPrice", "description": "Converted other regions prices in USD and EUR, to use for countries where Play doesn't support a country's local currency."}, "convertedRegionPrices": {"additionalProperties": {"$ref": "ConvertedRegionPrice"}, "description": "Map from region code to converted region price.", "type": "object"}, "regionVersion": {"$ref": "RegionsVersion", "description": "The region version at which the prices were generated."}}, "type": "object"}, "ConvertedOtherRegionsPrice": {"description": "Converted other regions prices.", "id": "ConvertedOtherRegionsPrice", "properties": {"eurPrice": {"$ref": "Money", "description": "Price in EUR to use for the \"Other regions\" location exclusive of taxes."}, "usdPrice": {"$ref": "Money", "description": "Price in USD to use for the \"Other regions\" location exclusive of taxes."}}, "type": "object"}, "ConvertedRegionPrice": {"description": "A converted region price.", "id": "ConvertedRegionPrice", "properties": {"price": {"$ref": "Money", "description": "The converted price tax inclusive."}, "regionCode": {"description": "The region code of the region.", "type": "string"}, "taxAmount": {"$ref": "Money", "description": "The tax amount of the converted price."}}, "type": "object"}, "CountryTargeting": {"description": "Country targeting specification.", "id": "CountryTargeting", "properties": {"countries": {"description": "Countries to target, specified as two letter [CLDR codes](https://unicode.org/cldr/charts/latest/supplemental/territory_containment_un_m_49.html).", "items": {"type": "string"}, "type": "array"}, "includeRestOfWorld": {"description": "Include \"rest of world\" as well as explicitly targeted countries.", "type": "boolean"}}, "type": "object"}, "CreateDraftAppRecoveryRequest": {"description": "Request message for CreateDraftAppRecovery.", "id": "CreateDraftAppRecoveryRequest", "properties": {"remoteInAppUpdate": {"$ref": "RemoteInAppUpdate", "description": "Action type is remote in-app update. As a consequence of this action, a downloadable recovery module is also created for testing purposes."}, "targeting": {"$ref": "Targeting", "description": "Specifies targeting criteria for the recovery action such as regions, android sdk versions, app versions etc."}}, "type": "object"}, "DeactivateBasePlanRequest": {"description": "Request message for DeactivateBasePlan.", "id": "DeactivateBasePlanRequest", "properties": {"basePlanId": {"description": "Required. The unique base plan ID of the base plan to deactivate.", "type": "string"}, "latencyTolerance": {"description": "Optional. The latency tolerance for the propagation of this product update. Defaults to latency-sensitive.", "enum": ["PRODUCT_UPDATE_LATENCY_TOLERANCE_UNSPECIFIED", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT"], "enumDescriptions": ["Defaults to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE.", "The update will propagate to clients within several minutes on average and up to a few hours in rare cases. Throughput is limited to 7,200 updates per app per hour.", "The update will propagate to clients within 24 hours. Supports high throughput of up to 720,000 updates per app per hour using batch modification methods."], "type": "string"}, "packageName": {"description": "Required. The parent app (package name) of the base plan to deactivate.", "type": "string"}, "productId": {"description": "Required. The parent subscription (ID) of the base plan to deactivate.", "type": "string"}}, "type": "object"}, "DeactivateOneTimeProductOfferRequest": {"description": "Request message for DeactivateOneTimeProductOffer.", "id": "DeactivateOneTimeProductOfferRequest", "properties": {"latencyTolerance": {"description": "Optional. The latency tolerance for the propagation of this update. Defaults to latency-sensitive.", "enum": ["PRODUCT_UPDATE_LATENCY_TOLERANCE_UNSPECIFIED", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT"], "enumDescriptions": ["Defaults to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE.", "The update will propagate to clients within several minutes on average and up to a few hours in rare cases. Throughput is limited to 7,200 updates per app per hour.", "The update will propagate to clients within 24 hours. Supports high throughput of up to 720,000 updates per app per hour using batch modification methods."], "type": "string"}, "offerId": {"description": "Required. The offer ID of the offer to deactivate.", "type": "string"}, "packageName": {"description": "Required. The parent app (package name) of the offer to deactivate.", "type": "string"}, "productId": {"description": "Required. The parent one-time product (ID) of the offer to deactivate.", "type": "string"}, "purchaseOptionId": {"description": "Required. The parent purchase option (ID) of the offer to deactivate.", "type": "string"}}, "type": "object"}, "DeactivatePurchaseOptionRequest": {"description": "Request message for UpdatePurchaseOptionState.", "id": "DeactivatePurchaseOptionRequest", "properties": {"latencyTolerance": {"description": "Optional. The latency tolerance for the propagation of this product update. Defaults to latency-sensitive.", "enum": ["PRODUCT_UPDATE_LATENCY_TOLERANCE_UNSPECIFIED", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT"], "enumDescriptions": ["Defaults to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE.", "The update will propagate to clients within several minutes on average and up to a few hours in rare cases. Throughput is limited to 7,200 updates per app per hour.", "The update will propagate to clients within 24 hours. Supports high throughput of up to 720,000 updates per app per hour using batch modification methods."], "type": "string"}, "packageName": {"description": "Required. The parent app (package name) of the purchase option to deactivate.", "type": "string"}, "productId": {"description": "Required. The parent one-time product (ID) of the purchase option to deactivate.", "type": "string"}, "purchaseOptionId": {"description": "Required. The purchase option ID of the purchase option to deactivate.", "type": "string"}}, "type": "object"}, "DeactivateSubscriptionOfferRequest": {"description": "Request message for DeactivateSubscriptionOffer.", "id": "DeactivateSubscriptionOfferRequest", "properties": {"basePlanId": {"description": "Required. The parent base plan (ID) of the offer to deactivate.", "type": "string"}, "latencyTolerance": {"description": "Optional. The latency tolerance for the propagation of this product update. Defaults to latency-sensitive.", "enum": ["PRODUCT_UPDATE_LATENCY_TOLERANCE_UNSPECIFIED", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT"], "enumDescriptions": ["Defaults to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE.", "The update will propagate to clients within several minutes on average and up to a few hours in rare cases. Throughput is limited to 7,200 updates per app per hour.", "The update will propagate to clients within 24 hours. Supports high throughput of up to 720,000 updates per app per hour using batch modification methods."], "type": "string"}, "offerId": {"description": "Required. The unique offer ID of the offer to deactivate.", "type": "string"}, "packageName": {"description": "Required. The parent app (package name) of the offer to deactivate.", "type": "string"}, "productId": {"description": "Required. The parent subscription (ID) of the offer to deactivate.", "type": "string"}}, "type": "object"}, "DeferredItemRemoval": {"description": "Information related to deferred item replacement.", "id": "DeferredItemRemoval", "properties": {}, "type": "object"}, "DeferredItemReplacement": {"description": "Information related to deferred item replacement.", "id": "DeferredItemReplacement", "properties": {"productId": {"description": "The product_id going to replace the existing product_id.", "type": "string"}}, "type": "object"}, "DeleteOneTimeProductOfferRequest": {"description": "Request message for deleting an one-time product offer.", "id": "DeleteOneTimeProductOfferRequest", "properties": {"latencyTolerance": {"description": "Optional. The latency tolerance for the propagation of this product update. Defaults to latency-sensitive.", "enum": ["PRODUCT_UPDATE_LATENCY_TOLERANCE_UNSPECIFIED", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT"], "enumDescriptions": ["Defaults to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE.", "The update will propagate to clients within several minutes on average and up to a few hours in rare cases. Throughput is limited to 7,200 updates per app per hour.", "The update will propagate to clients within 24 hours. Supports high throughput of up to 720,000 updates per app per hour using batch modification methods."], "type": "string"}, "offerId": {"description": "Required. The unique offer ID of the offer to delete.", "type": "string"}, "packageName": {"description": "Required. The parent app (package name) of the offer to delete.", "type": "string"}, "productId": {"description": "Required. The parent one-time product (ID) of the offer to delete.", "type": "string"}, "purchaseOptionId": {"description": "Required. The parent purchase option (ID) of the offer to delete.", "type": "string"}}, "type": "object"}, "DeleteOneTimeProductRequest": {"description": "Request message for deleting a one-time product.", "id": "DeleteOneTimeProductRequest", "properties": {"latencyTolerance": {"description": "Optional. The latency tolerance for the propagation of this product update. Defaults to latency-sensitive.", "enum": ["PRODUCT_UPDATE_LATENCY_TOLERANCE_UNSPECIFIED", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT"], "enumDescriptions": ["Defaults to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE.", "The update will propagate to clients within several minutes on average and up to a few hours in rare cases. Throughput is limited to 7,200 updates per app per hour.", "The update will propagate to clients within 24 hours. Supports high throughput of up to 720,000 updates per app per hour using batch modification methods."], "type": "string"}, "packageName": {"description": "Required. The parent app (package name) of the one-time product to delete.", "type": "string"}, "productId": {"description": "Required. The one-time product ID of the one-time product to delete.", "type": "string"}}, "type": "object"}, "DeletePurchaseOptionRequest": {"description": "Request message for deleting a purchase option.", "id": "DeletePurchaseOptionRequest", "properties": {"force": {"description": "Optional. This field has no effect for purchase options with no offers under them. For purchase options with associated offers: * If `force` is set to false (default), an error will be returned. * If `force` is set to true, any associated offers under the purchase option will be deleted.", "type": "boolean"}, "latencyTolerance": {"description": "Optional. The latency tolerance for the propagation of this product update. Defaults to latency-sensitive.", "enum": ["PRODUCT_UPDATE_LATENCY_TOLERANCE_UNSPECIFIED", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT"], "enumDescriptions": ["Defaults to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE.", "The update will propagate to clients within several minutes on average and up to a few hours in rare cases. Throughput is limited to 7,200 updates per app per hour.", "The update will propagate to clients within 24 hours. Supports high throughput of up to 720,000 updates per app per hour using batch modification methods."], "type": "string"}, "packageName": {"description": "Required. The parent app (package name) of the purchase option to delete.", "type": "string"}, "productId": {"description": "Required. The parent one-time product (ID) of the purchase option to delete.", "type": "string"}, "purchaseOptionId": {"description": "Required. The purchase option ID of the purchase option to delete.", "type": "string"}}, "type": "object"}, "DeobfuscationFile": {"description": "Represents a deobfuscation file.", "id": "DeobfuscationFile", "properties": {"symbolType": {"description": "The type of the deobfuscation file.", "enum": ["deobfuscationFileTypeUnspecified", "proguard", "nativeCode"], "enumDescriptions": ["Unspecified deobfuscation file type.", "Proguard deobfuscation file type.", "Native debugging symbols file type."], "type": "string"}}, "type": "object"}, "DeobfuscationFilesUploadResponse": {"description": "Responses for the upload.", "id": "DeobfuscationFilesUploadResponse", "properties": {"deobfuscationFile": {"$ref": "DeobfuscationFile", "description": "The uploaded Deobfuscation File configuration."}}, "type": "object"}, "DeployAppRecoveryRequest": {"description": "Request message for DeployAppRecovery.", "id": "DeployAppRecoveryRequest", "properties": {}, "type": "object"}, "DeployAppRecoveryResponse": {"description": "Response message for DeployAppRecovery.", "id": "DeployAppRecoveryResponse", "properties": {}, "type": "object"}, "DeveloperComment": {"description": "Developer entry from conversation between user and developer.", "id": "DeveloperComment", "properties": {"lastModified": {"$ref": "Timestamp", "description": "The last time at which this comment was updated."}, "text": {"description": "The content of the comment, i.e. reply body.", "type": "string"}}, "type": "object"}, "DeveloperInitiatedCancellation": {"description": "Information specific to cancellations initiated by developers.", "id": "DeveloperInitiatedCancellation", "properties": {}, "type": "object"}, "DeviceFeature": {"description": "Represents a device feature.", "id": "DeviceFeature", "properties": {"featureName": {"description": "Name of the feature.", "type": "string"}, "featureVersion": {"description": "The feature version specified by android:glEsVersion or android:version in in the AndroidManifest.", "format": "int32", "type": "integer"}}, "type": "object"}, "DeviceFeatureTargeting": {"description": "Targeting for a device feature.", "id": "DeviceFeatureTargeting", "properties": {"requiredFeature": {"$ref": "DeviceFeature", "description": "Feature of the device."}}, "type": "object"}, "DeviceGroup": {"description": "A group of devices. A group is defined by a set of device selectors. A device belongs to the group if it matches any selector (logical OR).", "id": "DeviceGroup", "properties": {"deviceSelectors": {"description": "Device selectors for this group. A device matching any of the selectors is included in this group.", "items": {"$ref": "DeviceSelector"}, "type": "array"}, "name": {"description": "The name of the group.", "type": "string"}}, "type": "object"}, "DeviceId": {"description": "Identifier of a device.", "id": "DeviceId", "properties": {"buildBrand": {"description": "Value of Build.BRAND.", "type": "string"}, "buildDevice": {"description": "Value of Build.DEVICE.", "type": "string"}}, "type": "object"}, "DeviceMetadata": {"description": "Characteristics of the user's device.", "id": "DeviceMetadata", "properties": {"cpuMake": {"description": "Device CPU make, e.g. \"Qualcomm\"", "type": "string"}, "cpuModel": {"description": "Device CPU model, e.g. \"MSM8974\"", "type": "string"}, "deviceClass": {"description": "Device class (e.g. tablet)", "type": "string"}, "glEsVersion": {"description": "OpenGL version", "format": "int32", "type": "integer"}, "manufacturer": {"description": "Device manufacturer (e.g. Motorola)", "type": "string"}, "nativePlatform": {"description": "Comma separated list of native platforms (e.g. \"arm\", \"arm7\")", "type": "string"}, "productName": {"description": "Device model name (e.g. Droid)", "type": "string"}, "ramMb": {"description": "Device RAM in Megabytes, e.g. \"2048\"", "format": "int32", "type": "integer"}, "screenDensityDpi": {"description": "Screen density in DPI", "format": "int32", "type": "integer"}, "screenHeightPx": {"description": "Screen height in pixels", "format": "int32", "type": "integer"}, "screenWidthPx": {"description": "Screen width in pixels", "format": "int32", "type": "integer"}}, "type": "object"}, "DeviceRam": {"description": "Conditions about a device's RAM capabilities.", "id": "DeviceRam", "properties": {"maxBytes": {"description": "Maximum RAM in bytes (bound excluded).", "format": "int64", "type": "string"}, "minBytes": {"description": "Minimum RAM in bytes (bound included).", "format": "int64", "type": "string"}}, "type": "object"}, "DeviceSelector": {"description": "Selector for a device group. A selector consists of a set of conditions on the device that should all match (logical AND) to determine a device group eligibility. For instance, if a selector specifies RAM conditions, device model inclusion and device model exclusion, a device is considered to match if: device matches RAM conditions AND device matches one of the included device models AND device doesn't match excluded device models", "id": "DeviceSelector", "properties": {"deviceRam": {"$ref": "DeviceRam", "description": "Conditions on the device's RAM."}, "excludedDeviceIds": {"description": "Device models excluded by this selector, even if they match all other conditions.", "items": {"$ref": "DeviceId"}, "type": "array"}, "forbiddenSystemFeatures": {"description": "A device that has any of these system features is excluded by this selector, even if it matches all other conditions.", "items": {"$ref": "SystemFeature"}, "type": "array"}, "includedDeviceIds": {"description": "Device models included by this selector.", "items": {"$ref": "DeviceId"}, "type": "array"}, "requiredSystemFeatures": {"description": "A device needs to have all these system features to be included by the selector.", "items": {"$ref": "SystemFeature"}, "type": "array"}, "systemOnChips": {"description": "Optional. The SoCs included by this selector. Only works for Android S+ devices.", "items": {"$ref": "SystemOnChip"}, "type": "array"}}, "type": "object"}, "DeviceSpec": {"description": "The device spec used to generate a system APK.", "id": "DeviceSpec", "properties": {"screenDensity": {"description": "Screen dpi.", "format": "uint32", "type": "integer"}, "supportedAbis": {"description": "Supported ABI architectures in the order of preference. The values should be the string as reported by the platform, e.g. \"armeabi-v7a\", \"x86_64\".", "items": {"type": "string"}, "type": "array"}, "supportedLocales": {"description": "All installed locales represented as BCP-47 strings, e.g. \"en-US\".", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "DeviceTier": {"description": "A single device tier. Devices matching any of the device groups in device_group_names are considered to match the tier.", "id": "DeviceTier", "properties": {"deviceGroupNames": {"description": "Groups of devices included in this tier. These groups must be defined explicitly under device_groups in this configuration.", "items": {"type": "string"}, "type": "array"}, "level": {"description": "The priority level of the tier. Tiers are evaluated in descending order of level: the highest level tier has the highest priority. The highest tier matching a given device is selected for that device. You should use a contiguous range of levels for your tiers in a tier set; tier levels in a tier set must be unique. For instance, if your tier set has 4 tiers (including the global fallback), you should define tiers 1, 2 and 3 in this configuration. Note: tier 0 is implicitly defined as a global fallback and selected for devices that don't match any of the tiers explicitly defined here. You mustn't define level 0 explicitly in this configuration.", "format": "int32", "type": "integer"}}, "type": "object"}, "DeviceTierConfig": {"description": "Configuration describing device targeting criteria for the content of an app.", "id": "DeviceTierConfig", "properties": {"deviceGroups": {"description": "Definition of device groups for the app.", "items": {"$ref": "DeviceGroup"}, "type": "array"}, "deviceTierConfigId": {"description": "Output only. The device tier config ID.", "format": "int64", "readOnly": true, "type": "string"}, "deviceTierSet": {"$ref": "DeviceTierSet", "description": "Definition of the set of device tiers for the app."}, "userCountrySets": {"description": "Definition of user country sets for the app.", "items": {"$ref": "UserCountrySet"}, "type": "array"}}, "type": "object"}, "DeviceTierSet": {"description": "A set of device tiers. A tier set determines what variation of app content gets served to a specific device, for device-targeted content. You should assign a priority level to each tier, which determines the ordering by which they are evaluated by Play. See the documentation of DeviceTier.level for more details.", "id": "DeviceTierSet", "properties": {"deviceTiers": {"description": "Device tiers belonging to the set.", "items": {"$ref": "DeviceTier"}, "type": "array"}}, "type": "object"}, "ExpansionFile": {"description": "An expansion file. The resource for ExpansionFilesService.", "id": "ExpansionFile", "properties": {"fileSize": {"description": "If set, this field indicates that this APK has an expansion file uploaded to it: this APK does not reference another APK's expansion file. The field's value is the size of the uploaded expansion file in bytes.", "format": "int64", "type": "string"}, "referencesVersion": {"description": "If set, this APK's expansion file references another APK's expansion file. The file_size field will not be set.", "format": "int32", "type": "integer"}}, "type": "object"}, "ExpansionFilesUploadResponse": {"description": "Response for uploading an expansion file.", "id": "ExpansionFilesUploadResponse", "properties": {"expansionFile": {"$ref": "ExpansionFile", "description": "The uploaded expansion file configuration."}}, "type": "object"}, "ExternalAccountIdentifiers": {"description": "User account identifier in the third-party service.", "id": "ExternalAccountIdentifiers", "properties": {"externalAccountId": {"description": "User account identifier in the third-party service. Only present if account linking happened as part of the subscription purchase flow.", "type": "string"}, "obfuscatedExternalAccountId": {"description": "An obfuscated version of the id that is uniquely associated with the user's account in your app. Present for the following purchases: * If account linking happened as part of the subscription purchase flow. * It was specified using https://developer.android.com/reference/com/android/billingclient/api/BillingFlowParams.Builder#setobfuscatedaccountid when the purchase was made.", "type": "string"}, "obfuscatedExternalProfileId": {"description": "An obfuscated version of the id that is uniquely associated with the user's profile in your app. Only present if specified using https://developer.android.com/reference/com/android/billingclient/api/BillingFlowParams.Builder#setobfuscatedprofileid when the purchase was made.", "type": "string"}}, "type": "object"}, "ExternalSubscription": {"description": "Details of an external subscription.", "id": "ExternalSubscription", "properties": {"subscriptionType": {"description": "Required. The type of the external subscription.", "enum": ["SUBSCRIPTION_TYPE_UNSPECIFIED", "RECURRING", "PREPAID"], "enumDescriptions": ["Unspecified, do not use.", "This is a recurring subscription where the user is charged every billing cycle.", "This is a prepaid subscription where the user pays up front."], "type": "string"}}, "type": "object"}, "ExternalTransaction": {"description": "The details of an external transaction.", "id": "ExternalTransaction", "properties": {"createTime": {"description": "Output only. The time when this transaction was created. This is the time when Google was notified of the transaction.", "format": "google-datetime", "readOnly": true, "type": "string"}, "currentPreTaxAmount": {"$ref": "Price", "description": "Output only. The current transaction amount before tax. This represents the current pre-tax amount including any refunds that may have been applied to this transaction.", "readOnly": true}, "currentTaxAmount": {"$ref": "Price", "description": "Output only. The current tax amount. This represents the current tax amount including any refunds that may have been applied to this transaction.", "readOnly": true}, "externalTransactionId": {"description": "Output only. The id of this transaction. All transaction ids under the same package name must be unique. Set when creating the external transaction.", "readOnly": true, "type": "string"}, "oneTimeTransaction": {"$ref": "OneTimeExternalTransaction", "description": "This is a one-time transaction and not part of a subscription."}, "originalPreTaxAmount": {"$ref": "Price", "description": "Required. The original transaction amount before taxes. This represents the pre-tax amount originally notified to Google before any refunds were applied."}, "originalTaxAmount": {"$ref": "Price", "description": "Required. The original tax amount. This represents the tax amount originally notified to Google before any refunds were applied."}, "packageName": {"description": "Output only. The resource name of the external transaction. The package name of the application the inapp products were sold (for example, 'com.some.app').", "readOnly": true, "type": "string"}, "recurringTransaction": {"$ref": "RecurringExternalTransaction", "description": "This transaction is part of a recurring series of transactions."}, "testPurchase": {"$ref": "ExternalTransactionTestPurchase", "description": "Output only. If set, this transaction was a test purchase. Google will not charge for a test transaction.", "readOnly": true}, "transactionProgramCode": {"description": "Optional. The transaction program code, used to help determine service fee for eligible apps participating in partner programs. Developers participating in the Play Media Experience Program (https://play.google.com/console/about/programs/mediaprogram/) must provide the program code when reporting alternative billing transactions. If you are an eligible developer, please contact your BDM for more information on how to set this field. Note: this field can not be used for external offers transactions.", "format": "int32", "type": "integer"}, "transactionState": {"description": "Output only. The current state of the transaction.", "enum": ["TRANSACTION_STATE_UNSPECIFIED", "TRANSACTION_REPORTED", "TRANSACTION_CANCELED"], "enumDescriptions": ["Unspecified transaction state. Not used.", "The transaction has been successfully reported to Google.", "The transaction has been fully refunded."], "readOnly": true, "type": "string"}, "transactionTime": {"description": "Required. The time when the transaction was completed.", "format": "google-datetime", "type": "string"}, "userTaxAddress": {"$ref": "ExternalTransactionAddress", "description": "Required. User address for tax computation."}}, "type": "object"}, "ExternalTransactionAddress": {"description": "User's address for the external transaction.", "id": "ExternalTransactionAddress", "properties": {"administrativeArea": {"description": "Optional. Top-level administrative subdivision of the country/region. Only required for transactions in India. Valid values are \"<PERSON><PERSON><PERSON> AND <PERSON><PERSON>OBAR ISLANDS\", \"ANDHRA PRADESH\", \"ARUNACHAL PRADESH\", \"ASSAM\", \"<PERSON>I<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\", \"DAD<PERSON> AND NAGAR HAVELI\", \"DAD<PERSON> AND NAGAR HAVELI AND DAMAN AND DIU\", \"D<PERSON><PERSON> AND DIU\", \"DELHI\", \"GOA\", \"G<PERSON><PERSON><PERSON><PERSON>\", \"HARYANA\", \"HIMACHAL PRADESH\", \"<PERSON><PERSON><PERSON> AND <PERSON><PERSON>HMIR\", \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", \"KERALA\", \"LADAKH\", \"<PERSON><PERSON><PERSON><PERSON>WEE<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON> PRADESH\", \"<PERSON><PERSON><PERSON>S<PERSON><PERSON>\", \"MANIPUR\", \"MEGHALAYA\", \"<PERSON><PERSON><PERSON><PERSON>\", \"NAGALAND\", \"ODISHA\", \"PUDUCHERRY\", \"PUNJAB\", \"RAJASTHAN\", \"SIK<PERSON><PERSON>\", \"TAMIL NADU\", \"TELANGANA\", \"TRIPURA\", \"UTT<PERSON> PRADESH\", \"UTTARAKHAND\", and \"WEST BENGAL\".", "type": "string"}, "regionCode": {"description": "Required. Two letter region code based on ISO-3166-1 Alpha-2 (UN region codes).", "type": "string"}}, "type": "object"}, "ExternalTransactionTestPurchase": {"description": "Represents a transaction performed using a test account. These transactions will not be charged by Google.", "id": "ExternalTransactionTestPurchase", "properties": {}, "type": "object"}, "ExternallyHostedApk": {"description": "Defines an APK available for this application that is hosted externally and not uploaded to Google Play. This function is only available to organizations using Managed Play whose application is configured to restrict distribution to the organizations.", "id": "ExternallyHostedApk", "properties": {"applicationLabel": {"description": "The application label.", "type": "string"}, "certificateBase64s": {"description": "A certificate (or array of certificates if a certificate-chain is used) used to sign this APK, represented as a base64 encoded byte array.", "items": {"type": "string"}, "type": "array"}, "externallyHostedUrl": {"description": "The URL at which the APK is hosted. This must be an https URL.", "type": "string"}, "fileSha1Base64": {"description": "The sha1 checksum of this APK, represented as a base64 encoded byte array.", "type": "string"}, "fileSha256Base64": {"description": "The sha256 checksum of this APK, represented as a base64 encoded byte array.", "type": "string"}, "fileSize": {"description": "The file size in bytes of this APK.", "format": "int64", "type": "string"}, "iconBase64": {"description": "The icon image from the APK, as a base64 encoded byte array.", "type": "string"}, "maximumSdk": {"description": "The maximum SDK supported by this APK (optional).", "format": "int32", "type": "integer"}, "minimumSdk": {"description": "The minimum SDK targeted by this APK.", "format": "int32", "type": "integer"}, "nativeCodes": {"description": "The native code environments supported by this APK (optional).", "items": {"type": "string"}, "type": "array"}, "packageName": {"description": "The package name.", "type": "string"}, "usesFeatures": {"description": "The features required by this APK (optional).", "items": {"type": "string"}, "type": "array"}, "usesPermissions": {"description": "The permissions requested by this APK.", "items": {"$ref": "UsesPermission"}, "type": "array"}, "versionCode": {"description": "The version code of this APK.", "format": "int32", "type": "integer"}, "versionName": {"description": "The version name of this APK.", "type": "string"}}, "type": "object"}, "FullRefund": {"description": "A full refund of the remaining amount of a transaction.", "id": "FullRefund", "properties": {}, "type": "object"}, "GeneratedApksListResponse": {"description": "Response to list generated APKs.", "id": "GeneratedApksListResponse", "properties": {"generatedApks": {"description": "All generated APKs, grouped by the APK signing key.", "items": {"$ref": "GeneratedApksPerSigningKey"}, "type": "array"}}, "type": "object"}, "GeneratedApksPerSigningKey": {"description": "Download metadata for split, standalone and universal APKs, as well as asset pack slices, signed with a given key.", "id": "GeneratedApksPerSigningKey", "properties": {"certificateSha256Hash": {"description": "SHA256 hash of the APK signing public key certificate.", "type": "string"}, "generatedAssetPackSlices": {"description": "List of asset pack slices which will be served for this app bundle, signed with a key corresponding to certificate_sha256_hash.", "items": {"$ref": "GeneratedAssetPackSlice"}, "type": "array"}, "generatedRecoveryModules": {"description": "Generated recovery apks for recovery actions signed with a key corresponding to certificate_sha256_hash. This includes all generated recovery APKs, also those in draft or cancelled state. This field is not set if no recovery actions were created for this signing key.", "items": {"$ref": "GeneratedRecoveryApk"}, "type": "array"}, "generatedSplitApks": {"description": "List of generated split APKs, signed with a key corresponding to certificate_sha256_hash.", "items": {"$ref": "GeneratedSplitApk"}, "type": "array"}, "generatedStandaloneApks": {"description": "List of generated standalone APKs, signed with a key corresponding to certificate_sha256_hash.", "items": {"$ref": "GeneratedStandaloneApk"}, "type": "array"}, "generatedUniversalApk": {"$ref": "GeneratedUniversalApk", "description": "Generated universal APK, signed with a key corresponding to certificate_sha256_hash. This field is not set if no universal APK was generated for this signing key."}, "targetingInfo": {"$ref": "TargetingInfo", "description": "Contains targeting information about the generated apks."}}, "type": "object"}, "GeneratedAssetPackSlice": {"description": "Download metadata for an asset pack slice.", "id": "GeneratedAssetPackSlice", "properties": {"downloadId": {"description": "Download ID, which uniquely identifies the APK to download. Should be supplied to `generatedapks.download` method.", "type": "string"}, "moduleName": {"description": "Name of the module that this asset slice belongs to.", "type": "string"}, "sliceId": {"description": "Asset slice ID.", "type": "string"}, "version": {"description": "Asset module version.", "format": "int64", "type": "string"}}, "type": "object"}, "GeneratedRecoveryApk": {"description": "Download metadata for an app recovery module.", "id": "GeneratedRecoveryApk", "properties": {"downloadId": {"description": "Download ID, which uniquely identifies the APK to download. Should be supplied to `generatedapks.download` method.", "type": "string"}, "moduleName": {"description": "Name of the module which recovery apk belongs to.", "type": "string"}, "recoveryId": {"description": "ID of the recovery action.", "format": "int64", "type": "string"}, "recoveryStatus": {"description": "The status of the recovery action corresponding to the recovery apk.", "enum": ["RECOVERY_STATUS_UNSPECIFIED", "RECOVERY_STATUS_ACTIVE", "RECOVERY_STATUS_CANCELED", "RECOVERY_STATUS_DRAFT", "RECOVERY_STATUS_GENERATION_IN_PROGRESS", "RECOVERY_STATUS_GENERATION_FAILED"], "enumDescriptions": ["RecoveryStatus is unspecified.", "The app recovery action has not been canceled since it has been created.", "The recovery action has been canceled. The action cannot be resumed.", "The recovery action is in the draft state and has not yet been deployed to users.", "The recovery action is generating recovery apks.", "The app recovery action generation has failed."], "type": "string"}}, "type": "object"}, "GeneratedSplitApk": {"description": "Download metadata for a split APK.", "id": "GeneratedSplitApk", "properties": {"downloadId": {"description": "Download ID, which uniquely identifies the APK to download. Should be supplied to `generatedapks.download` method.", "type": "string"}, "moduleName": {"description": "Name of the module that this APK belongs to.", "type": "string"}, "splitId": {"description": "Split ID. Empty for the main split of the base module.", "type": "string"}, "variantId": {"description": "ID of the generated variant.", "format": "int32", "type": "integer"}}, "type": "object"}, "GeneratedStandaloneApk": {"description": "Download metadata for a standalone APK.", "id": "GeneratedStandaloneApk", "properties": {"downloadId": {"description": "Download ID, which uniquely identifies the APK to download. Should be supplied to `generatedapks.download` method.", "type": "string"}, "variantId": {"description": "ID of the generated variant.", "format": "int32", "type": "integer"}}, "type": "object"}, "GeneratedUniversalApk": {"description": "Download metadata for a universal APK.", "id": "GeneratedUniversalApk", "properties": {"downloadId": {"description": "Download ID, which uniquely identifies the APK to download. Should be supplied to `generatedapks.download` method.", "type": "string"}}, "type": "object"}, "GetOneTimeProductOfferRequest": {"description": "Request message for GetOneTimeProductOffers.", "id": "GetOneTimeProductOfferRequest", "properties": {"offerId": {"description": "Required. The unique offer ID of the offer to get.", "type": "string"}, "packageName": {"description": "Required. The parent app (package name) of the offer to get.", "type": "string"}, "productId": {"description": "Required. The parent one-time product (ID) of the offer to get.", "type": "string"}, "purchaseOptionId": {"description": "Required. The parent purchase option (ID) of the offer to get.", "type": "string"}}, "type": "object"}, "GetSubscriptionOfferRequest": {"description": "Request message for GetSubscriptionOffer.", "id": "GetSubscriptionOfferRequest", "properties": {"basePlanId": {"description": "Required. The parent base plan (ID) of the offer to get.", "type": "string"}, "offerId": {"description": "Required. The unique offer ID of the offer to get.", "type": "string"}, "packageName": {"description": "Required. The parent app (package name) of the offer to get.", "type": "string"}, "productId": {"description": "Required. The parent subscription (ID) of the offer to get.", "type": "string"}}, "type": "object"}, "Grant": {"description": "An access grant resource.", "id": "<PERSON>", "properties": {"appLevelPermissions": {"description": "The permissions granted to the user for this app.", "items": {"enum": ["APP_LEVEL_PERMISSION_UNSPECIFIED", "CAN_ACCESS_APP", "CAN_VIEW_FINANCIAL_DATA", "CAN_MANAGE_PERMISSIONS", "CAN_REPLY_TO_REVIEWS", "CAN_MANAGE_PUBLIC_APKS", "CAN_MANAGE_TRACK_APKS", "CAN_MANAGE_TRACK_USERS", "CAN_MANAGE_PUBLIC_LISTING", "CAN_MANAGE_DRAFT_APPS", "CAN_MANAGE_ORDERS", "CAN_MANAGE_APP_CONTENT", "CAN_VIEW_NON_FINANCIAL_DATA", "CAN_VIEW_APP_QUALITY", "CAN_MANAGE_DEEPLINKS"], "enumDeprecated": [false, true, false, false, false, false, false, false, false, false, false, false, false, false, false], "enumDescriptions": ["Unknown or unspecified permission.", "View app information (read-only). Deprecated: Try defining a more granular capability. Otherwise, check AppLevelPermission.CAN_VIEW_NON_FINANCIAL_DATA.", "View financial data.", "Admin (all permissions).", "Reply to reviews.", "Release to production, exclude devices, and use app signing by Google Play.", "Release to testing tracks.", "Manage testing tracks and edit tester lists.", "Manage store presence.", "Edit and delete draft apps.", "Manage orders and subscriptions.", "Manage policy related pages.", "View app information (read-only).", "View app quality data such as Vitals, Crashes etc.", "Manage the deep links setup of an app."], "type": "string"}, "type": "array"}, "name": {"description": "Required. Resource name for this grant, following the pattern \"developers/{developer}/users/{email}/grants/{package_name}\". If this grant is for a draft app, the app ID will be used in this resource name instead of the package name.", "type": "string"}, "packageName": {"description": "Immutable. The package name of the app. This will be empty for draft apps.", "type": "string"}}, "type": "object"}, "Image": {"description": "An uploaded image. The resource for ImagesService.", "id": "Image", "properties": {"id": {"description": "A unique id representing this image.", "type": "string"}, "sha1": {"description": "A sha1 hash of the image.", "type": "string"}, "sha256": {"description": "A sha256 hash of the image.", "type": "string"}, "url": {"description": "A URL that will serve a preview of the image.", "type": "string"}}, "type": "object"}, "ImagesDeleteAllResponse": {"description": "Response for deleting all images.", "id": "ImagesDeleteAllResponse", "properties": {"deleted": {"description": "The deleted images.", "items": {"$ref": "Image"}, "type": "array"}}, "type": "object"}, "ImagesListResponse": {"description": "Response listing all images.", "id": "ImagesListResponse", "properties": {"images": {"description": "All listed Images.", "items": {"$ref": "Image"}, "type": "array"}}, "type": "object"}, "ImagesUploadResponse": {"description": "Response for uploading an image.", "id": "ImagesUploadResponse", "properties": {"image": {"$ref": "Image", "description": "The uploaded image."}}, "type": "object"}, "InAppProduct": {"description": "An in-app product. The resource for InappproductsService.", "id": "InAppProduct", "properties": {"defaultLanguage": {"description": "Default language of the localized data, as defined by BCP-47. e.g. \"en-US\".", "type": "string"}, "defaultPrice": {"$ref": "Price", "description": "Default price. Cannot be zero, as in-app products are never free. Always in the developer's Checkout merchant currency."}, "gracePeriod": {"description": "Grace period of the subscription, specified in ISO 8601 format. Allows developers to give their subscribers a grace period when the payment for the new recurrence period is declined. Acceptable values are P0D (zero days), P3D (three days), P7D (seven days), P14D (14 days), and P30D (30 days).", "type": "string"}, "listings": {"additionalProperties": {"$ref": "InAppProductListing"}, "description": "List of localized title and description data. Map key is the language of the localized data, as defined by BCP-47, e.g. \"en-US\".", "type": "object"}, "managedProductTaxesAndComplianceSettings": {"$ref": "ManagedProductTaxAndComplianceSettings", "description": "Details about taxes and legal compliance. Only applicable to managed products."}, "packageName": {"description": "Package name of the parent app.", "type": "string"}, "prices": {"additionalProperties": {"$ref": "Price"}, "description": "Prices per buyer region. None of these can be zero, as in-app products are never free. Map key is region code, as defined by ISO 3166-2.", "type": "object"}, "purchaseType": {"description": "The type of the product, e.g. a recurring subscription.", "enum": ["purchaseTypeUnspecified", "managedUser", "subscription"], "enumDescriptions": ["Unspecified purchase type.", "The default product type - one time purchase.", "In-app product with a recurring period."], "type": "string"}, "sku": {"description": "Stock-keeping-unit (SKU) of the product, unique within an app.", "type": "string"}, "status": {"description": "The status of the product, e.g. whether it's active.", "enum": ["statusUnspecified", "active", "inactive"], "enumDescriptions": ["Unspecified status.", "The product is published and active in the store.", "The product is not published and therefore inactive in the store."], "type": "string"}, "subscriptionPeriod": {"description": "Subscription period, specified in ISO 8601 format. Acceptable values are P1W (one week), P1M (one month), P3M (three months), P6M (six months), and P1Y (one year).", "type": "string"}, "subscriptionTaxesAndComplianceSettings": {"$ref": "SubscriptionTaxAndComplianceSettings", "description": "Details about taxes and legal compliance. Only applicable to subscription products."}, "trialPeriod": {"description": "Trial period, specified in ISO 8601 format. Acceptable values are anything between P7D (seven days) and P999D (999 days).", "type": "string"}}, "type": "object"}, "InAppProductListing": {"description": "Store listing of a single in-app product.", "id": "InAppProductListing", "properties": {"benefits": {"description": "Localized entitlement benefits for a subscription.", "items": {"type": "string"}, "type": "array"}, "description": {"description": "Description for the store listing.", "type": "string"}, "title": {"description": "Title for the store listing.", "type": "string"}}, "type": "object"}, "InappproductsBatchDeleteRequest": {"description": "Request to delete multiple in-app products.", "id": "InappproductsBatchDeleteRequest", "properties": {"requests": {"description": "Individual delete requests. At least one request is required. Can contain up to 100 requests. All requests must correspond to different in-app products.", "items": {"$ref": "InappproductsDeleteRequest"}, "type": "array"}}, "type": "object"}, "InappproductsBatchGetResponse": {"description": "Response message for BatchGetSubscriptions endpoint.", "id": "InappproductsBatchGetResponse", "properties": {"inappproduct": {"description": "The list of requested in-app products, in the same order as the request.", "items": {"$ref": "InAppProduct"}, "type": "array"}}, "type": "object"}, "InappproductsBatchUpdateRequest": {"description": "Request to update or insert one or more in-app products.", "id": "InappproductsBatchUpdateRequest", "properties": {"requests": {"description": "Required. Individual update requests. At least one request is required. Can contain up to 100 requests. All requests must correspond to different in-app products.", "items": {"$ref": "InappproductsUpdateRequest"}, "type": "array"}}, "type": "object"}, "InappproductsBatchUpdateResponse": {"description": "Response for a batch in-app product update.", "id": "InappproductsBatchUpdateResponse", "properties": {"inappproducts": {"description": "The updated or inserted in-app products.", "items": {"$ref": "InAppProduct"}, "type": "array"}}, "type": "object"}, "InappproductsDeleteRequest": {"description": "Request to delete an in-app product.", "id": "InappproductsDeleteRequest", "properties": {"latencyTolerance": {"description": "Optional. The latency tolerance for the propagation of this product update. Defaults to latency-sensitive.", "enum": ["PRODUCT_UPDATE_LATENCY_TOLERANCE_UNSPECIFIED", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT"], "enumDescriptions": ["Defaults to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE.", "The update will propagate to clients within several minutes on average and up to a few hours in rare cases. Throughput is limited to 7,200 updates per app per hour.", "The update will propagate to clients within 24 hours. Supports high throughput of up to 720,000 updates per app per hour using batch modification methods."], "type": "string"}, "packageName": {"description": "Package name of the app.", "type": "string"}, "sku": {"description": "Unique identifier for the in-app product.", "type": "string"}}, "type": "object"}, "InappproductsListResponse": {"description": "Response listing all in-app products.", "id": "InappproductsListResponse", "properties": {"inappproduct": {"description": "All in-app products.", "items": {"$ref": "InAppProduct"}, "type": "array"}, "kind": {"description": "The kind of this response (\"androidpublisher#inappproductsListResponse\").", "type": "string"}, "pageInfo": {"$ref": "PageInfo", "deprecated": true, "description": "Deprecated and unset."}, "tokenPagination": {"$ref": "TokenPagination", "description": "Pagination token, to handle a number of products that is over one page."}}, "type": "object"}, "InappproductsUpdateRequest": {"description": "Request to update an in-app product.", "id": "InappproductsUpdateRequest", "properties": {"allowMissing": {"description": "If set to true, and the in-app product with the given package_name and sku doesn't exist, the in-app product will be created.", "type": "boolean"}, "autoConvertMissingPrices": {"description": "If true the prices for all regions targeted by the parent app that don't have a price specified for this in-app product will be auto converted to the target currency based on the default price. Defaults to false.", "type": "boolean"}, "inappproduct": {"$ref": "InAppProduct", "description": "The new in-app product."}, "latencyTolerance": {"description": "Optional. The latency tolerance for the propagation of this product update. Defaults to latency-sensitive.", "enum": ["PRODUCT_UPDATE_LATENCY_TOLERANCE_UNSPECIFIED", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT"], "enumDescriptions": ["Defaults to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE.", "The update will propagate to clients within several minutes on average and up to a few hours in rare cases. Throughput is limited to 7,200 updates per app per hour.", "The update will propagate to clients within 24 hours. Supports high throughput of up to 720,000 updates per app per hour using batch modification methods."], "type": "string"}, "packageName": {"description": "Package name of the app.", "type": "string"}, "sku": {"description": "Unique identifier for the in-app product.", "type": "string"}}, "type": "object"}, "InstallmentPlan": {"description": "Information to a installment plan.", "id": "InstallmentPlan", "properties": {"initialCommittedPaymentsCount": {"description": "Total number of payments the user is initially committed for.", "format": "int32", "type": "integer"}, "pendingCancellation": {"$ref": "PendingCancellation", "description": "If present, this installment plan is pending to be canceled. The cancellation will happen only after the user finished all committed payments."}, "remainingCommittedPaymentsCount": {"description": "Total number of committed payments remaining to be paid for in this renewal cycle.", "format": "int32", "type": "integer"}, "subsequentCommittedPaymentsCount": {"description": "Total number of payments the user will be committed for after each commitment period. Empty means the installment plan will fall back to a normal auto-renew subscription after initial commitment.", "format": "int32", "type": "integer"}}, "type": "object"}, "InstallmentsBasePlanType": {"description": "Represents an installments base plan where a user commits to a specified number of payments.", "id": "InstallmentsBasePlanType", "properties": {"accountHoldDuration": {"description": "Optional. Account hold period of the subscription, specified in ISO 8601 format. Acceptable values must be in days and between P0D and P60D. If not specified, the default value is P30D. The sum of gracePeriodDuration and accountHoldDuration must be between P30D and P60D days, inclusive.", "type": "string"}, "billingPeriodDuration": {"description": "Required. Immutable. Subscription period, specified in ISO 8601 format. For a list of acceptable billing periods, refer to the help center. The duration is immutable after the base plan is created.", "type": "string"}, "committedPaymentsCount": {"description": "Required. Immutable. The number of payments the user is committed to. It is immutable after the base plan is created.", "format": "int32", "type": "integer"}, "gracePeriodDuration": {"description": "Grace period of the subscription, specified in ISO 8601 format. Acceptable values must be in days and between P0D and the lesser of 30D and base plan billing period. If not specified, a default value will be used based on the billing period. The sum of gracePeriodDuration and accountHoldDuration must be between P30D and P60D days, inclusive.", "type": "string"}, "prorationMode": {"description": "The proration mode for the base plan determines what happens when a user switches to this plan from another base plan. If unspecified, defaults to CHARGE_ON_NEXT_BILLING_DATE.", "enum": ["SUBSCRIPTION_PRORATION_MODE_UNSPECIFIED", "SUBSCRIPTION_PRORATION_MODE_CHARGE_ON_NEXT_BILLING_DATE", "SUBSCRIPTION_PRORATION_MODE_CHARGE_FULL_PRICE_IMMEDIATELY"], "enumDescriptions": ["Unspecified mode.", "Users will be charged for their new base plan at the end of their current billing period.", "Users will be charged for their new base plan immediately and in full. Any remaining period of their existing subscription will be used to extend the duration of the new billing plan."], "type": "string"}, "renewalType": {"description": "Required. Immutable. Installments base plan renewal type. Determines the behavior at the end of the initial commitment. The renewal type is immutable after the base plan is created.", "enum": ["RENEWAL_TYPE_UNSPECIFIED", "RENEWAL_TYPE_RENEWS_WITHOUT_COMMITMENT", "RENEWAL_TYPE_RENEWS_WITH_COMMITMENT"], "enumDescriptions": ["Unspecified state.", "Renews periodically for the billing period duration without commitment.", "Renews with the commitment of the same duration as the initial one."], "type": "string"}, "resubscribeState": {"description": "Whether users should be able to resubscribe to this base plan in Google Play surfaces. Defaults to RESUBSCRIBE_STATE_ACTIVE if not specified.", "enum": ["RESUBSCRIBE_STATE_UNSPECIFIED", "RESUBSCRIBE_STATE_ACTIVE", "RESUBSCRIBE_STATE_INACTIVE"], "enumDescriptions": ["Unspecified state.", "Resubscribe is active.", "Resubscribe is inactive."], "type": "string"}}, "type": "object"}, "InternalAppSharingArtifact": {"description": "An artifact resource which gets created when uploading an APK or Android App Bundle through internal app sharing.", "id": "InternalAppSharingArtifact", "properties": {"certificateFingerprint": {"description": "The sha256 fingerprint of the certificate used to sign the generated artifact.", "type": "string"}, "downloadUrl": {"description": "The download URL generated for the uploaded artifact. Users that are authorized to download can follow the link to the Play Store app to install it.", "type": "string"}, "sha256": {"description": "The sha256 hash of the artifact represented as a lowercase hexadecimal number, matching the output of the sha256sum command.", "type": "string"}}, "type": "object"}, "IntroductoryPriceInfo": {"description": "Contains the introductory price information for a subscription.", "id": "IntroductoryPriceInfo", "properties": {"introductoryPriceAmountMicros": {"description": "Introductory price of the subscription, not including tax. The currency is the same as price_currency_code. Price is expressed in micro-units, where 1,000,000 micro-units represents one unit of the currency. For example, if the subscription price is €1.99, price_amount_micros is 1990000.", "format": "int64", "type": "string"}, "introductoryPriceCurrencyCode": {"description": "ISO 4217 currency code for the introductory subscription price. For example, if the price is specified in British pounds sterling, price_currency_code is \"GBP\".", "type": "string"}, "introductoryPriceCycles": {"description": "The number of billing period to offer introductory pricing.", "format": "int32", "type": "integer"}, "introductoryPricePeriod": {"description": "Introductory price period, specified in ISO 8601 format. Common values are (but not limited to) \"P1W\" (one week), \"P1M\" (one month), \"P3M\" (three months), \"P6M\" (six months), and \"P1Y\" (one year).", "type": "string"}}, "type": "object"}, "LanguageTargeting": {"description": "Targeting based on language.", "id": "LanguageTargeting", "properties": {"alternatives": {"description": "Alternative languages.", "items": {"type": "string"}, "type": "array"}, "value": {"description": "ISO-639: 2 or 3 letter language code.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "LineItem": {"description": "Details of a line item.", "id": "LineItem", "properties": {"listingPrice": {"$ref": "Money", "description": "Item's listed price on Play Store, this may or may not include tax. Excludes any discounts or promotions."}, "oneTimePurchaseDetails": {"$ref": "OneTimePurchaseDetails", "description": "Details of a one-time purchase."}, "paidAppDetails": {"$ref": "PaidAppDetails", "description": "Details of a paid app purchase."}, "productId": {"description": "The purchased product ID or in-app SKU (for example, 'monthly001' or 'com.some.thing.inapp1').", "type": "string"}, "productTitle": {"description": "Developer-specified name of the product. Displayed in buyer's locale. Example: coins, monthly subscription, etc.", "type": "string"}, "subscriptionDetails": {"$ref": "SubscriptionDetails", "description": "Details of a subscription purchase."}, "tax": {"$ref": "Money", "description": "The tax paid for this line item."}, "total": {"$ref": "Money", "description": "The total amount paid by the user for this line item, taking into account discounts and tax."}}, "type": "object"}, "ListAppRecoveriesResponse": {"description": "Response message for ListAppRecoveries. -- api-linter: core::0158::response-next-page-token-field=disabled", "id": "ListAppRecoveriesResponse", "properties": {"recoveryActions": {"description": "List of recovery actions associated with the requested package name.", "items": {"$ref": "AppRecoveryAction"}, "type": "array"}}, "type": "object"}, "ListDeviceTierConfigsResponse": {"description": "Response listing existing device tier configs.", "id": "ListDeviceTierConfigsResponse", "properties": {"deviceTierConfigs": {"description": "Device tier configs created by the developer.", "items": {"$ref": "DeviceTierConfig"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "ListOneTimeProductOffersResponse": {"description": "Response message for ListOneTimeProductOffers.", "id": "ListOneTimeProductOffersResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "oneTimeProductOffers": {"description": "The one_time_product offers from the specified request.", "items": {"$ref": "OneTimeProductOffer"}, "type": "array"}}, "type": "object"}, "ListOneTimeProductsResponse": {"description": "Response message for ListOneTimeProducts.", "id": "ListOneTimeProductsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "oneTimeProducts": {"description": "The one-time products from the specified app.", "items": {"$ref": "OneTimeProduct"}, "type": "array"}}, "type": "object"}, "ListSubscriptionOffersResponse": {"description": "Response message for ListSubscriptionOffers.", "id": "ListSubscriptionOffersResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "subscriptionOffers": {"description": "The subscription offers from the specified subscription.", "items": {"$ref": "SubscriptionOffer"}, "type": "array"}}, "type": "object"}, "ListSubscriptionsResponse": {"description": "Response message for ListSubscriptions.", "id": "ListSubscriptionsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "subscriptions": {"description": "The subscriptions from the specified app.", "items": {"$ref": "Subscription"}, "type": "array"}}, "type": "object"}, "ListUsersResponse": {"description": "A response containing one or more users with access to an account.", "id": "ListUsersResponse", "properties": {"nextPageToken": {"description": "A token to pass to subsequent calls in order to retrieve subsequent results. This will not be set if there are no more results to return.", "type": "string"}, "users": {"description": "The resulting users.", "items": {"$ref": "User"}, "type": "array"}}, "type": "object"}, "Listing": {"description": "A localized store listing. The resource for ListingsService.", "id": "Listing", "properties": {"fullDescription": {"description": "Full description of the app.", "type": "string"}, "language": {"description": "Language localization code (a BCP-47 language tag; for example, \"de-AT\" for Austrian German).", "type": "string"}, "shortDescription": {"description": "Short description of the app.", "type": "string"}, "title": {"description": "Localized title of the app.", "type": "string"}, "video": {"description": "URL of a promotional YouTube video for the app.", "type": "string"}}, "type": "object"}, "ListingsListResponse": {"description": "Response listing all localized listings.", "id": "ListingsListResponse", "properties": {"kind": {"description": "The kind of this response (\"androidpublisher#listingsListResponse\").", "type": "string"}, "listings": {"description": "All localized listings.", "items": {"$ref": "Listing"}, "type": "array"}}, "type": "object"}, "LocalizedText": {"description": "Localized text in given language.", "id": "LocalizedText", "properties": {"language": {"description": "Language localization code (a BCP-47 language tag; for example, \"de-AT\" for Austrian German).", "type": "string"}, "text": {"description": "The text in the given language.", "type": "string"}}, "type": "object"}, "ManagedProductTaxAndComplianceSettings": {"description": "Details about taxation and legal compliance for managed products.", "id": "ManagedProductTaxAndComplianceSettings", "properties": {"eeaWithdrawalRightType": {"description": "Digital content or service classification for products distributed to users in the European Economic Area (EEA). The withdrawal regime under EEA consumer laws depends on this classification. Refer to the [Help Center article](https://support.google.com/googleplay/android-developer/answer/10463498) for more information.", "enum": ["WITHDRAWAL_RIGHT_TYPE_UNSPECIFIED", "WITHDRAWAL_RIGHT_DIGITAL_CONTENT", "WITHDRAWAL_RIGHT_SERVICE"], "enumDescriptions": ["", "", ""], "type": "string"}, "isTokenizedDigitalAsset": {"description": "Whether this in-app product is declared as a product representing a tokenized digital asset.", "type": "boolean"}, "taxRateInfoByRegionCode": {"additionalProperties": {"$ref": "RegionalTaxRateInfo"}, "description": "A mapping from region code to tax rate details. The keys are region codes as defined by Unicode's \"CLDR\".", "type": "object"}}, "type": "object"}, "MigrateBasePlanPricesRequest": {"description": "Request message for MigrateBasePlanPrices.", "id": "MigrateBasePlanPricesRequest", "properties": {"basePlanId": {"description": "Required. The unique base plan ID of the base plan to update prices on.", "type": "string"}, "latencyTolerance": {"description": "Optional. The latency tolerance for the propagation of this product update. Defaults to latency-sensitive.", "enum": ["PRODUCT_UPDATE_LATENCY_TOLERANCE_UNSPECIFIED", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT"], "enumDescriptions": ["Defaults to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE.", "The update will propagate to clients within several minutes on average and up to a few hours in rare cases. Throughput is limited to 7,200 updates per app per hour.", "The update will propagate to clients within 24 hours. Supports high throughput of up to 720,000 updates per app per hour using batch modification methods."], "type": "string"}, "packageName": {"description": "Required. Package name of the parent app. Must be equal to the package_name field on the Subscription resource.", "type": "string"}, "productId": {"description": "Required. The ID of the subscription to update. Must be equal to the product_id field on the Subscription resource.", "type": "string"}, "regionalPriceMigrations": {"description": "Required. The regional prices to update.", "items": {"$ref": "RegionalPriceMigrationConfig"}, "type": "array"}, "regionsVersion": {"$ref": "RegionsVersion", "description": "Required. The version of the available regions being used for the regional_price_migrations."}}, "type": "object"}, "MigrateBasePlanPricesResponse": {"description": "Response message for MigrateBasePlanPrices.", "id": "MigrateBasePlanPricesResponse", "properties": {}, "type": "object"}, "ModuleMetadata": {"description": "Metadata of a module.", "id": "ModuleMetadata", "properties": {"deliveryType": {"description": "Indicates the delivery type (e.g. on-demand) of the module.", "enum": ["UNKNOWN_DELIVERY_TYPE", "INSTALL_TIME", "ON_DEMAND", "FAST_FOLLOW"], "enumDescriptions": ["Unspecified delivery type.", "This module will always be downloaded as part of the initial install of the app.", "This module is requested on-demand, which means it will not be part of the initial install, and will only be sent when requested by the client.", "This module will be downloaded immediately after initial install finishes. The app can be opened before these modules are downloaded."], "type": "string"}, "dependencies": {"description": "Names of the modules that this module directly depends on. Each module implicitly depends on the base module.", "items": {"type": "string"}, "type": "array"}, "moduleType": {"description": "Indicates the type of this feature module.", "enum": ["UNKNOWN_MODULE_TYPE", "FEATURE_MODULE"], "enumDescriptions": ["Unknown feature module.", "Regular feature module."], "type": "string"}, "name": {"description": "Module name.", "type": "string"}, "targeting": {"$ref": "ModuleTargeting", "description": "The targeting that makes a conditional module installed. Relevant only for Split APKs."}}, "type": "object"}, "ModuleTargeting": {"description": "Targeting on the module level.", "id": "ModuleTargeting", "properties": {"deviceFeatureTargeting": {"description": "Targeting for device features.", "items": {"$ref": "DeviceFeatureTargeting"}, "type": "array"}, "sdkVersionTargeting": {"$ref": "SdkVersionTargeting", "description": "The sdk version that the variant targets"}, "userCountriesTargeting": {"$ref": "UserCountriesTargeting", "description": "Countries-level targeting"}}, "type": "object"}, "Money": {"description": "Represents an amount of money with its currency type.", "id": "Money", "properties": {"currencyCode": {"description": "The three-letter currency code defined in ISO 4217.", "type": "string"}, "nanos": {"description": "Number of nano (10^-9) units of the amount. The value must be between -999,999,999 and +999,999,999 inclusive. If `units` is positive, `nanos` must be positive or zero. If `units` is zero, `nanos` can be positive, zero, or negative. If `units` is negative, `nanos` must be negative or zero. For example $-1.75 is represented as `units`=-1 and `nanos`=-750,000,000.", "format": "int32", "type": "integer"}, "units": {"description": "The whole units of the amount. For example if `currencyCode` is `\"USD\"`, then 1 unit is one US dollar.", "format": "int64", "type": "string"}}, "type": "object"}, "MultiAbi": {"description": "Represents a list of ABIs.", "id": "MultiAbi", "properties": {"abi": {"description": "A list of targeted ABIs, as represented by the Android Platform", "items": {"$ref": "<PERSON><PERSON>"}, "type": "array"}}, "type": "object"}, "MultiAbiTargeting": {"description": "Targeting based on multiple abis.", "id": "MultiAbiTargeting", "properties": {"alternatives": {"description": "Targeting of other sibling directories that were in the Bundle. For main splits this is targeting of other main splits.", "items": {"$ref": "MultiAbi"}, "type": "array"}, "value": {"description": "Value of a multi abi.", "items": {"$ref": "MultiAbi"}, "type": "array"}}, "type": "object"}, "OfferDetails": {"description": "Offer details information related to a purchase line item.", "id": "OfferDetails", "properties": {"basePlanId": {"description": "The base plan ID. Present for all base plan and offers.", "type": "string"}, "offerId": {"description": "The offer ID. Only present for discounted offers.", "type": "string"}, "offerTags": {"description": "The latest offer tags associated with the offer. It includes tags inherited from the base plan.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "OfferTag": {"description": "Represents a custom tag specified for a product offer.", "id": "OfferTag", "properties": {"tag": {"description": "Must conform with RFC-1034. That is, this string can only contain lower-case letters (a-z), numbers (0-9), and hyphens (-), and be at most 20 characters.", "type": "string"}}, "type": "object"}, "OneTimeCode": {"description": "A single use promotion code.", "id": "OneTimeCode", "properties": {}, "type": "object"}, "OneTimeExternalTransaction": {"description": "Represents a one-time transaction.", "id": "OneTimeExternalTransaction", "properties": {"externalTransactionToken": {"description": "Input only. Provided during the call to Create. Retrieved from the client when the alternative billing flow is launched.", "type": "string"}}, "type": "object"}, "OneTimeProduct": {"description": "A single one-time product for an app.", "id": "OneTimeProduct", "properties": {"listings": {"description": "Required. Set of localized title and description data. Must not have duplicate entries with the same language_code.", "items": {"$ref": "OneTimeProductListing"}, "type": "array"}, "offerTags": {"description": "Optional. List of up to 20 custom tags specified for this one-time product, and returned to the app through the billing library. Purchase options and offers for this product will also receive these tags in the billing library.", "items": {"$ref": "OfferTag"}, "type": "array"}, "packageName": {"description": "Required. Immutable. Package name of the parent app.", "type": "string"}, "productId": {"description": "Required. Immutable. Unique product ID of the product. Unique within the parent app. Product IDs must start with a number or lowercase letter, and can contain numbers (0-9), lowercase letters (a-z), underscores (_), and periods (.).", "type": "string"}, "purchaseOptions": {"description": "Required. The set of purchase options for this one-time product.", "items": {"$ref": "OneTimeProductPurchaseOption"}, "type": "array"}, "regionsVersion": {"$ref": "RegionsVersion", "description": "Output only. The version of the regions configuration that was used to generate the one-time product.", "readOnly": true}, "restrictedPaymentCountries": {"$ref": "RestrictedPaymentCountries", "description": "Optional. Countries where the purchase of this one-time product is restricted to payment methods registered in the same country. If empty, no payment location restrictions are imposed."}, "taxAndComplianceSettings": {"$ref": "OneTimeProductTaxAndComplianceSettings", "description": "Details about taxes and legal compliance."}}, "type": "object"}, "OneTimeProductBuyPurchaseOption": {"description": "A purchase option that can be bought.", "id": "OneTimeProductBuyPurchaseOption", "properties": {"legacyCompatible": {"description": "Optional. Whether this purchase option will be available in legacy PBL flows that do not support one-time products model. Up to one \"buy\" purchase option can be marked as backwards compatible.", "type": "boolean"}, "multiQuantityEnabled": {"description": "Optional. Whether this purchase option allows multi-quantity. Multi-quantity allows buyer to purchase more than one item in a single checkout.", "type": "boolean"}}, "type": "object"}, "OneTimeProductDiscountedOffer": {"description": "Configuration specific to discounted offers.", "id": "OneTimeProductDiscountedOffer", "properties": {"endTime": {"description": "Time when the offer will stop being available.", "format": "google-datetime", "type": "string"}, "redemptionLimit": {"description": "Optional. The number of times this offer can be redeemed. If unset or set to 0, allows for unlimited offer redemptions. Otherwise must be a number between 1 and 50 inclusive.", "format": "int64", "type": "string"}, "startTime": {"description": "Time when the offer will start being available.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "OneTimeProductListing": {"description": "Regional store listing for a one-time product.", "id": "OneTimeProductListing", "properties": {"description": {"description": "Required. The description of this product in the language of this listing. The maximum length is 200 characters.", "type": "string"}, "languageCode": {"description": "Required. The language of this listing, as defined by BCP-47, e.g., \"en-US\".", "type": "string"}, "title": {"description": "Required. The title of this product in the language of this listing. The maximum length is 55 characters.", "type": "string"}}, "type": "object"}, "OneTimeProductOffer": {"description": "A single offer for a one-time product.", "id": "OneTimeProductOffer", "properties": {"discountedOffer": {"$ref": "OneTimeProductDiscountedOffer", "description": "A discounted offer."}, "offerId": {"description": "Required. Immutable. The ID of this product offer. Must be unique within the purchase option. It must start with a number or lower-case letter, and can only contain lower-case letters (a-z), numbers (0-9), and hyphens (-). The maximum length is 63 characters.", "type": "string"}, "offerTags": {"description": "Optional. List of up to 20 custom tags specified for this offer, and returned to the app through the billing library.", "items": {"$ref": "OfferTag"}, "type": "array"}, "packageName": {"description": "Required. Immutable. The package name of the app the parent product belongs to.", "type": "string"}, "preOrderOffer": {"$ref": "OneTimeProductPreOrderOffer", "description": "A pre-order offer."}, "productId": {"description": "Required. Immutable. The ID of the parent product this offer belongs to.", "type": "string"}, "purchaseOptionId": {"description": "Required. Immutable. The ID of the purchase option to which this offer is an extension.", "type": "string"}, "regionalPricingAndAvailabilityConfigs": {"description": "Set of regional pricing and availability information for this offer. Must not have duplicate entries with the same region_code.", "items": {"$ref": "OneTimeProductOfferRegionalPricingAndAvailabilityConfig"}, "type": "array"}, "regionsVersion": {"$ref": "RegionsVersion", "description": "Output only. The version of the regions configuration that was used to generate the one-time product offer.", "readOnly": true}, "state": {"description": "Output only. The current state of this offer. This field cannot be changed by updating the resource. Use the dedicated endpoints instead.", "enum": ["STATE_UNSPECIFIED", "DRAFT", "ACTIVE", "CANCELLED", "INACTIVE"], "enumDescriptions": ["Default value, should never be used.", "The offer is not and has never been available to users.", "The offer is available to users, as long as its conditions are met.", "This state is specific to pre-orders. The offer is cancelled and not available to users. All pending orders related to this offer were cancelled.", "This state is specific to discounted offers. The offer is no longer available to users."], "readOnly": true, "type": "string"}}, "type": "object"}, "OneTimeProductOfferNoPriceOverrideOptions": {"description": "Options for one-time product offers without a regional price override.", "id": "OneTimeProductOfferNoPriceOverrideOptions", "properties": {}, "type": "object"}, "OneTimeProductOfferRegionalPricingAndAvailabilityConfig": {"description": "Regional pricing and availability configuration for a one-time product offer.", "id": "OneTimeProductOfferRegionalPricingAndAvailabilityConfig", "properties": {"absoluteDiscount": {"$ref": "Money", "description": "The absolute value of the discount that is subtracted from the purchase option price. It should be between 0 and the purchase option price."}, "availability": {"description": "Required. The availability for this region.", "enum": ["AVAILABILITY_UNSPECIFIED", "AVAILABLE", "NO_LONGER_AVAILABLE"], "enumDescriptions": ["Unspecified availability. Must not be used.", "The offer is available to users.", "The offer is no longer available to users. This value can only be used if the availability was previously set as AVAILABLE."], "type": "string"}, "noOverride": {"$ref": "OneTimeProductOfferNoPriceOverrideOptions", "description": "The price defined in the purchase option for this region will be used."}, "regionCode": {"description": "Required. Region code this configuration applies to, as defined by ISO 3166-2, e.g., \"US\".", "type": "string"}, "relativeDiscount": {"description": "The fraction of the purchase option price that the user pays for this offer. For example, if the purchase option price for this region is $12, then a 50% discount would correspond to a price of $6. The discount must be specified as a fraction strictly larger than 0 and strictly smaller than 1. The resulting price will be rounded to the nearest billable unit (e.g. cents for USD). The relative discount is considered invalid if the discounted price ends up being smaller than the minimum price allowed in this region.", "format": "double", "type": "number"}}, "type": "object"}, "OneTimeProductPreOrderOffer": {"description": "Configuration specific to pre-order offers.", "id": "OneTimeProductPreOrderOffer", "properties": {"endTime": {"description": "Required. Time when the pre-order will stop being available.", "format": "google-datetime", "type": "string"}, "priceChangeBehavior": {"description": "Required. Immutable. Specifies how price changes affect pre-existing pre-orders.", "enum": ["PRE_ORDER_PRICE_CHANGE_BEHAVIOR_UNSPECIFIED", "PRE_ORDER_PRICE_CHANGE_BEHAVIOR_TWO_POINT_LOWEST", "PRE_ORDER_PRICE_CHANGE_BEHAVIOR_NEW_ORDERS_ONLY"], "enumDescriptions": ["Unspecified price change behavior. Must not be used.", "The buyer gets charged the minimum between the initial price at the time of pre-order and the final offer price on the release date.", "The buyer gets the same price as the one they pre-ordered, regardless of any price changes that may have happened after the pre-order."], "type": "string"}, "releaseTime": {"description": "Required. Time on which the product associated with the pre-order will be released and the pre-order orders fulfilled.", "format": "google-datetime", "type": "string"}, "startTime": {"description": "Required. Time when the pre-order will start being available.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "OneTimeProductPurchaseOption": {"description": "A single purchase option for a one-time product.", "id": "OneTimeProductPurchaseOption", "properties": {"buyOption": {"$ref": "OneTimeProductBuyPurchaseOption", "description": "A purchase option that can be bought."}, "newRegionsConfig": {"$ref": "OneTimeProductPurchaseOptionNewRegionsConfig", "description": "Pricing information for any new locations Play may launch in the future. If omitted, the purchase option will not be automatically available in any new locations Play may launch in the future."}, "offerTags": {"description": "Optional. List of up to 20 custom tags specified for this purchase option, and returned to the app through the billing library. Offers for this purchase option will also receive these tags in the billing library.", "items": {"$ref": "OfferTag"}, "type": "array"}, "purchaseOptionId": {"description": "Required. Immutable. The unique identifier of this purchase option. Must be unique within the one-time product. It must start with a number or lower-case letter, and can only contain lower-case letters (a-z), numbers (0-9), and hyphens (-). The maximum length is 63 characters.", "type": "string"}, "regionalPricingAndAvailabilityConfigs": {"description": "Regional pricing and availability information for this purchase option.", "items": {"$ref": "OneTimeProductPurchaseOptionRegionalPricingAndAvailabilityConfig"}, "type": "array"}, "rentOption": {"$ref": "OneTimeProductRentPurchaseOption", "description": "A purchase option that can be rented."}, "state": {"description": "Output only. The state of the purchase option, i.e., whether it's active. This field cannot be changed by updating the resource. Use the dedicated endpoints instead.", "enum": ["STATE_UNSPECIFIED", "DRAFT", "ACTIVE", "INACTIVE", "INACTIVE_PUBLISHED"], "enumDescriptions": ["Default value, should never be used.", "The purchase option is not and has never been available to users.", "The purchase option is available to users.", "The purchase option is not available to users anymore.", "The purchase option is not available for purchase anymore, but we continue to expose its offer via the Play Billing Library for backwards compatibility. Only automatically migrated purchase options can be in this state."], "readOnly": true, "type": "string"}, "taxAndComplianceSettings": {"$ref": "PurchaseOptionTaxAndComplianceSettings", "description": "Optional. Details about taxes and legal compliance."}}, "type": "object"}, "OneTimeProductPurchaseOptionNewRegionsConfig": {"description": "Pricing information for any new regions Play may launch in the future.", "id": "OneTimeProductPurchaseOptionNewRegionsConfig", "properties": {"availability": {"description": "Required. The regional availability for the new regions config. When set to AVAILABLE, the pricing information will be used for any new regions Play may launch in the future.", "enum": ["AVAILABILITY_UNSPECIFIED", "AVAILABLE", "NO_LONGER_AVAILABLE"], "enumDescriptions": ["Unspecified availability. Must not be used.", "The config will be used for any new regions Play may launch in the future.", "The config is not available anymore and will not be used for any new regions Play may launch in the future. This value can only be used if the availability was previously set as AVAILABLE."], "type": "string"}, "eurPrice": {"$ref": "Money", "description": "Required. Price in EUR to use for any new regions Play may launch in."}, "usdPrice": {"$ref": "Money", "description": "Required. Price in USD to use for any new regions Play may launch in."}}, "type": "object"}, "OneTimeProductPurchaseOptionRegionalPricingAndAvailabilityConfig": {"description": "Regional pricing and availability configuration for a purchase option.", "id": "OneTimeProductPurchaseOptionRegionalPricingAndAvailabilityConfig", "properties": {"availability": {"description": "The availability of the purchase option.", "enum": ["AVAILABILITY_UNSPECIFIED", "AVAILABLE", "NO_LONGER_AVAILABLE", "AVAILABLE_IF_RELEASED"], "enumDescriptions": ["Unspecified availability. Must not be used.", "The purchase option is available to users.", "The purchase option is no longer available to users. This value can only be used if the availability was previously set as AVAILABLE.", "The purchase option is initially unavailable, but made available via a released pre-order offer."], "type": "string"}, "price": {"$ref": "Money", "description": "The price of the purchase option in the specified region. Must be set in the currency that is linked to the specified region."}, "regionCode": {"description": "Required. Region code this configuration applies to, as defined by ISO 3166-2, e.g., \"US\".", "type": "string"}}, "type": "object"}, "OneTimeProductRentPurchaseOption": {"description": "A purchase option that can be rented.", "id": "OneTimeProductRentPurchaseOption", "properties": {"expirationPeriod": {"description": "Optional. The amount of time the user has after starting consuming the entitlement before it is revoked. Specified in ISO 8601 format.", "type": "string"}, "rentalPeriod": {"description": "Required. The amount of time a user has the entitlement for. Starts at purchase flow completion. Specified in ISO 8601 format.", "type": "string"}}, "type": "object"}, "OneTimeProductTaxAndComplianceSettings": {"description": "Details about taxation, Google Play policy and legal compliance for one-time products.", "id": "OneTimeProductTaxAndComplianceSettings", "properties": {"isTokenizedDigitalAsset": {"description": "Whether this one-time product is declared as a product representing a tokenized digital asset.", "type": "boolean"}, "regionalTaxConfigs": {"description": "Regional tax configuration.", "items": {"$ref": "RegionalTaxConfig"}, "type": "array"}}, "type": "object"}, "OneTimePurchaseDetails": {"description": "Details of a one-time purchase.", "id": "OneTimePurchaseDetails", "properties": {"offerId": {"description": "The offer ID of the one-time purchase offer.", "type": "string"}, "purchaseOptionId": {"description": "ID of the purchase option. This field is set for both purchase options and variant offers. For purchase options, this ID identifies the purchase option itself. For variant offers, this ID refers to the associated purchase option, and in conjunction with offer_id it identifies the variant offer.", "type": "string"}, "quantity": {"description": "The number of items purchased (for multi-quantity item purchases).", "format": "int32", "type": "integer"}, "rentalDetails": {"$ref": "RentalDetails", "description": "The details of a rent purchase. Only set if it is a rent purchase."}}, "type": "object"}, "Order": {"description": "The Order resource encapsulates comprehensive information about a transaction made on Google Play. It includes a variety of attributes that provide details about the order itself, the products purchased, and the history of events related to the order. The Orders APIs provide real-time access to your order data within the Google Play ecosystem. You can retrieve detailed information and metadata for both one-time and recurring orders, including transaction details like charges, taxes, and refunds, as well as metadata such as pricing phases for subscriptions. The Orders APIs let you automate tasks related to order management, reducing the need for manual checks via the Play Developer Console. The following are some of the use cases for this API: + Real-time order data retrieval - Get order details and metadata immediately after a purchase using an order ID. + Order update synchronization - Periodically sync order updates to maintain an up-to-date record of order information. Note: + The Orders API calls count towards your Play Developer API quota, which defaults to 200K daily, and may be insufficient to sync extensive order histories. + A maximum of 1000 orders can be retrieved per call. Using larger page sizes is recommended to minimize quota usage. Check your quota in the Cloud Console and request more if required.", "id": "Order", "properties": {"buyerAddress": {"$ref": "BuyerAddress", "description": "Address information for the customer, for use in tax computation. When Google is the Merchant of Record for the order, only country is shown."}, "createTime": {"description": "The time when the order was created.", "format": "google-datetime", "type": "string"}, "developerRevenueInBuyerCurrency": {"$ref": "Money", "description": "Your revenue for this order in the buyer's currency, including deductions of partial refunds, taxes and fees. Google deducts standard transaction and third party fees from each sale, including VAT in some regions."}, "lastEventTime": {"description": "The time of the last event that occurred on the order.", "format": "google-datetime", "type": "string"}, "lineItems": {"description": "The individual line items making up this order.", "items": {"$ref": "LineItem"}, "type": "array"}, "orderDetails": {"$ref": "OrderDetails", "description": "Detailed information about the order at creation time."}, "orderHistory": {"$ref": "OrderHistory", "description": "Details about events which modified the order."}, "orderId": {"description": "The order ID.", "type": "string"}, "pointsDetails": {"$ref": "PointsDetails", "description": "Play points applied to the order, including offer information, discount rate and point values."}, "purchaseToken": {"description": "The token provided to the user's device when the subscription or item was purchased.", "type": "string"}, "state": {"description": "The state of the order.", "enum": ["STATE_UNSPECIFIED", "PENDING", "PROCESSED", "CANCELED", "PENDING_REFUND", "PARTIALLY_REFUNDED", "REFUNDED"], "enumDescriptions": ["State unspecified. This value is not used.", "Order has been created and is waiting to be processed.", "Order has been successfully processed.", "Order was canceled before being processed.", "Requested refund is waiting to be processed.", "Part of the order amount was refunded.", "The full order amount was refunded."], "type": "string"}, "tax": {"$ref": "Money", "description": "The total tax paid as a part of this order."}, "total": {"$ref": "Money", "description": "The final amount paid by the customer, taking into account discounts and taxes."}}, "type": "object"}, "OrderDetails": {"description": "Detailed information about the order at creation time.", "id": "OrderDetails", "properties": {"taxInclusive": {"description": "Indicates whether the listed price was tax inclusive or not.", "type": "boolean"}}, "type": "object"}, "OrderHistory": {"description": "Details about events which modified the order.", "id": "OrderHistory", "properties": {"cancellationEvent": {"$ref": "CancellationEvent", "description": "Details of when the order was canceled."}, "partialRefundEvents": {"description": "Details of the partial refund events for this order.", "items": {"$ref": "PartialRefundEvent"}, "type": "array"}, "processedEvent": {"$ref": "ProcessedEvent", "description": "Details of when the order was processed."}, "refundEvent": {"$ref": "RefundEvent", "description": "Details of when the order was fully refunded."}}, "type": "object"}, "OtherRecurringProduct": {"description": "Details of a recurring external transaction product which doesn't belong to any other more specific category.", "id": "OtherRecurringProduct", "properties": {}, "type": "object"}, "OtherRegionsBasePlanConfig": {"description": "Pricing information for any new locations Play may launch in.", "id": "OtherRegionsBasePlanConfig", "properties": {"eurPrice": {"$ref": "Money", "description": "Required. Price in EUR to use for any new locations Play may launch in."}, "newSubscriberAvailability": {"description": "Whether the base plan is available for new subscribers in any new locations Play may launch in. If not specified, this will default to false.", "type": "boolean"}, "usdPrice": {"$ref": "Money", "description": "Required. Price in USD to use for any new locations Play may launch in."}}, "type": "object"}, "OtherRegionsSubscriptionOfferConfig": {"description": "Configuration for any new locations Play may launch in specified on a subscription offer.", "id": "OtherRegionsSubscriptionOfferConfig", "properties": {"otherRegionsNewSubscriberAvailability": {"description": "Whether the subscription offer in any new locations Play may launch in the future. If not specified, this will default to false.", "type": "boolean"}}, "type": "object"}, "OtherRegionsSubscriptionOfferPhaseConfig": {"description": "Configuration for any new locations Play may launch in for a single offer phase.", "id": "OtherRegionsSubscriptionOfferPhaseConfig", "properties": {"absoluteDiscounts": {"$ref": "OtherRegionsSubscriptionOfferPhasePrices", "description": "The absolute amount of money subtracted from the base plan price prorated over the phase duration that the user pays for this offer phase. For example, if the base plan price for this region is $12 for a period of 1 year, then a $1 absolute discount for a phase of a duration of 3 months would correspond to a price of $2. The resulting price may not be smaller than the minimum price allowed for any new locations Play may launch in."}, "free": {"$ref": "OtherRegionsSubscriptionOfferPhaseFreePriceOverride", "description": "Set to specify this offer is free to obtain."}, "otherRegionsPrices": {"$ref": "OtherRegionsSubscriptionOfferPhasePrices", "description": "The absolute price the user pays for this offer phase. The price must not be smaller than the minimum price allowed for any new locations Play may launch in."}, "relativeDiscount": {"description": "The fraction of the base plan price prorated over the phase duration that the user pays for this offer phase. For example, if the base plan price for this region is $12 for a period of 1 year, then a 50% discount for a phase of a duration of 3 months would correspond to a price of $1.50. The discount must be specified as a fraction strictly larger than 0 and strictly smaller than 1. The resulting price will be rounded to the nearest billable unit (e.g. cents for USD). The relative discount is considered invalid if the discounted price ends up being smaller than the minimum price allowed in any new locations Play may launch in.", "format": "double", "type": "number"}}, "type": "object"}, "OtherRegionsSubscriptionOfferPhaseFreePriceOverride": {"description": "Represents the free price override configuration for any new locations Play may launch for a single offer phase.", "id": "OtherRegionsSubscriptionOfferPhaseFreePriceOverride", "properties": {}, "type": "object"}, "OtherRegionsSubscriptionOfferPhasePrices": {"description": "Pricing information for any new locations Play may launch in.", "id": "OtherRegionsSubscriptionOfferPhasePrices", "properties": {"eurPrice": {"$ref": "Money", "description": "Required. Price in EUR to use for any new locations Play may launch in."}, "usdPrice": {"$ref": "Money", "description": "Required. Price in USD to use for any new locations Play may launch in."}}, "type": "object"}, "PageInfo": {"description": "Information about the current page. List operations that supports paging return only one \"page\" of results. This protocol buffer message describes the page that has been returned.", "id": "PageInfo", "properties": {"resultPerPage": {"description": "Maximum number of results returned in one page. ! The number of results included in the API response.", "format": "int32", "type": "integer"}, "startIndex": {"description": "Index of the first result returned in the current page.", "format": "int32", "type": "integer"}, "totalResults": {"description": "Total number of results available on the backend ! The total number of results in the result set.", "format": "int32", "type": "integer"}}, "type": "object"}, "PaidAppDetails": {"description": "Details of a paid app purchase.", "id": "PaidAppDetails", "properties": {}, "type": "object"}, "PartialRefund": {"description": "A partial refund of a transaction.", "id": "PartialRefund", "properties": {"refundId": {"description": "Required. A unique id distinguishing this partial refund. If the refund is successful, subsequent refunds with the same id will fail. Must be unique across refunds for one individual transaction.", "type": "string"}, "refundPreTaxAmount": {"$ref": "Price", "description": "Required. The pre-tax amount of the partial refund. Should be less than the remaining pre-tax amount of the transaction."}}, "type": "object"}, "PartialRefundEvent": {"description": "Details of the partial refund events for this order.", "id": "PartialRefundEvent", "properties": {"createTime": {"description": "The time when the partial refund was created.", "format": "google-datetime", "type": "string"}, "processTime": {"description": "The time when the partial refund was processed.", "format": "google-datetime", "type": "string"}, "refundDetails": {"$ref": "RefundDetails", "description": "Details for the partial refund."}, "state": {"description": "The state of the partial refund.", "enum": ["STATE_UNSPECIFIED", "PENDING", "PROCESSED_SUCCESSFULLY"], "enumDescriptions": ["State unspecified. This value is not used.", "The partial refund has been created, but not yet processed.", "The partial refund was processed successfully."], "type": "string"}}, "type": "object"}, "PausedStateContext": {"description": "Information specific to a subscription in paused state.", "id": "PausedStateContext", "properties": {"autoResumeTime": {"description": "Time at which the subscription will be automatically resumed.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "PendingCancellation": {"description": "This is an indicator of whether there is a pending cancellation on the virtual installment plan. The cancellation will happen only after the user finished all committed payments.", "id": "PendingCancellation", "properties": {}, "type": "object"}, "PointsDetails": {"description": "Details relating to any Play Points applied to an order.", "id": "PointsDetails", "properties": {"pointsCouponValue": {"$ref": "Money", "description": "The monetary value of a Play Points coupon. This is the discount the coupon provides, which may not be the total amount. Only set when Play Points coupons have been used. E.g. for a 100 points for $2 coupon, this is $2."}, "pointsDiscountRateMicros": {"description": "The percentage rate which the Play Points promotion reduces the cost by. E.g. for a 100 points for $2 coupon, this is 500,000. Since $2 has an estimate of 200 points, but the actual Points required, 100, is 50% of this, and 50% in micros is 500,000. Between 0 and 1,000,000.", "format": "int64", "type": "string"}, "pointsOfferId": {"description": "ID unique to the play points offer in use for this order.", "type": "string"}, "pointsSpent": {"description": "The number of Play Points applied in this order. E.g. for a 100 points for $2 coupon, this is 100. For coupon stacked with base offer, this is the total points spent across both.", "format": "int64", "type": "string"}}, "type": "object"}, "PrepaidBasePlanType": {"description": "Represents a base plan that does not automatically renew at the end of the base plan, and must be manually renewed by the user.", "id": "PrepaidBasePlanType", "properties": {"billingPeriodDuration": {"description": "Required. Immutable. Subscription period, specified in ISO 8601 format. For a list of acceptable billing periods, refer to the help center. The duration is immutable after the base plan is created.", "type": "string"}, "timeExtension": {"description": "Whether users should be able to extend this prepaid base plan in Google Play surfaces. Defaults to TIME_EXTENSION_ACTIVE if not specified.", "enum": ["TIME_EXTENSION_UNSPECIFIED", "TIME_EXTENSION_ACTIVE", "TIME_EXTENSION_INACTIVE"], "enumDescriptions": ["Unspecified state.", "Time extension is active. Users are allowed to top-up or extend their prepaid plan.", "Time extension is inactive. Users cannot top-up or extend their prepaid plan."], "type": "string"}}, "type": "object"}, "PrepaidPlan": {"description": "Information related to a prepaid plan.", "id": "PrepaidPlan", "properties": {"allowExtendAfterTime": {"description": "If present, this is the time after which top up purchases are allowed for the prepaid plan. Will not be present for expired prepaid plans.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "Price": {"description": "Definition of a price, i.e. currency and units.", "id": "Price", "properties": {"currency": {"description": "3 letter Currency code, as defined by ISO 4217. See java/com/google/common/money/CurrencyCode.java", "type": "string"}, "priceMicros": {"description": "Price in 1/million of the currency base unit, represented as a string.", "type": "string"}}, "type": "object"}, "ProcessedEvent": {"description": "Details of when the order was processed.", "id": "ProcessedEvent", "properties": {"eventTime": {"description": "The time when the order was processed.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "ProductLineItem": {"description": "Contains item-level info for a ProductPurchaseV2.", "id": "ProductLineItem", "properties": {"productId": {"description": "The purchased product ID (for example, 'monthly001').", "type": "string"}, "productOfferDetails": {"$ref": "ProductOfferDetails", "description": "The offer details for this item."}}, "type": "object"}, "ProductOfferDetails": {"description": "Offer details information related to a purchase line item.", "id": "ProductOfferDetails", "properties": {"consumptionState": {"description": "Output only. The consumption state of the purchase.", "enum": ["CONSUMPTION_STATE_UNSPECIFIED", "CONSUMPTION_STATE_YET_TO_BE_CONSUMED", "CONSUMPTION_STATE_CONSUMED"], "enumDescriptions": ["Consumption state unspecified. This value should never be set.", "Yet to be consumed.", "Consumed already."], "readOnly": true, "type": "string"}, "offerId": {"description": "The offer ID. Only present for offers.", "type": "string"}, "offerTags": {"description": "The latest offer tags associated with the offer. It includes tags inherited from the purchase option.", "items": {"type": "string"}, "type": "array"}, "offerToken": {"description": "The per-transaction offer token used to make this purchase line item.", "type": "string"}, "purchaseOptionId": {"description": "The purchase option ID.", "type": "string"}, "quantity": {"description": "The quantity associated with the purchase of the inapp product.", "format": "int32", "type": "integer"}, "refundableQuantity": {"description": "The quantity eligible for refund, i.e. quantity that hasn't been refunded. The value reflects quantity-based partial refunds and full refunds.", "format": "int32", "type": "integer"}, "rentOfferDetails": {"$ref": "RentOfferDetails", "description": "Offer details about rent offers. This will only be set for rental line items."}}, "type": "object"}, "ProductPurchase": {"description": "A ProductPurchase resource indicates the status of a user's inapp product purchase.", "id": "ProductPurchase", "properties": {"acknowledgementState": {"description": "The acknowledgement state of the inapp product. Possible values are: 0. Yet to be acknowledged 1. Acknowledged", "format": "int32", "type": "integer"}, "consumptionState": {"description": "The consumption state of the inapp product. Possible values are: 0. Yet to be consumed 1. Consumed", "format": "int32", "type": "integer"}, "developerPayload": {"description": "A developer-specified string that contains supplemental information about an order.", "type": "string"}, "kind": {"description": "This kind represents an inappPurchase object in the androidpublisher service.", "type": "string"}, "obfuscatedExternalAccountId": {"description": "An obfuscated version of the id that is uniquely associated with the user's account in your app. Only present if specified using https://developer.android.com/reference/com/android/billingclient/api/BillingFlowParams.Builder#setobfuscatedaccountid when the purchase was made.", "type": "string"}, "obfuscatedExternalProfileId": {"description": "An obfuscated version of the id that is uniquely associated with the user's profile in your app. Only present if specified using https://developer.android.com/reference/com/android/billingclient/api/BillingFlowParams.Builder#setobfuscatedprofileid when the purchase was made.", "type": "string"}, "orderId": {"description": "The order id associated with the purchase of the inapp product.", "type": "string"}, "productId": {"description": "The inapp product SKU. May not be present.", "type": "string"}, "purchaseState": {"description": "The purchase state of the order. Possible values are: 0. Purchased 1. Canceled 2. Pending", "format": "int32", "type": "integer"}, "purchaseTimeMillis": {"description": "The time the product was purchased, in milliseconds since the epoch (Jan 1, 1970).", "format": "int64", "type": "string"}, "purchaseToken": {"description": "The purchase token generated to identify this purchase. May not be present.", "type": "string"}, "purchaseType": {"description": "The type of purchase of the inapp product. This field is only set if this purchase was not made using the standard in-app billing flow. Possible values are: 0. Test (i.e. purchased from a license testing account) 1. Promo (i.e. purchased using a promo code). Does not include Play Points purchases. 2. Rewarded (i.e. from watching a video ad instead of paying)", "format": "int32", "type": "integer"}, "quantity": {"description": "The quantity associated with the purchase of the inapp product. If not present, the quantity is 1.", "format": "int32", "type": "integer"}, "refundableQuantity": {"description": "The quantity eligible for refund, i.e. quantity that hasn't been refunded. The value reflects quantity-based partial refunds and full refunds.", "format": "int32", "type": "integer"}, "regionCode": {"description": "ISO 3166-1 alpha-2 billing region code of the user at the time the product was granted.", "type": "string"}}, "type": "object"}, "ProductPurchaseV2": {"description": "A ProductPurchaseV2 resource indicates the status of a user's inapp product purchase.", "id": "ProductPurchaseV2", "properties": {"acknowledgementState": {"description": "Output only. The acknowledgement state of the purchase.", "enum": ["ACKNOWLEDGEMENT_STATE_UNSPECIFIED", "ACKNOWLEDGEMENT_STATE_PENDING", "ACKNOWLEDGEMENT_STATE_ACKNOWLEDGED"], "enumDescriptions": ["Unspecified acknowledgement state.", "The purchase is not acknowledged yet.", "The purchase is acknowledged."], "readOnly": true, "type": "string"}, "kind": {"description": "This kind represents a ProductPurchaseV2 object in the androidpublisher service.", "type": "string"}, "obfuscatedExternalAccountId": {"description": "An obfuscated version of the id that is uniquely associated with the user's account in your app. Only present if specified using https://developer.android.com/reference/com/android/billingclient/api/BillingFlowParams.Builder#setobfuscatedaccountid when the purchase was made.", "type": "string"}, "obfuscatedExternalProfileId": {"description": "An obfuscated version of the id that is uniquely associated with the user's profile in your app. Only present if specified using https://developer.android.com/reference/com/android/billingclient/api/BillingFlowParams.Builder#setobfuscatedprofileid when the purchase was made.", "type": "string"}, "orderId": {"description": "The order id associated with the purchase of the inapp product. May not be set if there is no order associated with the purchase.", "type": "string"}, "productLineItem": {"description": "Contains item-level info for a ProductPurchaseV2.", "items": {"$ref": "ProductLineItem"}, "type": "array"}, "purchaseCompletionTime": {"description": "The time when the purchase was successful, i.e., when the PurchaseState has changed to PURCHASED. This field will not be present until the payment is complete. For example, if the user initiated a pending transaction (https://developer.android.com/google/play/billing/integrate#pending), this field will not be populated until the user successfully completes the steps required to complete the transaction.", "format": "google-datetime", "type": "string"}, "purchaseStateContext": {"$ref": "PurchaseStateContext", "description": "Information about the purchase state of the purchase."}, "regionCode": {"description": "ISO 3166-1 alpha-2 billing region code of the user at the time the product was granted.", "type": "string"}, "testPurchaseContext": {"$ref": "TestPurchaseContext", "description": "Information related to test purchases. This will only be set for test purchases."}}, "type": "object"}, "ProductPurchasesAcknowledgeRequest": {"description": "Request for the product.purchases.acknowledge API.", "id": "ProductPurchasesAcknowledgeRequest", "properties": {"developerPayload": {"description": "Payload to attach to the purchase.", "type": "string"}}, "type": "object"}, "PurchaseOptionTaxAndComplianceSettings": {"description": "Details about taxation, Google Play policy and legal compliance for one-time product purchase options.", "id": "PurchaseOptionTaxAndComplianceSettings", "properties": {"withdrawalRightType": {"description": "Optional. Digital content or service classification for products distributed to users in eligible regions. If unset, it defaults to `WITHDRAWAL_RIGHT_DIGITAL_CONTENT`. Refer to the [Help Center article](https://support.google.com/googleplay/android-developer/answer/10463498) for more information.", "enum": ["WITHDRAWAL_RIGHT_TYPE_UNSPECIFIED", "WITHDRAWAL_RIGHT_DIGITAL_CONTENT", "WITHDRAWAL_RIGHT_SERVICE"], "enumDescriptions": ["", "", ""], "type": "string"}}, "type": "object"}, "PurchaseStateContext": {"description": "Context about the purchase state.", "id": "PurchaseStateContext", "properties": {"purchaseState": {"description": "Output only. The purchase state of the purchase.", "enum": ["PURCHASE_STATE_UNSPECIFIED", "PURCHASED", "CANCELLED", "PENDING"], "enumDescriptions": ["Purchase state unspecified. This value should never be set.", "Purchased successfully.", "Purchase canceled.", "The purchase is in a pending state and has not yet been completed. For more information on handling pending purchases, see https://developer.android.com/google/play/billing/integrate#pending."], "readOnly": true, "type": "string"}}, "type": "object"}, "RecurringExternalTransaction": {"description": "Represents a transaction that is part of a recurring series of payments. This can be a subscription or a one-time product with multiple payments (such as preorder).", "id": "RecurringExternalTransaction", "properties": {"externalSubscription": {"$ref": "ExternalSubscription", "description": "Details of an external subscription."}, "externalTransactionToken": {"description": "Input only. Provided during the call to Create. Retrieved from the client when the alternative billing flow is launched. Required only for the initial purchase.", "type": "string"}, "initialExternalTransactionId": {"description": "The external transaction id of the first transaction of this recurring series of transactions. For example, for a subscription this would be the transaction id of the first payment. Required when creating recurring external transactions.", "type": "string"}, "migratedTransactionProgram": {"description": "Input only. Provided during the call to C<PERSON>. Must only be used when migrating a subscription from manual monthly reporting to automated reporting.", "enum": ["EXTERNAL_TRANSACTION_PROGRAM_UNSPECIFIED", "USER_CHOICE_BILLING", "ALTERNATIVE_BILLING_ONLY"], "enumDescriptions": ["Unspecified transaction program. Not used.", "User choice billing, where a user may choose between Google Play Billing developer-managed billing.", "Alternative billing only, where users may only use developer-manager billing."], "type": "string"}, "otherRecurringProduct": {"$ref": "OtherRecurringProduct", "description": "Details of a recurring external transaction product which doesn't belong to any other specific category."}}, "type": "object"}, "RefundDetails": {"description": "Details for a partial or full refund.", "id": "RefundDetails", "properties": {"tax": {"$ref": "Money", "description": "The amount of tax refunded."}, "total": {"$ref": "Money", "description": "The total amount refunded, including tax."}}, "type": "object"}, "RefundEvent": {"description": "Details of when the order was fully refunded.", "id": "RefundEvent", "properties": {"eventTime": {"description": "The time when the order was fully refunded.", "format": "google-datetime", "type": "string"}, "refundDetails": {"$ref": "RefundDetails", "description": "Details for the full refund."}, "refundReason": {"description": "The reason the order was refunded.", "enum": ["REFUND_REASON_UNSPECIFIED", "OTHER", "CHARGEBACK"], "enumDescriptions": ["Refund reason unspecified. This value is not used.", "The order was refunded for a reason other than the listed reasons here.", "The order was charged back."], "type": "string"}}, "type": "object"}, "RefundExternalTransactionRequest": {"description": "A request to refund an existing external transaction.", "id": "RefundExternalTransactionRequest", "properties": {"fullRefund": {"$ref": "FullRefund", "description": "A full-amount refund."}, "partialRefund": {"$ref": "PartialRefund", "description": "A partial refund."}, "refundTime": {"description": "Required. The time that the transaction was refunded.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "RegionalBasePlanConfig": {"description": "Configuration for a base plan specific to a region.", "id": "RegionalBasePlanConfig", "properties": {"newSubscriberAvailability": {"description": "Whether the base plan in the specified region is available for new subscribers. Existing subscribers will not have their subscription canceled if this value is set to false. If not specified, this will default to false.", "type": "boolean"}, "price": {"$ref": "Money", "description": "The price of the base plan in the specified region. Must be set if the base plan is available to new subscribers. Must be set in the currency that is linked to the specified region."}, "regionCode": {"description": "Required. Region code this configuration applies to, as defined by ISO 3166-2, e.g. \"US\".", "type": "string"}}, "type": "object"}, "RegionalPriceMigrationConfig": {"description": "Configuration for migration of a legacy price cohort.", "id": "RegionalPriceMigrationConfig", "properties": {"oldestAllowedPriceVersionTime": {"description": "Required. Subscribers in all legacy price cohorts before this time will be migrated to the current price. Subscribers in any newer price cohorts are unaffected. Affected subscribers will receive one or more notifications from Google Play about the price change. Price decreases occur at the subscriber's next billing date. Price increases occur at the subscriber's next billing date following a notification period that varies by region and price increase type.", "format": "google-datetime", "type": "string"}, "priceIncreaseType": {"description": "Optional. The requested type of price increase", "enum": ["PRICE_INCREASE_TYPE_UNSPECIFIED", "PRICE_INCREASE_TYPE_OPT_IN", "PRICE_INCREASE_TYPE_OPT_OUT"], "enumDescriptions": ["Unspecified state.", "Subscribers must accept the price increase or their subscription is canceled.", "Subscribers are notified but do not have to accept the price increase. Opt-out price increases not meeting regional, frequency, and amount limits will proceed as opt-in price increase. The first opt-out price increase for each app must be initiated in the Google Play Console."], "type": "string"}, "regionCode": {"description": "Required. Region code this configuration applies to, as defined by ISO 3166-2, e.g. \"US\".", "type": "string"}}, "type": "object"}, "RegionalSubscriptionOfferConfig": {"description": "Configuration for a subscription offer in a single region.", "id": "RegionalSubscriptionOfferConfig", "properties": {"newSubscriberAvailability": {"description": "Whether the subscription offer in the specified region is available for new subscribers. Existing subscribers will not have their subscription cancelled if this value is set to false. If not specified, this will default to false.", "type": "boolean"}, "regionCode": {"description": "Required. Immutable. Region code this configuration applies to, as defined by ISO 3166-2, e.g. \"US\".", "type": "string"}}, "type": "object"}, "RegionalSubscriptionOfferPhaseConfig": {"description": "Configuration for a single phase of a subscription offer in a single region.", "id": "RegionalSubscriptionOfferPhaseConfig", "properties": {"absoluteDiscount": {"$ref": "Money", "description": "The absolute amount of money subtracted from the base plan price prorated over the phase duration that the user pays for this offer phase. For example, if the base plan price for this region is $12 for a period of 1 year, then a $1 absolute discount for a phase of a duration of 3 months would correspond to a price of $2. The resulting price may not be smaller than the minimum price allowed for this region."}, "free": {"$ref": "RegionalSubscriptionOfferPhaseFreePriceOverride", "description": "Set to specify this offer is free to obtain."}, "price": {"$ref": "Money", "description": "The absolute price the user pays for this offer phase. The price must not be smaller than the minimum price allowed for this region."}, "regionCode": {"description": "Required. Immutable. The region to which this config applies.", "type": "string"}, "relativeDiscount": {"description": "The fraction of the base plan price prorated over the phase duration that the user pays for this offer phase. For example, if the base plan price for this region is $12 for a period of 1 year, then a 50% discount for a phase of a duration of 3 months would correspond to a price of $1.50. The discount must be specified as a fraction strictly larger than 0 and strictly smaller than 1. The resulting price will be rounded to the nearest billable unit (e.g. cents for USD). The relative discount is considered invalid if the discounted price ends up being smaller than the minimum price allowed in this region.", "format": "double", "type": "number"}}, "type": "object"}, "RegionalSubscriptionOfferPhaseFreePriceOverride": {"description": "Represents the free price override configuration for a single phase of a subscription offer", "id": "RegionalSubscriptionOfferPhaseFreePriceOverride", "properties": {}, "type": "object"}, "RegionalTaxConfig": {"description": "Details about taxation in a given geographical region.", "id": "RegionalTaxConfig", "properties": {"eligibleForStreamingServiceTaxRate": {"description": "You must tell us if your app contains streaming products to correctly charge US state and local sales tax. Field only supported in the United States.", "type": "boolean"}, "regionCode": {"description": "Required. Region code this configuration applies to, as defined by ISO 3166-2, e.g. \"US\".", "type": "string"}, "streamingTaxType": {"description": "To collect communications or amusement taxes in the United States, choose the appropriate tax category. [Learn more](https://support.google.com/googleplay/android-developer/answer/10463498#streaming_tax).", "enum": ["STREAMING_TAX_TYPE_UNSPECIFIED", "STREAMING_TAX_TYPE_TELCO_VIDEO_RENTAL", "STREAMING_TAX_TYPE_TELCO_VIDEO_SALES", "STREAMING_TAX_TYPE_TELCO_VIDEO_MULTI_CHANNEL", "STREAMING_TAX_TYPE_TELCO_AUDIO_RENTAL", "STREAMING_TAX_TYPE_TELCO_AUDIO_SALES", "STREAMING_TAX_TYPE_TELCO_AUDIO_MULTI_CHANNEL"], "enumDescriptions": ["No telecommunications tax collected.", "US-specific telecommunications tax tier for video streaming, on demand, rentals / subscriptions / pay-per-view.", "US-specific telecommunications tax tier for video streaming of pre-recorded content like movies, tv shows.", "US-specific telecommunications tax tier for video streaming of multi-channel programming.", "US-specific telecommunications tax tier for audio streaming, rental / subscription.", "US-specific telecommunications tax tier for audio streaming, sale / permanent download.", "US-specific telecommunications tax tier for multi channel audio streaming like radio."], "type": "string"}, "taxTier": {"description": "Tax tier to specify reduced tax rate. Developers who sell digital news, magazines, newspapers, books, or audiobooks in various regions may be eligible for reduced tax rates. [Learn more](https://support.google.com/googleplay/android-developer/answer/10463498).", "enum": ["TAX_TIER_UNSPECIFIED", "TAX_TIER_BOOKS_1", "TAX_TIER_NEWS_1", "TAX_TIER_NEWS_2", "TAX_TIER_MUSIC_OR_AUDIO_1", "TAX_TIER_LIVE_OR_BROADCAST_1"], "enumDescriptions": ["", "", "", "", "", ""], "type": "string"}}, "type": "object"}, "RegionalTaxRateInfo": {"description": "Specified details about taxation in a given geographical region.", "id": "RegionalTaxRateInfo", "properties": {"eligibleForStreamingServiceTaxRate": {"description": "You must tell us if your app contains streaming products to correctly charge US state and local sales tax. Field only supported in the United States.", "type": "boolean"}, "streamingTaxType": {"description": "To collect communications or amusement taxes in the United States, choose the appropriate tax category. [Learn more](https://support.google.com/googleplay/android-developer/answer/10463498#streaming_tax).", "enum": ["STREAMING_TAX_TYPE_UNSPECIFIED", "STREAMING_TAX_TYPE_TELCO_VIDEO_RENTAL", "STREAMING_TAX_TYPE_TELCO_VIDEO_SALES", "STREAMING_TAX_TYPE_TELCO_VIDEO_MULTI_CHANNEL", "STREAMING_TAX_TYPE_TELCO_AUDIO_RENTAL", "STREAMING_TAX_TYPE_TELCO_AUDIO_SALES", "STREAMING_TAX_TYPE_TELCO_AUDIO_MULTI_CHANNEL"], "enumDescriptions": ["No telecommunications tax collected.", "US-specific telecommunications tax tier for video streaming, on demand, rentals / subscriptions / pay-per-view.", "US-specific telecommunications tax tier for video streaming of pre-recorded content like movies, tv shows.", "US-specific telecommunications tax tier for video streaming of multi-channel programming.", "US-specific telecommunications tax tier for audio streaming, rental / subscription.", "US-specific telecommunications tax tier for audio streaming, sale / permanent download.", "US-specific telecommunications tax tier for multi channel audio streaming like radio."], "type": "string"}, "taxTier": {"description": "Tax tier to specify reduced tax rate. Developers who sell digital news, magazines, newspapers, books, or audiobooks in various regions may be eligible for reduced tax rates. [Learn more](https://support.google.com/googleplay/android-developer/answer/10463498).", "enum": ["TAX_TIER_UNSPECIFIED", "TAX_TIER_BOOKS_1", "TAX_TIER_NEWS_1", "TAX_TIER_NEWS_2", "TAX_TIER_MUSIC_OR_AUDIO_1", "TAX_TIER_LIVE_OR_BROADCAST_1"], "enumDescriptions": ["", "", "", "", "", ""], "type": "string"}}, "type": "object"}, "Regions": {"description": "Region targeting data for app recovery action targeting.", "id": "Regions", "properties": {"regionCode": {"description": "Regions targeted by the recovery action. Region codes are ISO 3166 Alpha-2 country codes. For example, US stands for United States of America. See https://www.iso.org/iso-3166-country-codes.html for the complete list of country codes.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "RegionsVersion": {"description": "The version of the available regions being used for the specified resource.", "id": "RegionsVersion", "properties": {"version": {"description": "Required. A string representing the version of available regions being used for the specified resource. Regional prices and latest supported version for the resource have to be specified according to the information published in [this article](https://support.google.com/googleplay/android-developer/answer/10532353). Each time the supported locations substantially change, the version will be incremented. Using this field will ensure that creating and updating the resource with an older region's version and set of regional prices and currencies will succeed even though a new version is available.", "type": "string"}}, "type": "object"}, "RemoteInAppUpdate": {"description": "Object representation for Remote in-app update action type.", "id": "RemoteInAppUpdate", "properties": {"isRemoteInAppUpdateRequested": {"description": "Required. Set to true if Remote In-App Update action type is needed.", "type": "boolean"}}, "type": "object"}, "RemoteInAppUpdateData": {"description": "Data related to Remote In-App Update action such as recovered user count, affected user count etc.", "id": "RemoteInAppUpdateData", "properties": {"remoteAppUpdateDataPerBundle": {"description": "Data related to the recovery action at bundle level.", "items": {"$ref": "RemoteInAppUpdateDataPerBundle"}, "type": "array"}}, "type": "object"}, "RemoteInAppUpdateDataPerBundle": {"description": "Data related to the recovery action at bundle level.", "id": "RemoteInAppUpdateDataPerBundle", "properties": {"recoveredDeviceCount": {"description": "Total number of devices which have been rescued.", "format": "int64", "type": "string"}, "totalDeviceCount": {"description": "Total number of devices affected by this recovery action associated with bundle of the app.", "format": "int64", "type": "string"}, "versionCode": {"description": "Version Code corresponding to the target bundle.", "format": "int64", "type": "string"}}, "type": "object"}, "RentOfferDetails": {"description": "Offer details information related to a rental line item.", "id": "RentOfferDetails", "properties": {}, "type": "object"}, "RentalDetails": {"description": "Details of a rental purchase.", "id": "RentalDetails", "properties": {}, "type": "object"}, "ReplacementCancellation": {"description": "Information specific to cancellations caused by subscription replacement.", "id": "ReplacementCancellation", "properties": {}, "type": "object"}, "RestrictedPaymentCountries": {"description": "Countries where the purchase of this product is restricted to payment methods registered in the same country. If empty, no payment location restrictions are imposed.", "id": "RestrictedPaymentCountries", "properties": {"regionCodes": {"description": "Required. Region codes to impose payment restrictions on, as defined by ISO 3166-2, e.g. \"US\".", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Review": {"description": "An Android app review.", "id": "Review", "properties": {"authorName": {"description": "The name of the user who wrote the review.", "type": "string"}, "comments": {"description": "A repeated field containing comments for the review.", "items": {"$ref": "Comment"}, "type": "array"}, "reviewId": {"description": "Unique identifier for this review.", "type": "string"}}, "type": "object"}, "ReviewReplyResult": {"description": "The result of replying/updating a reply to review.", "id": "ReviewReplyResult", "properties": {"lastEdited": {"$ref": "Timestamp", "description": "The time at which the reply took effect."}, "replyText": {"description": "The reply text that was applied.", "type": "string"}}, "type": "object"}, "ReviewsListResponse": {"description": "Response listing reviews.", "id": "ReviewsListResponse", "properties": {"pageInfo": {"$ref": "PageInfo", "description": "Information about the current page."}, "reviews": {"description": "List of reviews.", "items": {"$ref": "Review"}, "type": "array"}, "tokenPagination": {"$ref": "TokenPagination", "description": "Pagination token, to handle a number of products that is over one page."}}, "type": "object"}, "ReviewsReplyRequest": {"description": "Request to reply to review or update existing reply.", "id": "ReviewsReplyRequest", "properties": {"replyText": {"description": "The text to set as the reply. Replies of more than approximately 350 characters will be rejected. HTML tags will be stripped.", "type": "string"}}, "type": "object"}, "ReviewsReplyResponse": {"description": "Response on status of replying to a review.", "id": "ReviewsReplyResponse", "properties": {"result": {"$ref": "ReviewReplyResult", "description": "The result of replying/updating a reply to review."}}, "type": "object"}, "RevocationContext": {"description": "Revocation context of the purchases.subscriptionsv2.revoke API.", "id": "RevocationContext", "properties": {"fullRefund": {"$ref": "RevocationContextFullRefund", "description": "Optional. Used when users should be refunded the full amount of latest charge on each item in the subscription."}, "itemBasedRefund": {"$ref": "RevocationContextItemBasedRefund", "description": "Optional. Used when a specific item should be refunded in a subscription with add-on items."}, "proratedRefund": {"$ref": "RevocationContextProratedRefund", "description": "Optional. Used when users should be refunded a prorated amount they paid for their subscription based on the amount of time remaining in a subscription."}}, "type": "object"}, "RevocationContextFullRefund": {"description": "Used to determine if the refund type in the RevocationContext is a full refund.", "id": "RevocationContextFullRefund", "properties": {}, "type": "object"}, "RevocationContextItemBasedRefund": {"description": "Used to determine what specific item to revoke in a subscription with multiple items.", "id": "RevocationContextItemBasedRefund", "properties": {"productId": {"description": "Required. If the subscription is a subscription with add-ons, the product id of the subscription item to revoke.", "type": "string"}}, "type": "object"}, "RevocationContextProratedRefund": {"description": "Used to determine if the refund type in the RevocationContext is a prorated refund.", "id": "RevocationContextProratedRefund", "properties": {}, "type": "object"}, "RevokeSubscriptionPurchaseRequest": {"description": "Request for the purchases.subscriptionsv2.revoke API.", "id": "RevokeSubscriptionPurchaseRequest", "properties": {"revocationContext": {"$ref": "RevocationContext", "description": "Required. Additional details around the subscription revocation."}}, "type": "object"}, "RevokeSubscriptionPurchaseResponse": {"description": "Response for the purchases.subscriptionsv2.revoke API.", "id": "RevokeSubscriptionPurchaseResponse", "properties": {}, "type": "object"}, "SafetyLabelsUpdateRequest": {"description": "Request to update Safety Labels of an app.", "id": "SafetyLabelsUpdateRequest", "properties": {"safetyLabels": {"description": "Required. Contents of the CSV file containing Data Safety responses. For the format of this file, see the Help Center documentation at https://support.google.com/googleplay/android-developer/answer/10787469?#zippy=%2Cunderstand-the-csv-format To download an up to date template, follow the steps at https://support.google.com/googleplay/android-developer/answer/10787469?#zippy=%2Cexport-to-a-csv-file", "type": "string"}}, "type": "object"}, "SafetyLabelsUpdateResponse": {"description": "Response for SafetyLabelsUpdate rpc.", "id": "SafetyLabelsUpdateResponse", "properties": {}, "type": "object"}, "ScreenDensity": {"description": "Represents a screen density.", "id": "ScreenDensity", "properties": {"densityAlias": {"description": "Alias for a screen density.", "enum": ["DENSITY_UNSPECIFIED", "NODPI", "LDPI", "MDPI", "TVDPI", "HDPI", "XHDPI", "XXHDPI", "XXXHDPI"], "enumDescriptions": ["Unspecified screen density.", "NODPI screen density.", "LDPI screen density.", "MDPI screen density.", "TVDPI screen density.", "HDPI screen density.", "XHDPI screen density.", "XXHDPI screen density.", "XXXHDPI screen density."], "type": "string"}, "densityDpi": {"description": "Value for density dpi.", "format": "int32", "type": "integer"}}, "type": "object"}, "ScreenDensityTargeting": {"description": "Targeting based on screen density.", "id": "ScreenDensityTargeting", "properties": {"alternatives": {"description": "Targeting of other sibling directories that were in the Bundle. For main splits this is targeting of other main splits.", "items": {"$ref": "ScreenDensity"}, "type": "array"}, "value": {"description": "Value of a screen density.", "items": {"$ref": "ScreenDensity"}, "type": "array"}}, "type": "object"}, "SdkVersion": {"description": "Represents an sdk version.", "id": "SdkVersion", "properties": {"min": {"description": "Inclusive minimum value of an sdk version.", "format": "int32", "type": "integer"}}, "type": "object"}, "SdkVersionTargeting": {"description": "Targeting based on sdk version.", "id": "SdkVersionTargeting", "properties": {"alternatives": {"description": "Targeting of other sibling directories that were in the Bundle. For main splits this is targeting of other main splits.", "items": {"$ref": "SdkVersion"}, "type": "array"}, "value": {"description": "Value of an sdk version.", "items": {"$ref": "SdkVersion"}, "type": "array"}}, "type": "object"}, "SignupPromotion": {"description": "The promotion applied on this item when purchased.", "id": "SignupPromotion", "properties": {"oneTimeCode": {"$ref": "OneTimeCode", "description": "A one-time code was applied."}, "vanityCode": {"$ref": "VanityCode", "description": "A vanity code was applied."}}, "type": "object"}, "SplitApkMetadata": {"description": "Holds data specific to Split APKs.", "id": "SplitApkMetadata", "properties": {"isMasterSplit": {"description": "Indicates whether this APK is the main split of the module.", "type": "boolean"}, "splitId": {"description": "Id of the split.", "type": "string"}}, "type": "object"}, "SplitApkVariant": {"description": "Variant is a group of APKs that covers a part of the device configuration space. APKs from multiple variants are never combined on one device.", "id": "SplitApkVariant", "properties": {"apkSet": {"description": "Set of APKs, one set per module.", "items": {"$ref": "ApkSet"}, "type": "array"}, "targeting": {"$ref": "VariantTargeting", "description": "Variant-level targeting."}, "variantNumber": {"description": "Number of the variant, starting at 0 (unless overridden). A device will receive APKs from the first variant that matches the device configuration, with higher variant numbers having priority over lower variant numbers.", "format": "int32", "type": "integer"}}, "type": "object"}, "StandaloneApkMetadata": {"description": "Holds data specific to Standalone APKs.", "id": "StandaloneApkMetadata", "properties": {"fusedModuleName": {"description": "Names of the modules fused in this standalone APK.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "SubscribeWithGoogleInfo": {"description": "Information associated with purchases made with 'Subscribe with Google'.", "id": "SubscribeWithGoogleInfo", "properties": {"emailAddress": {"description": "The email address of the user when the subscription was purchased.", "type": "string"}, "familyName": {"description": "The family name of the user when the subscription was purchased.", "type": "string"}, "givenName": {"description": "The given name of the user when the subscription was purchased.", "type": "string"}, "profileId": {"description": "The Google profile id of the user when the subscription was purchased.", "type": "string"}, "profileName": {"description": "The profile name of the user when the subscription was purchased.", "type": "string"}}, "type": "object"}, "Subscription": {"description": "A single subscription for an app.", "id": "Subscription", "properties": {"archived": {"deprecated": true, "description": "Output only. Deprecated: subscription archiving is not supported.", "readOnly": true, "type": "boolean"}, "basePlans": {"description": "The set of base plans for this subscription. Represents the prices and duration of the subscription if no other offers apply.", "items": {"$ref": "BasePlan"}, "type": "array"}, "listings": {"description": "Required. List of localized listings for this subscription. Must contain at least an entry for the default language of the parent app.", "items": {"$ref": "SubscriptionListing"}, "type": "array"}, "packageName": {"description": "Immutable. Package name of the parent app.", "type": "string"}, "productId": {"description": "Immutable. Unique product ID of the product. Unique within the parent app. Product IDs must be composed of lower-case letters (a-z), numbers (0-9), underscores (_) and dots (.). It must start with a lower-case letter or number, and be between 1 and 40 (inclusive) characters in length.", "type": "string"}, "restrictedPaymentCountries": {"$ref": "RestrictedPaymentCountries", "description": "Optional. Countries where the purchase of this subscription is restricted to payment methods registered in the same country. If empty, no payment location restrictions are imposed."}, "taxAndComplianceSettings": {"$ref": "SubscriptionTaxAndComplianceSettings", "description": "Details about taxes and legal compliance."}}, "type": "object"}, "SubscriptionCancelSurveyResult": {"description": "Information provided by the user when they complete the subscription cancellation flow (cancellation reason survey).", "id": "SubscriptionCancelSurveyResult", "properties": {"cancelSurveyReason": {"description": "The cancellation reason the user chose in the survey. Possible values are: 0. Other 1. I don't use this service enough 2. Technical issues 3. Cost-related reasons 4. I found a better app", "format": "int32", "type": "integer"}, "userInputCancelReason": {"description": "The customized input cancel reason from the user. Only present when cancelReason is 0.", "type": "string"}}, "type": "object"}, "SubscriptionDeferralInfo": {"description": "A SubscriptionDeferralInfo contains the data needed to defer a subscription purchase to a future expiry time.", "id": "SubscriptionDeferralInfo", "properties": {"desiredExpiryTimeMillis": {"description": "The desired next expiry time to assign to the subscription, in milliseconds since the Epoch. The given time must be later/greater than the current expiry time for the subscription.", "format": "int64", "type": "string"}, "expectedExpiryTimeMillis": {"description": "The expected expiry time for the subscription. If the current expiry time for the subscription is not the value specified here, the deferral will not occur.", "format": "int64", "type": "string"}}, "type": "object"}, "SubscriptionDetails": {"description": "Details of a subscription purchase.", "id": "SubscriptionDetails", "properties": {"basePlanId": {"description": "The base plan ID of the subscription.", "type": "string"}, "offerId": {"description": "The offer ID for the current subscription offer.", "type": "string"}, "offerPhase": {"description": "The pricing phase for the billing period funded by this order.", "enum": ["OFFER_PHASE_UNSPECIFIED", "BASE", "INTRODUCTORY", "FREE_TRIAL"], "enumDescriptions": ["Offer phase unspecified. This value is not used.", "The order funds a base price period.", "The order funds an introductory pricing period.", "The order funds a free trial period."], "type": "string"}, "servicePeriodEndTime": {"description": "The end of the billing period funded by this order. This is a snapshot of the billing/service period end time at the moment the order was processed, and should be used only for accounting. To get the current end time of the subscription service period, use purchases.subscriptionsv2.get.", "format": "google-datetime", "type": "string"}, "servicePeriodStartTime": {"description": "The start of the billing period funded by this order. This is a snapshot of the billing/service period start time at the moment the order was processed, and should be used only for accounting.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "SubscriptionItemPriceChangeDetails": {"description": "Price change related information of a subscription item.", "id": "SubscriptionItemPriceChangeDetails", "properties": {"expectedNewPriceChargeTime": {"description": "The renewal time at which the price change will become effective for the user. This is subject to change(to a future time) due to cases where the renewal time shifts like pause. This field is only populated if the price change has not taken effect.", "format": "google-datetime", "type": "string"}, "newPrice": {"$ref": "Money", "description": "New recurring price for the subscription item."}, "priceChangeMode": {"description": "Price change mode specifies how the subscription item price is changing.", "enum": ["PRICE_CHANGE_MODE_UNSPECIFIED", "PRICE_DECREASE", "PRICE_INCREASE", "OPT_OUT_PRICE_INCREASE"], "enumDescriptions": ["Price change mode unspecified. This value should never be set.", "If the subscription price is decreasing.", "If the subscription price is increasing and the user needs to accept it.", "If the subscription price is increasing with opt out mode."], "type": "string"}, "priceChangeState": {"description": "State the price change is currently in.", "enum": ["PRICE_CHANGE_STATE_UNSPECIFIED", "OUTSTANDING", "CONFIRMED", "APPLIED", "CANCELED"], "enumDescriptions": ["Price change state unspecified. This value should not be used.", "Waiting for the user to agree for the price change.", "The price change is confirmed to happen for the user.", "The price change is applied, i.e. the user has started being charged the new price.", "The price change was canceled."], "type": "string"}}, "type": "object"}, "SubscriptionListing": {"description": "The consumer-visible metadata of a subscription.", "id": "SubscriptionListing", "properties": {"benefits": {"description": "A list of benefits shown to the user on platforms such as the Play Store and in restoration flows in the language of this listing. Plain text. Ordered list of at most four benefits.", "items": {"type": "string"}, "type": "array"}, "description": {"description": "The description of this subscription in the language of this listing. Maximum length - 80 characters. Plain text.", "type": "string"}, "languageCode": {"description": "Required. The language of this listing, as defined by BCP-47, e.g. \"en-US\".", "type": "string"}, "title": {"description": "Required. The title of this subscription in the language of this listing. Plain text.", "type": "string"}}, "type": "object"}, "SubscriptionOffer": {"description": "A single, temporary offer", "id": "SubscriptionOffer", "properties": {"basePlanId": {"description": "Required. Immutable. The ID of the base plan to which this offer is an extension.", "type": "string"}, "offerId": {"description": "Required. Immutable. Unique ID of this subscription offer. Must be unique within the base plan.", "type": "string"}, "offerTags": {"description": "List of up to 20 custom tags specified for this offer, and returned to the app through the billing library.", "items": {"$ref": "OfferTag"}, "type": "array"}, "otherRegionsConfig": {"$ref": "OtherRegionsSubscriptionOfferConfig", "description": "The configuration for any new locations Play may launch in the future."}, "packageName": {"description": "Required. Immutable. The package name of the app the parent subscription belongs to.", "type": "string"}, "phases": {"description": "Required. The phases of this subscription offer. Must contain at least one and at most two entries. Users will always receive all these phases in the specified order.", "items": {"$ref": "SubscriptionOfferPhase"}, "type": "array"}, "productId": {"description": "Required. Immutable. The ID of the parent subscription this offer belongs to.", "type": "string"}, "regionalConfigs": {"description": "Required. The region-specific configuration of this offer. Must contain at least one entry.", "items": {"$ref": "RegionalSubscriptionOfferConfig"}, "type": "array"}, "state": {"description": "Output only. The current state of this offer. Can be changed using Activate and Deactivate actions. NB: the base plan state supersedes this state, so an active offer may not be available if the base plan is not active.", "enum": ["STATE_UNSPECIFIED", "DRAFT", "ACTIVE", "INACTIVE"], "enumDescriptions": ["Default value, should never be used.", "The subscription offer is not and has never been available to users.", "The subscription offer is available to new and existing users.", "The subscription offer is not available to new users. Existing users retain access."], "readOnly": true, "type": "string"}, "targeting": {"$ref": "SubscriptionOfferTargeting", "description": "The requirements that users need to fulfil to be eligible for this offer. Represents the requirements that Play will evaluate to decide whether an offer should be returned. Developers may further filter these offers themselves."}}, "type": "object"}, "SubscriptionOfferPhase": {"description": "A single phase of a subscription offer.", "id": "SubscriptionOfferPhase", "properties": {"duration": {"description": "Required. The duration of a single recurrence of this phase. Specified in ISO 8601 format.", "type": "string"}, "otherRegionsConfig": {"$ref": "OtherRegionsSubscriptionOfferPhaseConfig", "description": "Pricing information for any new locations Play may launch in."}, "recurrenceCount": {"description": "Required. The number of times this phase repeats. If this offer phase is not free, each recurrence charges the user the price of this offer phase.", "format": "int32", "type": "integer"}, "regionalConfigs": {"description": "Required. The region-specific configuration of this offer phase. This list must contain exactly one entry for each region for which the subscription offer has a regional config.", "items": {"$ref": "RegionalSubscriptionOfferPhaseConfig"}, "type": "array"}}, "type": "object"}, "SubscriptionOfferTargeting": {"description": "Defines the rule a user needs to satisfy to receive this offer.", "id": "SubscriptionOfferTargeting", "properties": {"acquisitionRule": {"$ref": "AcquisitionTargetingRule", "description": "Offer targeting rule for new user acquisition."}, "upgradeRule": {"$ref": "UpgradeTargetingRule", "description": "Offer targeting rule for upgrading users' existing plans."}}, "type": "object"}, "SubscriptionPriceChange": {"description": "Contains the price change information for a subscription that can be used to control the user journey for the price change in the app. This can be in the form of seeking confirmation from the user or tailoring the experience for a successful conversion.", "id": "SubscriptionPriceChange", "properties": {"newPrice": {"$ref": "Price", "description": "The new price the subscription will renew with if the price change is accepted by the user."}, "state": {"description": "The current state of the price change. Possible values are: 0. Outstanding: State for a pending price change waiting for the user to agree. In this state, you can optionally seek confirmation from the user using the In-App API. 1. Accepted: State for an accepted price change that the subscription will renew with unless it's canceled. The price change takes effect on a future date when the subscription renews. Note that the change might not occur when the subscription is renewed next.", "format": "int32", "type": "integer"}}, "type": "object"}, "SubscriptionPurchase": {"description": "A SubscriptionPurchase resource indicates the status of a user's subscription purchase.", "id": "SubscriptionPurchase", "properties": {"acknowledgementState": {"description": "The acknowledgement state of the subscription product. Possible values are: 0. Yet to be acknowledged 1. Acknowledged", "format": "int32", "type": "integer"}, "autoRenewing": {"description": "Whether the subscription will automatically be renewed when it reaches its current expiry time.", "type": "boolean"}, "autoResumeTimeMillis": {"description": "Time at which the subscription will be automatically resumed, in milliseconds since the Epoch. Only present if the user has requested to pause the subscription.", "format": "int64", "type": "string"}, "cancelReason": {"description": "The reason why a subscription was canceled or is not auto-renewing. Possible values are: 0. User canceled the subscription 1. Subscription was canceled by the system, for example because of a billing problem 2. Subscription was replaced with a new subscription 3. Subscription was canceled by the developer", "format": "int32", "type": "integer"}, "cancelSurveyResult": {"$ref": "SubscriptionCancelSurveyResult", "description": "Information provided by the user when they complete the subscription cancellation flow (cancellation reason survey)."}, "countryCode": {"description": "ISO 3166-1 alpha-2 billing country/region code of the user at the time the subscription was granted.", "type": "string"}, "developerPayload": {"description": "A developer-specified string that contains supplemental information about an order.", "type": "string"}, "emailAddress": {"description": "The email address of the user when the subscription was purchased. Only present for purchases made with 'Subscribe with Google'.", "type": "string"}, "expiryTimeMillis": {"description": "Time at which the subscription will expire, in milliseconds since the Epoch.", "format": "int64", "type": "string"}, "externalAccountId": {"description": "User account identifier in the third-party service. Only present if account linking happened as part of the subscription purchase flow.", "type": "string"}, "familyName": {"description": "The family name of the user when the subscription was purchased. Only present for purchases made with 'Subscribe with Google'.", "type": "string"}, "givenName": {"description": "The given name of the user when the subscription was purchased. Only present for purchases made with 'Subscribe with Google'.", "type": "string"}, "introductoryPriceInfo": {"$ref": "IntroductoryPriceInfo", "description": "Introductory price information of the subscription. This is only present when the subscription was purchased with an introductory price. This field does not indicate the subscription is currently in introductory price period."}, "kind": {"description": "This kind represents a subscriptionPurchase object in the androidpublisher service.", "type": "string"}, "linkedPurchaseToken": {"description": "The purchase token of the originating purchase if this subscription is one of the following: 0. Re-signup of a canceled but non-lapsed subscription 1. Upgrade/downgrade from a previous subscription For example, suppose a user originally signs up and you receive purchase token X, then the user cancels and goes through the resignup flow (before their subscription lapses) and you receive purchase token Y, and finally the user upgrades their subscription and you receive purchase token Z. If you call this API with purchase token Z, this field will be set to Y. If you call this API with purchase token Y, this field will be set to X. If you call this API with purchase token X, this field will not be set.", "type": "string"}, "obfuscatedExternalAccountId": {"description": "An obfuscated version of the id that is uniquely associated with the user's account in your app. Present for the following purchases: * If account linking happened as part of the subscription purchase flow. * It was specified using https://developer.android.com/reference/com/android/billingclient/api/BillingFlowParams.Builder#setobfuscatedaccountid when the purchase was made.", "type": "string"}, "obfuscatedExternalProfileId": {"description": "An obfuscated version of the id that is uniquely associated with the user's profile in your app. Only present if specified using https://developer.android.com/reference/com/android/billingclient/api/BillingFlowParams.Builder#setobfuscatedprofileid when the purchase was made.", "type": "string"}, "orderId": {"description": "The order id of the latest recurring order associated with the purchase of the subscription. If the subscription was canceled because payment was declined, this will be the order id from the payment declined order.", "type": "string"}, "paymentState": {"description": "The payment state of the subscription. Possible values are: 0. Payment pending 1. Payment received 2. Free trial 3. Pending deferred upgrade/downgrade Not present for canceled, expired subscriptions.", "format": "int32", "type": "integer"}, "priceAmountMicros": {"description": "Price of the subscription, For tax exclusive countries, the price doesn't include tax. For tax inclusive countries, the price includes tax. Price is expressed in micro-units, where 1,000,000 micro-units represents one unit of the currency. For example, if the subscription price is €1.99, price_amount_micros is 1990000.", "format": "int64", "type": "string"}, "priceChange": {"$ref": "SubscriptionPriceChange", "description": "The latest price change information available. This is present only when there is an upcoming price change for the subscription yet to be applied. Once the subscription renews with the new price or the subscription is canceled, no price change information will be returned."}, "priceCurrencyCode": {"description": "ISO 4217 currency code for the subscription price. For example, if the price is specified in British pounds sterling, price_currency_code is \"GBP\".", "type": "string"}, "profileId": {"description": "The Google profile id of the user when the subscription was purchased. Only present for purchases made with 'Subscribe with Google'.", "type": "string"}, "profileName": {"description": "The profile name of the user when the subscription was purchased. Only present for purchases made with 'Subscribe with Google'.", "type": "string"}, "promotionCode": {"description": "The promotion code applied on this purchase. This field is only set if a vanity code promotion is applied when the subscription was purchased.", "type": "string"}, "promotionType": {"description": "The type of promotion applied on this purchase. This field is only set if a promotion is applied when the subscription was purchased. Possible values are: 0. One time code 1. Vanity code", "format": "int32", "type": "integer"}, "purchaseType": {"description": "The type of purchase of the subscription. This field is only set if this purchase was not made using the standard in-app billing flow. Possible values are: 0. Test (i.e. purchased from a license testing account) 1. Promo (i.e. purchased using a promo code)", "format": "int32", "type": "integer"}, "startTimeMillis": {"description": "Time at which the subscription was granted, in milliseconds since the Epoch.", "format": "int64", "type": "string"}, "userCancellationTimeMillis": {"description": "The time at which the subscription was canceled by the user, in milliseconds since the epoch. Only present if cancelReason is 0.", "format": "int64", "type": "string"}}, "type": "object"}, "SubscriptionPurchaseLineItem": {"description": "Item-level info for a subscription purchase.", "id": "SubscriptionPurchaseLineItem", "properties": {"autoRenewingPlan": {"$ref": "AutoRenewingPlan", "description": "The item is auto renewing."}, "deferredItemRemoval": {"$ref": "DeferredItemRemoval", "description": "Information for deferred item removal."}, "deferredItemReplacement": {"$ref": "DeferredItemReplacement", "description": "Information for deferred item replacement."}, "expiryTime": {"description": "Time at which the subscription expired or will expire unless the access is extended (ex. renews).", "format": "google-datetime", "type": "string"}, "latestSuccessfulOrderId": {"description": "The order id of the latest successful order associated with this item. Not present if the item is not owned by the user yet (e.g. the item being deferred replaced to).", "type": "string"}, "offerDetails": {"$ref": "OfferDetails", "description": "The offer details for this item."}, "prepaidPlan": {"$ref": "PrepaidPlan", "description": "The item is prepaid."}, "productId": {"description": "The purchased product ID (for example, 'monthly001').", "type": "string"}, "signupPromotion": {"$ref": "SignupPromotion", "description": "Promotion details about this item. Only set if a promotion was applied during signup."}}, "type": "object"}, "SubscriptionPurchaseV2": {"description": "Indicates the status of a user's subscription purchase.", "id": "SubscriptionPurchaseV2", "properties": {"acknowledgementState": {"description": "The acknowledgement state of the subscription.", "enum": ["ACKNOWLEDGEMENT_STATE_UNSPECIFIED", "ACKNOWLEDGEMENT_STATE_PENDING", "ACKNOWLEDGEMENT_STATE_ACKNOWLEDGED"], "enumDescriptions": ["Unspecified acknowledgement state.", "The subscription is not acknowledged yet.", "The subscription is acknowledged."], "type": "string"}, "canceledStateContext": {"$ref": "CanceledStateContext", "description": "Additional context around canceled subscriptions. Only present if the subscription currently has subscription_state SUBSCRIPTION_STATE_CANCELED or SUBSCRIPTION_STATE_EXPIRED."}, "externalAccountIdentifiers": {"$ref": "ExternalAccountIdentifiers", "description": "User account identifier in the third-party service."}, "kind": {"description": "This kind represents a SubscriptionPurchaseV2 object in the androidpublisher service.", "type": "string"}, "latestOrderId": {"deprecated": true, "description": "Deprecated: Use line_items.latest_successful_order_id instead. The order id of the latest order associated with the purchase of the subscription. For autoRenewing subscription, this is the order id of signup order if it is not renewed yet, or the last recurring order id (success, pending, or declined order). For prepaid subscription, this is the order id associated with the queried purchase token.", "type": "string"}, "lineItems": {"description": "Item-level info for a subscription purchase. The items in the same purchase should be either all with AutoRenewingPlan or all with PrepaidPlan.", "items": {"$ref": "SubscriptionPurchaseLineItem"}, "type": "array"}, "linkedPurchaseToken": {"description": "The purchase token of the old subscription if this subscription is one of the following: * Re-signup of a canceled but non-lapsed subscription * Upgrade/downgrade from a previous subscription. * Convert from prepaid to auto renewing subscription. * Convert from an auto renewing subscription to prepaid. * Topup a prepaid subscription.", "type": "string"}, "pausedStateContext": {"$ref": "PausedStateContext", "description": "Additional context around paused subscriptions. Only present if the subscription currently has subscription_state SUBSCRIPTION_STATE_PAUSED."}, "regionCode": {"description": "ISO 3166-1 alpha-2 billing country/region code of the user at the time the subscription was granted.", "type": "string"}, "startTime": {"description": "Time at which the subscription was granted. Not set for pending subscriptions (subscription was created but awaiting payment during signup).", "format": "google-datetime", "type": "string"}, "subscribeWithGoogleInfo": {"$ref": "SubscribeWithGoogleInfo", "description": "User profile associated with purchases made with 'Subscribe with Google'."}, "subscriptionState": {"description": "The current state of the subscription.", "enum": ["SUBSCRIPTION_STATE_UNSPECIFIED", "SUBSCRIPTION_STATE_PENDING", "SUBSCRIPTION_STATE_ACTIVE", "SUBSCRIPTION_STATE_PAUSED", "SUBSCRIPTION_STATE_IN_GRACE_PERIOD", "SUBSCRIPTION_STATE_ON_HOLD", "SUBSCRIPTION_STATE_CANCELED", "SUBSCRIPTION_STATE_EXPIRED", "SUBSCRIPTION_STATE_PENDING_PURCHASE_CANCELED"], "enumDescriptions": ["Unspecified subscription state.", "Subscription was created but awaiting payment during signup. In this state, all items are awaiting payment.", "Subscription is active. - (1) If the subscription is an auto renewing plan, at least one item is auto_renew_enabled and not expired. - (2) If the subscription is a prepaid plan, at least one item is not expired.", "Subscription is paused. The state is only available when the subscription is an auto renewing plan. In this state, all items are in paused state.", "Subscription is in grace period. The state is only available when the subscription is an auto renewing plan. In this state, all items are in grace period.", "Subscription is on hold (suspended). The state is only available when the subscription is an auto renewing plan. In this state, all items are on hold.", "Subscription is canceled but not expired yet. The state is only available when the subscription is an auto renewing plan. All items have auto_renew_enabled set to false.", "Subscription is expired. All items have expiry_time in the past.", "Pending transaction for subscription is canceled. If this pending purchase was for an existing subscription, use linked_purchase_token to get the current state of that subscription."], "type": "string"}, "testPurchase": {"$ref": "TestPurchase", "description": "Only present if this subscription purchase is a test purchase."}}, "type": "object"}, "SubscriptionPurchasesAcknowledgeRequest": {"description": "Request for the purchases.subscriptions.acknowledge API.", "id": "SubscriptionPurchasesAcknowledgeRequest", "properties": {"developerPayload": {"description": "Payload to attach to the purchase.", "type": "string"}}, "type": "object"}, "SubscriptionPurchasesDeferRequest": {"description": "Request for the purchases.subscriptions.defer API.", "id": "SubscriptionPurchasesDeferRequest", "properties": {"deferralInfo": {"$ref": "SubscriptionDeferralInfo", "description": "The information about the new desired expiry time for the subscription."}}, "type": "object"}, "SubscriptionPurchasesDeferResponse": {"description": "Response for the purchases.subscriptions.defer API.", "id": "SubscriptionPurchasesDeferResponse", "properties": {"newExpiryTimeMillis": {"description": "The new expiry time for the subscription in milliseconds since the Epoch.", "format": "int64", "type": "string"}}, "type": "object"}, "SubscriptionTaxAndComplianceSettings": {"description": "Details about taxation, Google Play policy, and legal compliance for subscription products.", "id": "SubscriptionTaxAndComplianceSettings", "properties": {"eeaWithdrawalRightType": {"description": "Digital content or service classification for products distributed to users in the European Economic Area (EEA). The withdrawal regime under EEA consumer laws depends on this classification. Refer to the [Help Center article](https://support.google.com/googleplay/android-developer/answer/10463498) for more information.", "enum": ["WITHDRAWAL_RIGHT_TYPE_UNSPECIFIED", "WITHDRAWAL_RIGHT_DIGITAL_CONTENT", "WITHDRAWAL_RIGHT_SERVICE"], "enumDescriptions": ["", "", ""], "type": "string"}, "isTokenizedDigitalAsset": {"description": "Whether this subscription is declared as a product representing a tokenized digital asset.", "type": "boolean"}, "taxRateInfoByRegionCode": {"additionalProperties": {"$ref": "RegionalTaxRateInfo"}, "description": "A mapping from region code to tax rate details. The keys are region codes as defined by Unicode's \"CLDR\".", "type": "object"}}, "type": "object"}, "SystemApkOptions": {"description": "Options for system APKs.", "id": "SystemApkOptions", "properties": {"rotated": {"description": "Whether to use the rotated key for signing the system APK.", "type": "boolean"}, "uncompressedDexFiles": {"description": "Whether system APK was generated with uncompressed dex files.", "type": "boolean"}, "uncompressedNativeLibraries": {"description": "Whether system APK was generated with uncompressed native libraries.", "type": "boolean"}}, "type": "object"}, "SystemApksListResponse": {"description": "Response to list previously created system APK variants.", "id": "SystemApksListResponse", "properties": {"variants": {"description": "All system APK variants created.", "items": {"$ref": "<PERSON><PERSON><PERSON>"}, "type": "array"}}, "type": "object"}, "SystemFeature": {"description": "Representation of a system feature.", "id": "SystemFeature", "properties": {"name": {"description": "The name of the feature.", "type": "string"}}, "type": "object"}, "SystemInitiatedCancellation": {"description": "Information specific to cancellations initiated by Google system.", "id": "SystemInitiatedCancellation", "properties": {}, "type": "object"}, "SystemOnChip": {"description": "Representation of a System-on-Chip (SoC) of an Android device. Can be used to target S+ devices.", "id": "SystemOnChip", "properties": {"manufacturer": {"description": "Required. The designer of the SoC, eg. \"Google\" Value of build property \"ro.soc.manufacturer\" https://developer.android.com/reference/android/os/Build#SOC_MANUFACTURER Required.", "type": "string"}, "model": {"description": "Required. The model of the SoC, eg. \"Tensor\" Value of build property \"ro.soc.model\" https://developer.android.com/reference/android/os/Build#SOC_MODEL Required.", "type": "string"}}, "type": "object"}, "Targeting": {"description": "Targeting details for a recovery action such as regions, android sdk levels, app versions etc.", "id": "Targeting", "properties": {"allUsers": {"$ref": "AllUsers", "description": "All users are targeted."}, "androidSdks": {"$ref": "AndroidSdks", "description": "Targeting is based on android api levels of devices."}, "regions": {"$ref": "Regions", "description": "Targeting is based on the user account region."}, "versionList": {"$ref": "AppVersionList", "description": "Target version codes as a list."}, "versionRange": {"$ref": "AppVersionRange", "description": "Target version codes as a range."}}, "type": "object"}, "TargetingInfo": {"description": "Targeting information about the generated apks.", "id": "TargetingInfo", "properties": {"assetSliceSet": {"description": "List of created asset slices.", "items": {"$ref": "AssetSliceSet"}, "type": "array"}, "packageName": {"description": "The package name of this app.", "type": "string"}, "variant": {"description": "List of the created variants.", "items": {"$ref": "SplitApkVariant"}, "type": "array"}}, "type": "object"}, "TargetingRuleScope": {"description": "Defines the scope of subscriptions which a targeting rule can match to target offers to users based on past or current entitlement.", "id": "TargetingRuleScope", "properties": {"anySubscriptionInApp": {"$ref": "TargetingRuleScopeAnySubscriptionInApp", "description": "The scope of the current targeting rule is any subscription in the parent app."}, "specificSubscriptionInApp": {"description": "The scope of the current targeting rule is the subscription with the specified subscription ID. Must be a subscription within the same parent app.", "type": "string"}, "thisSubscription": {"$ref": "TargetingRuleScopeThisSubscription", "description": "The scope of the current targeting rule is the subscription in which this offer is defined."}}, "type": "object"}, "TargetingRuleScopeAnySubscriptionInApp": {"description": "Represents the targeting rule scope corresponding to any subscription in the parent app.", "id": "TargetingRuleScopeAnySubscriptionInApp", "properties": {}, "type": "object"}, "TargetingRuleScopeThisSubscription": {"description": "Represents the targeting rule scope corresponding to the subscriptions in which this offer is defined.", "id": "TargetingRuleScopeThisSubscription", "properties": {}, "type": "object"}, "TargetingUpdate": {"description": "Update type for targeting. Note it is always a subset Targeting.", "id": "TargetingUpdate", "properties": {"allUsers": {"$ref": "AllUsers", "description": "All users are targeted."}, "androidSdks": {"$ref": "AndroidSdks", "description": "Additional android sdk levels are targeted by the recovery action."}, "regions": {"$ref": "Regions", "description": "Additional regions are targeted by the recovery action."}}, "type": "object"}, "TestPurchase": {"description": "Whether this subscription purchase is a test purchase.", "id": "TestPurchase", "properties": {}, "type": "object"}, "TestPurchaseContext": {"description": "Context about a test purchase.", "id": "TestPurchaseContext", "properties": {"fopType": {"description": "The fop type of the test purchase.", "enum": ["FOP_TYPE_UNSPECIFIED", "TEST"], "enumDescriptions": ["Fop type unspecified. This value should never be set.", "The purchase was made using a test card."], "type": "string"}}, "type": "object"}, "Testers": {"description": "The testers of an app. The resource for TestersService. Note: while it is possible in the Play Console UI to add testers via email lists, email lists are not supported by this resource.", "id": "Testers", "properties": {"googleGroups": {"description": "All testing Google Groups, as email addresses.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TextureCompressionFormat": {"description": "Represents texture compression format.", "id": "TextureCompressionFormat", "properties": {"alias": {"description": "Alias for texture compression format.", "enum": ["UNSPECIFIED_TEXTURE_COMPRESSION_FORMAT", "ETC1_RGB8", "PALETTED", "THREE_DC", "ATC", "LATC", "DXT1", "S3TC", "PVRTC", "ASTC", "ETC2"], "enumDescriptions": ["Unspecified format.", "ETC1_RGB8 format.", "PALETTED format.", "THREE_DC format.", "ATC format.", "LATC format.", "DXT1 format.", "S3TC format.", "PVRTC format.", "ASTC format.", "ETC2 format."], "type": "string"}}, "type": "object"}, "TextureCompressionFormatTargeting": {"description": "Targeting by a texture compression format.", "id": "TextureCompressionFormatTargeting", "properties": {"alternatives": {"description": "List of alternative TCFs (TCFs targeted by the sibling splits).", "items": {"$ref": "TextureCompressionFormat"}, "type": "array"}, "value": {"description": "The list of targeted TCFs. Should not be empty.", "items": {"$ref": "TextureCompressionFormat"}, "type": "array"}}, "type": "object"}, "Timestamp": {"description": "A Timestamp represents a point in time independent of any time zone or local calendar, encoded as a count of seconds and fractions of seconds at nanosecond resolution. The count is relative to an epoch at UTC midnight on January 1, 1970.", "id": "Timestamp", "properties": {"nanos": {"description": "Non-negative fractions of a second at nanosecond resolution. Must be from 0 to 999,999,999 inclusive.", "format": "int32", "type": "integer"}, "seconds": {"description": "Represents seconds of UTC time since Unix epoch.", "format": "int64", "type": "string"}}, "type": "object"}, "TokenPagination": {"description": "Pagination information returned by a List operation when token pagination is enabled. List operations that supports paging return only one \"page\" of results. This protocol buffer message describes the page that has been returned. When using token pagination, clients should use the next/previous token to get another page of the result. The presence or absence of next/previous token indicates whether a next/previous page is available and provides a mean of accessing this page. ListRequest.page_token should be set to either next_page_token or previous_page_token to access another page.", "id": "TokenPagination", "properties": {"nextPageToken": {"description": "Tokens to pass to the standard list field 'page_token'. Whenever available, tokens are preferred over manipulating start_index.", "type": "string"}, "previousPageToken": {"type": "string"}}, "type": "object"}, "Track": {"description": "A track configuration. The resource for TracksService.", "id": "Track", "properties": {"releases": {"description": "In a read request, represents all active releases in the track. In an update request, represents desired changes.", "items": {"$ref": "TrackRelease"}, "type": "array"}, "track": {"description": "Identifier of the track. Form factor tracks have a special prefix as an identifier, for example `wear:production`, `automotive:production`. [More on track name](https://developers.google.com/android-publisher/tracks#ff-track-name)", "type": "string"}}, "type": "object"}, "TrackConfig": {"description": "Configurations of the new track.", "id": "TrackConfig", "properties": {"formFactor": {"description": "Required. Form factor of the new track. Defaults to the default track.", "enum": ["FORM_FACTOR_UNSPECIFIED", "DEFAULT", "WEAR", "AUTOMOTIVE"], "enumDescriptions": ["Fallback value, do not use.", "Default track.", "Wear form factor track.", "Automotive form factor track."], "type": "string"}, "track": {"description": "Required. Identifier of the new track. For default tracks, this field consists of the track alias only. Form factor tracks have a special prefix as an identifier, for example `wear:production`, `automotive:production`. This prefix must match the value of the `form_factor` field, if it is not a default track. [More on track name](https://developers.google.com/android-publisher/tracks#ff-track-name)", "type": "string"}, "type": {"description": "Required. Type of the new track. Currently, the only supported value is closedTesting.", "enum": ["TRACK_TYPE_UNSPECIFIED", "CLOSED_TESTING"], "enumDescriptions": ["Fallback value, do not use.", "Closed testing track."], "type": "string"}}, "type": "object"}, "TrackCountryAvailability": {"description": "Resource for per-track country availability information.", "id": "TrackCountryAvailability", "properties": {"countries": {"description": "A list of one or more countries where artifacts in this track are available. This list includes all countries that are targeted by the track, even if only specific carriers are targeted in that country.", "items": {"$ref": "TrackTargetedCountry"}, "type": "array"}, "restOfWorld": {"description": "Whether artifacts in this track are available to \"rest of the world\" countries.", "type": "boolean"}, "syncWithProduction": {"description": "Whether this track's availability is synced with the default production track. See https://support.google.com/googleplay/android-developer/answer/7550024 for more information on syncing country availability with production. Note that if this is true, the returned \"countries\" and \"rest_of_world\" fields will reflect the values for the default production track.", "type": "boolean"}}, "type": "object"}, "TrackRelease": {"description": "A release within a track.", "id": "TrackRelease", "properties": {"countryTargeting": {"$ref": "CountryTargeting", "description": "Restricts a release to a specific set of countries. Note this is only allowed to be set for inProgress releases in the production track."}, "inAppUpdatePriority": {"description": "In-app update priority of the release. All newly added APKs in the release will be considered at this priority. Can take values in the range [0, 5], with 5 the highest priority. Defaults to 0. in_app_update_priority can not be updated once the release is rolled out. See https://developer.android.com/guide/playcore/in-app-updates.", "format": "int32", "type": "integer"}, "name": {"description": "The release name. Not required to be unique. If not set, the name is generated from the APK's version_name. If the release contains multiple APKs, the name is generated from the date.", "type": "string"}, "releaseNotes": {"description": "A description of what is new in this release.", "items": {"$ref": "LocalizedText"}, "type": "array"}, "status": {"description": "The status of the release.", "enum": ["statusUnspecified", "draft", "inProgress", "halted", "completed"], "enumDescriptions": ["Unspecified status.", "The release's APKs are not being served to users.", "The release's APKs are being served to a fraction of users, determined by 'user_fraction'.", "The release's APKs will no longer be served to users. Users who already have these APKs are unaffected.", "The release will have no further changes. Its APKs are being served to all users, unless they are eligible to APKs of a more recent release."], "type": "string"}, "userFraction": {"description": "Fraction of users who are eligible for a staged release. 0 < fraction < 1. Can only be set when status is \"inProgress\" or \"halted\".", "format": "double", "type": "number"}, "versionCodes": {"description": "Version codes of all APKs in the release. Must include version codes to retain from previous releases.", "items": {"format": "int64", "type": "string"}, "type": "array"}}, "type": "object"}, "TrackTargetedCountry": {"description": "Representation of a single country where the contents of a track are available.", "id": "TrackTargetedCountry", "properties": {"countryCode": {"description": "The country to target, as a two-letter CLDR code.", "type": "string"}}, "type": "object"}, "TracksListResponse": {"description": "Response listing all tracks.", "id": "TracksListResponse", "properties": {"kind": {"description": "The kind of this response (\"androidpublisher#tracksListResponse\").", "type": "string"}, "tracks": {"description": "All tracks (including tracks with no releases).", "items": {"$ref": "Track"}, "type": "array"}}, "type": "object"}, "UpdateBasePlanStateRequest": {"description": "Request message to update the state of a subscription base plan.", "id": "UpdateBasePlanStateRequest", "properties": {"activateBasePlanRequest": {"$ref": "ActivateBasePlanRequest", "description": "Activates a base plan. Once activated, base plans will be available to new subscribers."}, "deactivateBasePlanRequest": {"$ref": "DeactivateBasePlanRequest", "description": "Deactivates a base plan. Once deactivated, the base plan will become unavailable to new subscribers, but existing subscribers will maintain their subscription"}}, "type": "object"}, "UpdateOneTimeProductOfferRequest": {"description": "Request message for UpdateOneTimeProductOffer.", "id": "UpdateOneTimeProductOfferRequest", "properties": {"allowMissing": {"description": "Optional. If set to true, and the offer with the given package_name, product_id, purchase_option_id and offer_id doesn't exist, an offer will be created. If a new offer is created, the update_mask is ignored.", "type": "boolean"}, "latencyTolerance": {"description": "Optional. The latency tolerance for the propagation of this offer update. Defaults to latency-sensitive.", "enum": ["PRODUCT_UPDATE_LATENCY_TOLERANCE_UNSPECIFIED", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT"], "enumDescriptions": ["Defaults to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE.", "The update will propagate to clients within several minutes on average and up to a few hours in rare cases. Throughput is limited to 7,200 updates per app per hour.", "The update will propagate to clients within 24 hours. Supports high throughput of up to 720,000 updates per app per hour using batch modification methods."], "type": "string"}, "oneTimeProductOffer": {"$ref": "OneTimeProductOffer", "description": "Required. The one-time product offer to update."}, "regionsVersion": {"$ref": "RegionsVersion", "description": "Required. The version of the available regions being used for the offer."}, "updateMask": {"description": "Required. The list of fields to be updated.", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "UpdateOneTimeProductOfferStateRequest": {"description": "Request message to update the state of a one-time product offer.", "id": "UpdateOneTimeProductOfferStateRequest", "properties": {"activateOneTimeProductOfferRequest": {"$ref": "ActivateOneTimeProductOfferRequest", "description": "Activates an offer. Once activated, the offer is available to users, as long as its conditions are met."}, "cancelOneTimeProductOfferRequest": {"$ref": "CancelOneTimeProductOfferRequest", "description": "Cancels an offer. Once cancelled, the offer is not available to users. Any pending orders related to this offer will be cancelled. This state transition is specific to pre-orders."}, "deactivateOneTimeProductOfferRequest": {"$ref": "DeactivateOneTimeProductOfferRequest", "description": "Deactivates an offer. Once deactivated, the offer is no longer available to users. This state transition is specific to discounted offers."}}, "type": "object"}, "UpdateOneTimeProductRequest": {"description": "Request message for UpdateOneTimeProduct.", "id": "UpdateOneTimeProductRequest", "properties": {"allowMissing": {"description": "Optional. If set to true, and the one-time product with the given package_name and product_id doesn't exist, the one-time product will be created. If a new one-time product is created, update_mask is ignored.", "type": "boolean"}, "latencyTolerance": {"description": "Optional. The latency tolerance for the propagation of this product upsert. Defaults to latency-sensitive.", "enum": ["PRODUCT_UPDATE_LATENCY_TOLERANCE_UNSPECIFIED", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT"], "enumDescriptions": ["Defaults to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE.", "The update will propagate to clients within several minutes on average and up to a few hours in rare cases. Throughput is limited to 7,200 updates per app per hour.", "The update will propagate to clients within 24 hours. Supports high throughput of up to 720,000 updates per app per hour using batch modification methods."], "type": "string"}, "oneTimeProduct": {"$ref": "OneTimeProduct", "description": "Required. The one-time product to upsert."}, "regionsVersion": {"$ref": "RegionsVersion", "description": "Required. The version of the available regions being used for the one-time product."}, "updateMask": {"description": "Required. The list of fields to be updated.", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "UpdatePurchaseOptionStateRequest": {"description": "Request message to update the state of a one-time product purchase option.", "id": "UpdatePurchaseOptionStateRequest", "properties": {"activatePurchaseOptionRequest": {"$ref": "ActivatePurchaseOptionRequest", "description": "Activates a purchase option. Once activated, the purchase option will be available."}, "deactivatePurchaseOptionRequest": {"$ref": "DeactivatePurchaseOptionRequest", "description": "Deactivates a purchase option. Once deactivated, the purchase option will become unavailable."}}, "type": "object"}, "UpdateSubscriptionOfferRequest": {"description": "Request message for UpdateSubscriptionOffer.", "id": "UpdateSubscriptionOfferRequest", "properties": {"allowMissing": {"description": "Optional. If set to true, and the subscription offer with the given package_name, product_id, base_plan_id and offer_id doesn't exist, an offer will be created. If a new offer is created, update_mask is ignored.", "type": "boolean"}, "latencyTolerance": {"description": "Optional. The latency tolerance for the propagation of this product update. Defaults to latency-sensitive.", "enum": ["PRODUCT_UPDATE_LATENCY_TOLERANCE_UNSPECIFIED", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT"], "enumDescriptions": ["Defaults to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE.", "The update will propagate to clients within several minutes on average and up to a few hours in rare cases. Throughput is limited to 7,200 updates per app per hour.", "The update will propagate to clients within 24 hours. Supports high throughput of up to 720,000 updates per app per hour using batch modification methods."], "type": "string"}, "regionsVersion": {"$ref": "RegionsVersion", "description": "Required. The version of the available regions being used for the subscription_offer."}, "subscriptionOffer": {"$ref": "SubscriptionOffer", "description": "Required. The subscription offer to update."}, "updateMask": {"description": "Required. The list of fields to be updated.", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "UpdateSubscriptionOfferStateRequest": {"description": "Request message to update the state of a subscription offer.", "id": "UpdateSubscriptionOfferStateRequest", "properties": {"activateSubscriptionOfferRequest": {"$ref": "ActivateSubscriptionOfferRequest", "description": "Activates an offer. Once activated, the offer will be available to new subscribers."}, "deactivateSubscriptionOfferRequest": {"$ref": "DeactivateSubscriptionOfferRequest", "description": "Deactivates an offer. Once deactivated, the offer will become unavailable to new subscribers, but existing subscribers will maintain their subscription"}}, "type": "object"}, "UpdateSubscriptionRequest": {"description": "Request message for UpdateSubscription.", "id": "UpdateSubscriptionRequest", "properties": {"allowMissing": {"description": "Optional. If set to true, and the subscription with the given package_name and product_id doesn't exist, the subscription will be created. If a new subscription is created, update_mask is ignored.", "type": "boolean"}, "latencyTolerance": {"description": "Optional. The latency tolerance for the propagation of this product update. Defaults to latency-sensitive.", "enum": ["PRODUCT_UPDATE_LATENCY_TOLERANCE_UNSPECIFIED", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE", "PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT"], "enumDescriptions": ["Defaults to PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE.", "The update will propagate to clients within several minutes on average and up to a few hours in rare cases. Throughput is limited to 7,200 updates per app per hour.", "The update will propagate to clients within 24 hours. Supports high throughput of up to 720,000 updates per app per hour using batch modification methods."], "type": "string"}, "regionsVersion": {"$ref": "RegionsVersion", "description": "Required. The version of the available regions being used for the subscription."}, "subscription": {"$ref": "Subscription", "description": "Required. The subscription to update."}, "updateMask": {"description": "Required. The list of fields to be updated.", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "UpgradeTargetingRule": {"description": "Represents a targeting rule of the form: User currently has {scope} [with billing period {billing_period}].", "id": "UpgradeTargetingRule", "properties": {"billingPeriodDuration": {"description": "The specific billing period duration, specified in ISO 8601 format, that a user must be currently subscribed to to be eligible for this rule. If not specified, users subscribed to any billing period are matched.", "type": "string"}, "oncePerUser": {"description": "Limit this offer to only once per user. If set to true, a user can never be eligible for this offer again if they ever subscribed to this offer.", "type": "boolean"}, "scope": {"$ref": "TargetingRuleScope", "description": "Required. The scope of subscriptions this rule considers. Only allows \"this subscription\" and \"specific subscription in app\"."}}, "type": "object"}, "User": {"description": "A user resource.", "id": "User", "properties": {"accessState": {"description": "Output only. The state of the user's access to the Play Console.", "enum": ["ACCESS_STATE_UNSPECIFIED", "INVITED", "INVITATION_EXPIRED", "ACCESS_GRANTED", "ACCESS_EXPIRED"], "enumDescriptions": ["Unknown or unspecified access state.", "User is invited but has not yet accepted the invitation.", "Invitation has expired.", "User has accepted an invitation and has access to the Play Console.", "Account access has expired."], "readOnly": true, "type": "string"}, "developerAccountPermissions": {"description": "Permissions for the user which apply across the developer account.", "items": {"enum": ["DEVELOPER_LEVEL_PERMISSION_UNSPECIFIED", "CAN_SEE_ALL_APPS", "CAN_VIEW_FINANCIAL_DATA_GLOBAL", "CAN_MANAGE_PERMISSIONS_GLOBAL", "CAN_EDIT_GAMES_GLOBAL", "CAN_PUBLISH_GAMES_GLOBAL", "CAN_REPLY_TO_REVIEWS_GLOBAL", "CAN_MANAGE_PUBLIC_APKS_GLOBAL", "CAN_MANAGE_TRACK_APKS_GLOBAL", "CAN_MANAGE_TRACK_USERS_GLOBAL", "CAN_MANAGE_PUBLIC_LISTING_GLOBAL", "CAN_MANAGE_DRAFT_APPS_GLOBAL", "CAN_CREATE_MANAGED_PLAY_APPS_GLOBAL", "CAN_CHANGE_MANAGED_PLAY_SETTING_GLOBAL", "CAN_MANAGE_ORDERS_GLOBAL", "CAN_MANAGE_APP_CONTENT_GLOBAL", "CAN_VIEW_NON_FINANCIAL_DATA_GLOBAL", "CAN_VIEW_APP_QUALITY_GLOBAL", "CAN_MANAGE_DEEPLINKS_GLOBAL"], "enumDeprecated": [false, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "enumDescriptions": ["Unknown or unspecified permission.", "View app information and download bulk reports (read-only). Deprecated: Check CAN_VIEW_NON_FINANCIAL_DATA_GLOBAL.", "View financial data, orders, and cancellation survey responses.", "Admin (all permissions).", "Edit Play Games Services projects.", "Publish Play Games Services projects.", "Reply to reviews.", "Release to production, exclude devices, and use app signing by Google Play.", "Release to testing tracks.", "Manage testing tracks and edit tester lists.", "Manage store presence.", "Create, edit, and delete draft apps.", "Create and publish private apps to your organization.", "Choose whether apps are public, or only available to your organization.", "Manage orders and subscriptions.", "Manage policy related pages on all apps for the developer.", "View app information and download bulk reports (read-only).", "View app quality information for all apps for the developer.", "Manage the deep links setup for all apps for the developer."], "type": "string"}, "type": "array"}, "email": {"description": "Immutable. The user's email address.", "type": "string"}, "expirationTime": {"description": "The time at which the user's access expires, if set. When setting this value, it must always be in the future.", "format": "google-datetime", "type": "string"}, "grants": {"description": "Output only. Per-app permissions for the user.", "items": {"$ref": "<PERSON>"}, "readOnly": true, "type": "array"}, "name": {"description": "Required. Resource name for this user, following the pattern \"developers/{developer}/users/{email}\".", "type": "string"}, "partial": {"description": "Output only. Whether there are more permissions for the user that are not represented here. This can happen if the caller does not have permission to manage all apps in the account. This is also `true` if this user is the account owner. If this field is `true`, it should be taken as a signal that this user cannot be fully managed via the API. That is, the API caller is not be able to manage all of the permissions this user holds, either because it doesn't know about them or because the user is the account owner.", "readOnly": true, "type": "boolean"}}, "type": "object"}, "UserComment": {"description": "User entry from conversation between user and developer.", "id": "UserComment", "properties": {"androidOsVersion": {"description": "Integer Android SDK version of the user's device at the time the review was written, e.g. 23 is Marshmallow. May be absent.", "format": "int32", "type": "integer"}, "appVersionCode": {"description": "Integer version code of the app as installed at the time the review was written. May be absent.", "format": "int32", "type": "integer"}, "appVersionName": {"description": "String version name of the app as installed at the time the review was written. May be absent.", "type": "string"}, "device": {"description": "Codename for the reviewer's device, e.g. klte, flounder. May be absent.", "type": "string"}, "deviceMetadata": {"$ref": "DeviceMetadata", "description": "Information about the characteristics of the user's device."}, "lastModified": {"$ref": "Timestamp", "description": "The last time at which this comment was updated."}, "originalText": {"description": "Untranslated text of the review, where the review was translated. If the review was not translated this is left blank.", "type": "string"}, "reviewerLanguage": {"description": "Language code for the reviewer. This is taken from the device settings so is not guaranteed to match the language the review is written in. May be absent.", "type": "string"}, "starRating": {"description": "The star rating associated with the review, from 1 to 5.", "format": "int32", "type": "integer"}, "text": {"description": "The content of the comment, i.e. review body. In some cases users have been able to write a review with separate title and body; in those cases the title and body are concatenated and separated by a tab character.", "type": "string"}, "thumbsDownCount": {"description": "Number of users who have given this review a thumbs down.", "format": "int32", "type": "integer"}, "thumbsUpCount": {"description": "Number of users who have given this review a thumbs up.", "format": "int32", "type": "integer"}}, "type": "object"}, "UserCountriesTargeting": {"description": "Describes an inclusive/exclusive list of country codes that module targets.", "id": "UserCountriesTargeting", "properties": {"countryCodes": {"description": "List of country codes in the two-letter CLDR territory format.", "items": {"type": "string"}, "type": "array"}, "exclude": {"description": "Indicates if the list above is exclusive.", "type": "boolean"}}, "type": "object"}, "UserCountrySet": {"description": "A set of user countries. A country set determines what variation of app content gets served to a specific location.", "id": "UserCountrySet", "properties": {"countryCodes": {"description": "List of country codes representing countries. A Country code is represented in ISO 3166 alpha-2 format. For Example:- \"IT\" for Italy, \"GE\" for Georgia.", "items": {"type": "string"}, "type": "array"}, "name": {"description": "Country set name.", "type": "string"}}, "type": "object"}, "UserInitiatedCancellation": {"description": "Information specific to cancellations initiated by users.", "id": "UserInitiatedCancellation", "properties": {"cancelSurveyResult": {"$ref": "CancelSurveyResult", "description": "Information provided by the user when they complete the subscription cancellation flow (cancellation reason survey)."}, "cancelTime": {"description": "The time at which the subscription was canceled by the user. The user might still have access to the subscription after this time. Use line_items.expiry_time to determine if a user still has access.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "UsesPermission": {"description": "A permission used by this APK.", "id": "UsesPermission", "properties": {"maxSdkVersion": {"description": "Optionally, the maximum SDK version for which the permission is required.", "format": "int32", "type": "integer"}, "name": {"description": "The name of the permission requested.", "type": "string"}}, "type": "object"}, "VanityCode": {"description": "A multiple use, predefined promotion code.", "id": "VanityCode", "properties": {"promotionCode": {"description": "The promotion code.", "type": "string"}}, "type": "object"}, "Variant": {"description": "APK that is suitable for inclusion in a system image. The resource of SystemApksService.", "id": "<PERSON><PERSON><PERSON>", "properties": {"deviceSpec": {"$ref": "DeviceSpec", "description": "The device spec used to generate the APK."}, "options": {"$ref": "SystemApkOptions", "description": "Optional. Options applied to the generated APK."}, "variantId": {"description": "Output only. The ID of a previously created system APK variant.", "format": "uint32", "readOnly": true, "type": "integer"}}, "type": "object"}, "VariantTargeting": {"description": "Targeting on the level of variants.", "id": "VariantTargeting", "properties": {"abiTargeting": {"$ref": "AbiTargeting", "description": "The abi that the variant targets"}, "multiAbiTargeting": {"$ref": "MultiAbiTargeting", "description": "Multi-api-level targeting"}, "screenDensityTargeting": {"$ref": "ScreenDensityTargeting", "description": "The screen densities that this variant supports"}, "sdkVersionTargeting": {"$ref": "SdkVersionTargeting", "description": "The sdk version that the variant targets"}, "textureCompressionFormatTargeting": {"$ref": "TextureCompressionFormatTargeting", "description": "Texture-compression-format-level targeting"}}, "type": "object"}, "VoidedPurchase": {"description": "A VoidedPurchase resource indicates a purchase that was either canceled/refunded/charged-back.", "id": "VoidedPurchase", "properties": {"kind": {"description": "This kind represents a voided purchase object in the androidpublisher service.", "type": "string"}, "orderId": {"description": "The order id which uniquely identifies a one-time purchase, subscription purchase, or subscription renewal.", "type": "string"}, "purchaseTimeMillis": {"description": "The time at which the purchase was made, in milliseconds since the epoch (Jan 1, 1970).", "format": "int64", "type": "string"}, "purchaseToken": {"description": "The token which uniquely identifies a one-time purchase or subscription. To uniquely identify subscription renewals use order_id (available starting from version 3 of the API).", "type": "string"}, "voidedQuantity": {"description": "The voided quantity as the result of a quantity-based partial refund. Voided purchases of quantity-based partial refunds may only be returned when includeQuantityBasedPartialRefund is set to true.", "format": "int32", "type": "integer"}, "voidedReason": {"description": "The reason why the purchase was voided, possible values are: 0. Other 1. Remorse 2. Not_received 3. Defective 4. Accidental_purchase 5. <PERSON>aud 6. Friendly_fraud 7. Chargeback 8. Unacknowledged_purchase", "format": "int32", "type": "integer"}, "voidedSource": {"description": "The initiator of voided purchase, possible values are: 0. User 1. Developer 2. Google", "format": "int32", "type": "integer"}, "voidedTimeMillis": {"description": "The time at which the purchase was canceled/refunded/charged-back, in milliseconds since the epoch (Jan 1, 1970).", "format": "int64", "type": "string"}}, "type": "object"}, "VoidedPurchasesListResponse": {"description": "Response for the voidedpurchases.list API.", "id": "VoidedPurchasesListResponse", "properties": {"pageInfo": {"$ref": "PageInfo", "description": "General pagination information."}, "tokenPagination": {"$ref": "TokenPagination", "description": "Pagination information for token pagination."}, "voidedPurchases": {"items": {"$ref": "VoidedPurchase"}, "type": "array"}}, "type": "object"}}, "servicePath": "", "title": "Google Play Android Developer API", "version": "v3"}