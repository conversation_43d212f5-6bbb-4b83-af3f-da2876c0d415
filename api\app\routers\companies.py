"""
Companies router for the QueTeToca API.
"""
import logging
from typing import Annotated, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from ..database import get_db
from ..schemas.company import (
    CompanyCreate,
    CompanyUpdate,
    CompanyResponse,
    CompanyListResponse,
    CompanyDetailResponse,
    CompanyAdminRequest,
    CompanyAdminResponse,
    CompanySettingsUpdate,
    CompanyStatsResponse
)
from ..schemas.base import PaginationParams
from ..services.company import CompanyService
from ..services.dependencies import (
    get_current_active_user,
    get_current_admin_user,
    get_current_super_admin
)
from ..models import User

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/companies", tags=["Companies"])


@router.get("", response_model=CompanyListResponse)
async def list_companies(
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_admin_user)],
    pagination: Annotated[PaginationParams, Depends()],
    is_active: Optional[bool] = Query(None, description="Filter by active status")
):
    """
    List companies.
    
    Only admins can list companies.
    Company admins can only see their own company.
    
    Args:
        db: Database session
        current_user: Current authenticated admin user
        pagination: Pagination parameters
        is_active: Optional active status filter
        
    Returns:
        List of companies
    """
    try:
        company_service = CompanyService(db)
        
        # Super admins can see all companies
        if current_user.role.value == "SUPER_ADMIN":
            companies = company_service.get_companies(
                skip=pagination.get_offset(),
                limit=pagination.size,
                is_active=is_active
            )
        else:
            # Company admins can only see their own company
            if not current_user.company_id:
                companies = []
            else:
                company = company_service.get_company_by_id(current_user.company_id)
                companies = [company] if company else []
        
        company_responses = [CompanyResponse.model_validate(company) for company in companies]
        
        return CompanyListResponse(
            success=True,
            message=f"Retrieved {len(company_responses)} companies",
            companies=company_responses
        )
    
    except Exception as e:
        logger.error(f"Error listing companies: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving companies"
        )


@router.post("", response_model=CompanyDetailResponse)
async def create_company(
    company_data: CompanyCreate,
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_super_admin)]
):
    """
    Create a new company.
    
    Only super admins can create companies.
    
    Args:
        company_data: Company creation data
        db: Database session
        current_user: Current authenticated super admin user
        
    Returns:
        Created company information
    """
    try:
        company_service = CompanyService(db)
        company = company_service.create_company(company_data)
        
        company_response = CompanyResponse.model_validate(company)
        
        return CompanyDetailResponse(
            success=True,
            message="Company created successfully",
            company=company_response
        )
    
    except Exception as e:
        logger.error(f"Error creating company: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error creating company"
        )


@router.get("/{company_id}", response_model=CompanyDetailResponse)
async def get_company(
    company_id: str,
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_admin_user)]
):
    """
    Get a specific company by ID.
    
    Args:
        company_id: Company ID
        db: Database session
        current_user: Current authenticated admin user
        
    Returns:
        Company information
    """
    try:
        # Check access permissions
        if (current_user.role.value == "COMPANY_ADMIN" and 
            current_user.company_id != company_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access to this company is not allowed"
            )
        
        company_service = CompanyService(db)
        company = company_service.get_company_by_id(company_id)
        
        if not company:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Company not found"
            )
        
        company_response = CompanyResponse.model_validate(company)
        
        return CompanyDetailResponse(
            success=True,
            message="Company retrieved successfully",
            company=company_response
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting company {company_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving company"
        )


@router.put("/{company_id}", response_model=CompanyDetailResponse)
async def update_company(
    company_id: str,
    company_data: CompanyUpdate,
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_admin_user)]
):
    """
    Update a company.
    
    Args:
        company_id: Company ID
        company_data: Company update data
        db: Database session
        current_user: Current authenticated admin user
        
    Returns:
        Updated company information
    """
    try:
        # Check access permissions
        if (current_user.role.value == "COMPANY_ADMIN" and 
            current_user.company_id != company_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access to this company is not allowed"
            )
        
        company_service = CompanyService(db)
        updated_company = company_service.update_company(company_id, company_data)
        
        if not updated_company:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Company not found"
            )
        
        company_response = CompanyResponse.model_validate(updated_company)
        
        return CompanyDetailResponse(
            success=True,
            message="Company updated successfully",
            company=company_response
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating company {company_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error updating company"
        )


@router.delete("/{company_id}")
async def delete_company(
    company_id: str,
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_super_admin)]
):
    """
    Delete a company.

    Only super admins can delete companies.

    Args:
        company_id: Company ID
        db: Database session
        current_user: Current authenticated super admin user

    Returns:
        Deletion confirmation
    """
    try:
        company_service = CompanyService(db)
        success = company_service.delete_company(company_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Company not found"
            )

        return {
            "success": True,
            "message": "Company deleted successfully"
        }

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting company {company_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error deleting company"
        )


@router.post("/{company_id}/admins", response_model=CompanyAdminResponse)
async def add_company_admin(
    company_id: str,
    admin_request: CompanyAdminRequest,
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_admin_user)]
):
    """
    Add an admin to a company.

    Args:
        company_id: Company ID
        admin_request: Admin user UID
        db: Database session
        current_user: Current authenticated admin user

    Returns:
        Admin addition confirmation
    """
    try:
        # Check access permissions
        if (current_user.role.value == "COMPANY_ADMIN" and
            current_user.company_id != company_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access to this company is not allowed"
            )

        company_service = CompanyService(db)
        company = company_service.add_admin(company_id, admin_request.user_uid)

        if not company:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Company not found"
            )

        return CompanyAdminResponse(
            success=True,
            message="Admin added successfully",
            company_id=company_id,
            user_uid=admin_request.user_uid,
            action="added"
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding admin to company {company_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error adding admin"
        )


@router.delete("/{company_id}/admins/{user_uid}", response_model=CompanyAdminResponse)
async def remove_company_admin(
    company_id: str,
    user_uid: str,
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_admin_user)]
):
    """
    Remove an admin from a company.

    Args:
        company_id: Company ID
        user_uid: User UID to remove
        db: Database session
        current_user: Current authenticated admin user

    Returns:
        Admin removal confirmation
    """
    try:
        # Check access permissions
        if (current_user.role.value == "COMPANY_ADMIN" and
            current_user.company_id != company_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access to this company is not allowed"
            )

        # Prevent self-removal
        if current_user.uid == user_uid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot remove yourself as admin"
            )

        company_service = CompanyService(db)
        company = company_service.remove_admin(company_id, user_uid)

        if not company:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Company not found"
            )

        return CompanyAdminResponse(
            success=True,
            message="Admin removed successfully",
            company_id=company_id,
            user_uid=user_uid,
            action="removed"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error removing admin from company {company_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error removing admin"
        )


@router.get("/{company_id}/stats", response_model=CompanyStatsResponse)
async def get_company_stats(
    company_id: str,
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_admin_user)],
    start_date: Optional[str] = Query(None, description="Start date in YYYY-MM-DD format"),
    end_date: Optional[str] = Query(None, description="End date in YYYY-MM-DD format")
):
    """
    Get statistics for a company.

    Args:
        company_id: Company ID
        db: Database session
        current_user: Current authenticated admin user
        start_date: Optional start date filter
        end_date: Optional end date filter

    Returns:
        Company statistics
    """
    try:
        # Check access permissions
        if (current_user.role.value == "COMPANY_ADMIN" and
            current_user.company_id != company_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access to this company is not allowed"
            )

        company_service = CompanyService(db)

        # Parse dates if provided
        parsed_start_date = None
        parsed_end_date = None

        if start_date:
            from datetime import datetime
            try:
                parsed_start_date = datetime.strptime(start_date, "%Y-%m-%d").date()
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid start_date format. Use YYYY-MM-DD"
                )

        if end_date:
            from datetime import datetime
            try:
                parsed_end_date = datetime.strptime(end_date, "%Y-%m-%d").date()
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid end_date format. Use YYYY-MM-DD"
                )

        stats = company_service.get_company_stats(
            company_id,
            parsed_start_date,
            parsed_end_date
        )

        if not stats:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Company not found"
            )

        return CompanyStatsResponse(
            success=True,
            message="Company statistics retrieved successfully",
            **stats
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting stats for company {company_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving company statistics"
        )


@router.put("/{company_id}/settings")
async def update_company_settings(
    company_id: str,
    settings_data: CompanySettingsUpdate,
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_admin_user)]
):
    """
    Update company settings.

    Args:
        company_id: Company ID
        settings_data: Settings update data
        db: Database session
        current_user: Current authenticated admin user

    Returns:
        Settings update confirmation
    """
    try:
        # Check access permissions
        if (current_user.role.value == "COMPANY_ADMIN" and
            current_user.company_id != company_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access to this company is not allowed"
            )

        company_service = CompanyService(db)
        company = company_service.update_company_settings(company_id, settings_data.settings)

        if not company:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Company not found"
            )

        return {
            "success": True,
            "message": "Company settings updated successfully",
            "settings": company.settings
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating settings for company {company_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error updating company settings"
        )
