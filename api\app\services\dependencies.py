"""
FastAPI dependencies for authentication and authorization.
"""
import logging
from typing import Optional, Annotated
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from ..database import get_db
from ..models import User, Company, Bar
from ..models.enums import UserRole
from .auth import get_current_user

logger = logging.getLogger(__name__)

# Security scheme
security = HTTPBearer()


async def get_current_active_user(
    credentials: Annotated[HTTPAuthorizationCredentials, Depends(security)],
    db: Annotated[Session, Depends(get_db)]
) -> User:
    """
    Get current authenticated user.
    
    Args:
        credentials: HTTP Bearer token
        db: Database session
        
    Returns:
        Current user
        
    Raises:
        HTTPException: If authentication fails
    """
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user = get_current_user(db, credentials.credentials)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user


async def get_current_admin_user(
    current_user: Annotated[User, Depends(get_current_active_user)]
) -> User:
    """
    Get current user if they are an admin.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Current user if admin
        
    Raises:
        HTTPException: If user is not an admin
    """
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin privileges required"
        )
    
    return current_user


async def get_current_company_admin(
    current_user: Annotated[User, Depends(get_current_active_user)]
) -> User:
    """
    Get current user if they are a company admin.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Current user if company admin
        
    Raises:
        HTTPException: If user is not a company admin
    """
    if not current_user.is_company_admin and current_user.role != UserRole.SUPER_ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Company admin privileges required"
        )
    
    return current_user


async def get_current_super_admin(
    current_user: Annotated[User, Depends(get_current_active_user)]
) -> User:
    """
    Get current user if they are a super admin.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Current user if super admin
        
    Raises:
        HTTPException: If user is not a super admin
    """
    if current_user.role != UserRole.SUPER_ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Super admin privileges required"
        )
    
    return current_user


def require_company_access(company_id: str):
    """
    Dependency factory to require access to a specific company.
    
    Args:
        company_id: Company ID to check access for
        
    Returns:
        Dependency function
    """
    async def check_company_access(
        current_user: Annotated[User, Depends(get_current_active_user)],
        db: Annotated[Session, Depends(get_db)]
    ) -> User:
        # Super admin can access any company
        if current_user.role == UserRole.SUPER_ADMIN:
            return current_user
        
        # Check if user belongs to the company
        if current_user.company_id != company_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access to this company is not allowed"
            )
        
        # Verify company exists
        company = db.query(Company).filter(Company.id == company_id).first()
        if not company:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Company not found"
            )
        
        return current_user
    
    return check_company_access


def require_bar_access(bar_id: str):
    """
    Dependency factory to require access to a specific bar.
    
    Args:
        bar_id: Bar ID to check access for
        
    Returns:
        Dependency function
    """
    async def check_bar_access(
        current_user: Annotated[User, Depends(get_current_active_user)],
        db: Annotated[Session, Depends(get_db)]
    ) -> tuple[User, Bar]:
        # Get the bar
        bar = db.query(Bar).filter(Bar.id == bar_id).first()
        if not bar:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Bar not found"
            )
        
        # Super admin can access any bar
        if current_user.role == UserRole.SUPER_ADMIN:
            return current_user, bar
        
        # Check if user belongs to the bar's company
        if current_user.company_id != bar.company_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access to this bar is not allowed"
            )
        
        return current_user, bar
    
    return check_bar_access


async def get_optional_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db)
) -> Optional[User]:
    """
    Get current user if authenticated, otherwise return None.
    
    Args:
        credentials: Optional HTTP Bearer token
        db: Database session
        
    Returns:
        Current user if authenticated, None otherwise
    """
    if not credentials:
        return None
    
    try:
        user = get_current_user(db, credentials.credentials)
        return user
    except Exception:
        return None


class RoleChecker:
    """Role-based access control checker."""
    
    def __init__(self, allowed_roles: list[UserRole]):
        self.allowed_roles = allowed_roles
    
    def __call__(self, current_user: User = Depends(get_current_active_user)) -> User:
        if current_user.role not in self.allowed_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied. Required roles: {[role.value for role in self.allowed_roles]}"
            )
        return current_user


# Common role checkers
require_admin = RoleChecker([UserRole.COMPANY_ADMIN, UserRole.SUPER_ADMIN])
require_manager = RoleChecker([UserRole.BAR_MANAGER, UserRole.COMPANY_ADMIN, UserRole.SUPER_ADMIN])
require_super_admin = RoleChecker([UserRole.SUPER_ADMIN])
