"""
Tests for authentication endpoints.
"""
import pytest
from fastapi.testclient import TestClient


class TestAuthEndpoints:
    """Test authentication endpoints."""
    
    def test_verify_token_success(self, client: TestClient, test_user):
        """Test successful token verification."""
        response = client.post(
            "/auth/verify-token",
            json={"token": "test-token"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["user"]["email"] == "<EMAIL>"
        assert "access_token" in data
        assert data["token_type"] == "bearer"
    
    def test_verify_token_invalid(self, client: TestClient):
        """Test token verification with invalid token."""
        response = client.post(
            "/auth/verify-token",
            json={"token": "invalid-token"}
        )
        
        assert response.status_code == 401
        data = response.json()
        assert data["success"] is False
        assert "Invalid or expired Firebase token" in data["message"]
    
    def test_verify_token_missing(self, client: TestClient):
        """Test token verification with missing token."""
        response = client.post(
            "/auth/verify-token",
            json={}
        )
        
        assert response.status_code == 422  # Validation error
    
    def test_get_current_user_success(self, client: TestClient, test_user, auth_headers):
        """Test getting current user information."""
        response = client.get(
            "/auth/me",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["user"]["email"] == "<EMAIL>"
        assert data["user"]["uid"] == "test-user-uid"
    
    def test_get_current_user_unauthorized(self, client: TestClient):
        """Test getting current user without authentication."""
        response = client.get("/auth/me")
        
        assert response.status_code == 401
    
    def test_get_current_user_invalid_token(self, client: TestClient):
        """Test getting current user with invalid token."""
        response = client.get(
            "/auth/me",
            headers={"Authorization": "Bearer invalid-token"}
        )
        
        assert response.status_code == 401
    
    def test_refresh_token_success(self, client: TestClient, test_user, auth_headers):
        """Test token refresh."""
        response = client.post(
            "/auth/refresh",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "access_token" in data
        assert data["token_type"] == "bearer"
    
    def test_refresh_token_unauthorized(self, client: TestClient):
        """Test token refresh without authentication."""
        response = client.post("/auth/refresh")
        
        assert response.status_code == 401
    
    def test_logout_success(self, client: TestClient, test_user, auth_headers):
        """Test user logout."""
        response = client.post(
            "/auth/logout",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "Logged out successfully" in data["message"]
    
    def test_logout_unauthorized(self, client: TestClient):
        """Test logout without authentication."""
        response = client.post("/auth/logout")
        
        assert response.status_code == 401


class TestAuthHelpers:
    """Test authentication helper functions."""
    
    def test_user_roles(self, test_user, test_admin_user, test_company_admin):
        """Test user role properties."""
        # Customer user
        assert test_user.is_customer is True
        assert test_user.is_admin is False
        assert test_user.is_company_admin is False
        assert test_user.is_bar_manager is False
        
        # Admin user
        assert test_admin_user.is_customer is False
        assert test_admin_user.is_admin is True
        assert test_admin_user.is_company_admin is False
        assert test_admin_user.is_bar_manager is False
        
        # Company admin user
        assert test_company_admin.is_customer is False
        assert test_company_admin.is_admin is True
        assert test_company_admin.is_company_admin is True
        assert test_company_admin.is_bar_manager is False
    
    def test_user_permissions(self, test_company_admin, test_company, test_bar):
        """Test user permission checks."""
        # Company admin can manage their company
        assert test_company_admin.can_manage_company(test_company.id) is True
        assert test_company_admin.can_manage_company("other-company-id") is False
        
        # Company admin can manage bars in their company
        assert test_company_admin.can_manage_bar(test_bar) is True
