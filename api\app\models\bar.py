"""
Bar model for the QueTeToca API.
"""
from datetime import datetime
from typing import List, Optional
from sqlalchemy import Column, String, DateTime, Text, ForeignKey, JSON, Integer, Float
from sqlalchemy.orm import relationship
import uuid

from ..database import Base


class Bar(Base):
    """Bar model."""
    
    __tablename__ = "bars"
    
    # Primary key
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), index=True)
    
    # Basic bar information
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    
    # Location information
    address = Column(Text, nullable=False)
    city = Column(String(100), nullable=True)
    state = Column(String(100), nullable=True)
    country = Column(String(100), nullable=True)
    postal_code = Column(String(20), nullable=True)
    latitude = Column(Float, nullable=True)
    longitude = Column(Float, nullable=True)
    
    # Contact information
    phone = Column(String(50), nullable=True)
    email = Column(String(255), nullable=True)
    website = Column(String(255), nullable=True)
    
    # Company association
    company_id = Column(
        String(36), 
        ForeignKey("companies.id", ondelete="CASCADE"), 
        nullable=False,
        index=True
    )
    
    # QR Code information
    qr_code = Column(Text, nullable=True)  # Base64 encoded QR code image
    qr_code_url = Column(String(500), nullable=True)  # URL for the QR code
    
    # Bar settings
    max_queue_size = Column(Integer, default=100, nullable=False)
    estimated_wait_time = Column(Integer, default=15, nullable=False)  # minutes
    settings = Column(JSON, default=dict, nullable=False)
    
    # Status
    is_active = Column(String(10), default="true", nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    company = relationship("Company", back_populates="bars")
    queue_entries = relationship("QueueEntry", back_populates="bar", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Bar(id='{self.id}', name='{self.name}', company_id='{self.company_id}')>"
    
    @property
    def active(self) -> bool:
        """Check if bar is active."""
        return self.is_active == "true"
    
    @property
    def current_queue_size(self) -> int:
        """Get current queue size."""
        return len([entry for entry in self.queue_entries if entry.status == "WAITING"])
    
    @property
    def is_queue_full(self) -> bool:
        """Check if queue is full."""
        return self.current_queue_size >= self.max_queue_size
    
    @property
    def next_queue_number(self) -> int:
        """Get the next queue number."""
        if not self.queue_entries:
            return 1
        
        # Get the highest queue number for today
        today = datetime.utcnow().date()
        today_entries = [
            entry for entry in self.queue_entries 
            if entry.created_at.date() == today
        ]
        
        if not today_entries:
            return 1
        
        return max(entry.queue_number for entry in today_entries) + 1
    
    def get_setting(self, key: str, default=None):
        """Get a bar setting."""
        return self.settings.get(key, default)
    
    def set_setting(self, key: str, value) -> None:
        """Set a bar setting."""
        settings_dict = dict(self.settings) if self.settings else {}
        settings_dict[key] = value
        self.settings = settings_dict
    
    def activate(self) -> None:
        """Activate the bar."""
        self.is_active = "true"
    
    def deactivate(self) -> None:
        """Deactivate the bar."""
        self.is_active = "false"
    
    def update_qr_code(self, qr_code_data: str, qr_url: str) -> None:
        """Update QR code information."""
        self.qr_code = qr_code_data
        self.qr_code_url = qr_url
    
    def get_full_address(self) -> str:
        """Get formatted full address."""
        parts = [self.address]
        if self.city:
            parts.append(self.city)
        if self.state:
            parts.append(self.state)
        if self.postal_code:
            parts.append(self.postal_code)
        if self.country:
            parts.append(self.country)
        
        return ", ".join(parts)
