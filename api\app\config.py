"""
Configuration settings for the QueTeToca API.
"""
import os
from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Application settings."""
    
    # Database Configuration
    database_url: str = Field(
        default="sqlite:///./quetetoca.db",
        description="Database connection URL"
    )
    
    # Firebase Configuration
    firebase_credentials_path: Optional[str] = Field(
        default=None,
        description="Path to Firebase credentials JSON file"
    )
    firebase_project_id: Optional[str] = Field(
        default=None,
        description="Firebase project ID"
    )
    
    # JWT Configuration
    secret_key: str = Field(
        default="your-super-secret-key-change-this-in-production",
        description="Secret key for JWT tokens"
    )
    algorithm: str = Field(
        default="HS256",
        description="Algorithm for JWT tokens"
    )
    access_token_expire_minutes: int = Field(
        default=30,
        description="Access token expiration time in minutes"
    )
    
    # CORS Configuration
    allowed_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8080"],
        description="Allowed CORS origins"
    )
    allowed_methods: List[str] = Field(
        default=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        description="Allowed CORS methods"
    )
    allowed_headers: List[str] = Field(
        default=["*"],
        description="Allowed CORS headers"
    )
    
    # Rate Limiting
    rate_limit_requests: int = Field(
        default=100,
        description="Number of requests allowed per window"
    )
    rate_limit_window: int = Field(
        default=60,
        description="Rate limit window in seconds"
    )
    
    # Application Configuration
    environment: str = Field(
        default="development",
        description="Application environment"
    )
    debug: bool = Field(
        default=True,
        description="Debug mode"
    )
    
    # Logging Configuration
    log_level: str = Field(
        default="INFO",
        description="Logging level"
    )
    log_format: str = Field(
        default="json",
        description="Logging format (json or text)"
    )
    
    # QR Code Configuration
    qr_code_base_url: str = Field(
        default="https://yourdomain.com/queue",
        description="Base URL for QR codes"
    )
    
    # WebSocket Configuration
    ws_heartbeat_interval: int = Field(
        default=30,
        description="WebSocket heartbeat interval in seconds"
    )
    
    # Redis Configuration (optional)
    redis_url: Optional[str] = Field(
        default=None,
        description="Redis connection URL for caching and rate limiting"
    )
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
        @classmethod
        def parse_env_var(cls, field_name: str, raw_val: str) -> any:
            """Parse environment variables."""
            if field_name in ['allowed_origins', 'allowed_methods', 'allowed_headers']:
                return [x.strip() for x in raw_val.split(',')]
            return cls.json_loads(raw_val)


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings."""
    return settings
