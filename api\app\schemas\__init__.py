"""
Schemas package for the QueTeToca API.
"""
from .base import (
    BaseSchema,
    TimestampMixin,
    ResponseBase,
    ErrorResponse,
    PaginationParams,
    PaginatedResponse
)
from .user import (
    UserBase,
    UserCreate,
    UserUpdate,
    UserInDB,
    UserResponse,
    UserListResponse,
    UserDetailResponse,
    AuthTokenRequest,
    AuthTokenResponse,
    CurrentUserResponse
)
from .company import (
    CompanyBase,
    CompanyCreate,
    CompanyUpdate,
    CompanyInDB,
    CompanyResponse,
    CompanyListResponse,
    CompanyDetailResponse,
    CompanyAdminRequest,
    CompanyAdminResponse,
    CompanySettingsUpdate,
    CompanyStatsResponse
)
from .bar import (
    BarBase,
    BarCreate,
    BarUpdate,
    BarInDB,
    BarResponse,
    BarListResponse,
    BarDetailResponse,
    QRCodeResponse,
    BarStatsResponse,
    BarSettingsUpdate
)
from .queue import (
    QueueEntryBase,
    QueueEntryCreate,
    QueueEntryUpdate,
    QueueEntryInDB,
    QueueEntryResponse,
    QueueListResponse,
    QueueEntryDetailResponse,
    QueueJoinRequest,
    QueueJoinResponse,
    QueueCallRequest,
    QueueCallResponse,
    QueueCompleteRequest,
    QueueCompleteResponse,
    QueueCancelRequest,
    QueueCancelResponse,
    QueueStatusUpdate,
    QueueStatsResponse
)

__all__ = [
    # Base schemas
    "BaseSchema",
    "TimestampMixin",
    "ResponseBase",
    "ErrorResponse",
    "PaginationParams",
    "PaginatedResponse",

    # User schemas
    "UserBase",
    "UserCreate",
    "UserUpdate",
    "UserInDB",
    "UserResponse",
    "UserListResponse",
    "UserDetailResponse",
    "AuthTokenRequest",
    "AuthTokenResponse",
    "CurrentUserResponse",

    # Company schemas
    "CompanyBase",
    "CompanyCreate",
    "CompanyUpdate",
    "CompanyInDB",
    "CompanyResponse",
    "CompanyListResponse",
    "CompanyDetailResponse",
    "CompanyAdminRequest",
    "CompanyAdminResponse",
    "CompanySettingsUpdate",
    "CompanyStatsResponse",

    # Bar schemas
    "BarBase",
    "BarCreate",
    "BarUpdate",
    "BarInDB",
    "BarResponse",
    "BarListResponse",
    "BarDetailResponse",
    "QRCodeResponse",
    "BarStatsResponse",
    "BarSettingsUpdate",

    # Queue schemas
    "QueueEntryBase",
    "QueueEntryCreate",
    "QueueEntryUpdate",
    "QueueEntryInDB",
    "QueueEntryResponse",
    "QueueListResponse",
    "QueueEntryDetailResponse",
    "QueueJoinRequest",
    "QueueJoinResponse",
    "QueueCallRequest",
    "QueueCallResponse",
    "QueueCompleteRequest",
    "QueueCompleteResponse",
    "QueueCancelRequest",
    "QueueCancelResponse",
    "QueueStatusUpdate",
    "QueueStatsResponse"
]