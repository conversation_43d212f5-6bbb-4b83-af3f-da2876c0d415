"""
User schemas for the QueTeToca API.
"""
from datetime import datetime
from typing import Optional
from pydantic import EmailStr, Field

from .base import BaseSchema, TimestampMixin, ResponseBase
from ..models.enums import UserRole


class UserBase(BaseSchema):
    """Base user schema."""
    email: EmailStr
    display_name: Optional[str] = None
    photo_url: Optional[str] = None


class UserCreate(UserBase):
    """Schema for creating a user."""
    uid: str = Field(..., min_length=1, max_length=128)
    role: UserRole = UserRole.CUSTOMER
    company_id: Optional[str] = None


class UserUpdate(BaseSchema):
    """Schema for updating a user."""
    display_name: Optional[str] = None
    photo_url: Optional[str] = None
    role: Optional[UserRole] = None
    company_id: Optional[str] = None


class UserInDB(UserBase, TimestampMixin):
    """User schema as stored in database."""
    uid: str
    role: UserRole
    company_id: Optional[str] = None
    last_login: Optional[datetime] = None


class UserResponse(UserInDB):
    """User response schema."""
    is_admin: bool
    is_company_admin: bool
    is_bar_manager: bool
    is_customer: bool


class UserListResponse(ResponseBase):
    """User list response schema."""
    users: list[UserResponse]


class UserDetailResponse(ResponseBase):
    """User detail response schema."""
    user: UserResponse


class AuthTokenRequest(BaseSchema):
    """Authentication token request schema."""
    token: str = Field(..., min_length=1)


class AuthTokenResponse(ResponseBase):
    """Authentication token response schema."""
    user: UserResponse
    access_token: str
    token_type: str = "bearer"
    expires_in: int


class CurrentUserResponse(ResponseBase):
    """Current user response schema."""
    user: UserResponse
