"""
Rate limiting middleware for the QueTeToca API.
"""
import time
from typing import Dict, Tuple
from fastapi import Request, HTTPException, status
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

from ..config import get_settings

settings = get_settings()

# Create limiter instance
limiter = Limiter(key_func=get_remote_address)


def add_rate_limiting_middleware(app):
    """Add rate limiting middleware to the FastAPI app."""
    app.state.limiter = limiter
    app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)


# Rate limiting decorators for different endpoints
def rate_limit_auth():
    """Rate limit for authentication endpoints."""
    return limiter.limit("10/minute")


def rate_limit_queue_join():
    """Rate limit for joining queues."""
    return limiter.limit("5/minute")


def rate_limit_general():
    """General rate limit for API endpoints."""
    return limiter.limit(f"{settings.rate_limit_requests}/{settings.rate_limit_window}seconds")


def rate_limit_websocket():
    """Rate limit for WebSocket connections."""
    return limiter.limit("20/minute")


class InMemoryRateLimiter:
    """Simple in-memory rate limiter for custom use cases."""
    
    def __init__(self):
        self.requests: Dict[str, list] = {}
    
    def is_allowed(self, key: str, limit: int, window: int) -> Tuple[bool, int]:
        """
        Check if request is allowed based on rate limit.
        
        Args:
            key: Unique identifier for the client
            limit: Maximum number of requests allowed
            window: Time window in seconds
            
        Returns:
            Tuple of (is_allowed, retry_after_seconds)
        """
        now = time.time()
        
        # Clean old requests
        if key in self.requests:
            self.requests[key] = [
                req_time for req_time in self.requests[key]
                if now - req_time < window
            ]
        else:
            self.requests[key] = []
        
        # Check if limit exceeded
        if len(self.requests[key]) >= limit:
            oldest_request = min(self.requests[key])
            retry_after = int(window - (now - oldest_request)) + 1
            return False, retry_after
        
        # Add current request
        self.requests[key].append(now)
        return True, 0
    
    def reset(self, key: str):
        """Reset rate limit for a specific key."""
        if key in self.requests:
            del self.requests[key]


# Global rate limiter instance
memory_limiter = InMemoryRateLimiter()


async def check_rate_limit(request: Request, limit: int = 100, window: int = 60):
    """
    Check rate limit for a request.
    
    Args:
        request: FastAPI request object
        limit: Maximum requests allowed
        window: Time window in seconds
        
    Raises:
        HTTPException: If rate limit exceeded
    """
    client_ip = get_remote_address(request)
    
    is_allowed, retry_after = memory_limiter.is_allowed(client_ip, limit, window)
    
    if not is_allowed:
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Rate limit exceeded",
            headers={"Retry-After": str(retry_after)}
        )
