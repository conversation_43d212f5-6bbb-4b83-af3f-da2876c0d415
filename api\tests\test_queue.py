"""
Tests for queue endpoints.
"""
import pytest
from fastapi.testclient import TestClient


class TestQueueEndpoints:
    """Test queue endpoints."""
    
    def test_get_bar_queue(self, client: TestClient, test_bar, test_queue_entry):
        """Test getting bar queue (public endpoint)."""
        response = client.get(f"/bars/{test_bar.id}/queue")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["bar_id"] == test_bar.id
        assert len(data["entries"]) >= 1
        assert data["total_waiting"] >= 0
    
    def test_get_bar_queue_with_auth(self, client: TestClient, test_bar, test_queue_entry, auth_headers):
        """Test getting bar queue with authentication."""
        response = client.get(
            f"/bars/{test_bar.id}/queue",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert len(data["entries"]) >= 1
    
    def test_join_queue_success(self, client: TestClient, test_bar, test_data_factory):
        """Test joining a queue successfully."""
        queue_data = test_data_factory.create_queue_entry_data()
        
        response = client.post(
            f"/bars/{test_bar.id}/queue",
            json=queue_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["entry"]["customer_name"] == queue_data["customer_name"]
        assert data["entry"]["party_size"] == queue_data["party_size"]
        assert "position_in_queue" in data
        assert "estimated_wait_time" in data
    
    def test_join_queue_with_auth(self, client: TestClient, test_bar, auth_headers, test_data_factory):
        """Test joining a queue with authentication."""
        queue_data = test_data_factory.create_queue_entry_data()
        
        response = client.post(
            f"/bars/{test_bar.id}/queue",
            json=queue_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["entry"]["customer_uid"] == "test-user-uid"
    
    def test_join_queue_invalid_data(self, client: TestClient, test_bar):
        """Test joining a queue with invalid data."""
        invalid_data = {
            "customer_name": "",  # Empty name
            "party_size": 0  # Invalid party size
        }
        
        response = client.post(
            f"/bars/{test_bar.id}/queue",
            json=invalid_data
        )
        
        assert response.status_code == 422  # Validation error
    
    def test_call_queue_entry_success(self, client: TestClient, test_queue_entry, company_admin_auth_headers):
        """Test calling a queue entry."""
        response = client.put(
            f"/queue/{test_queue_entry.id}/call",
            headers=company_admin_auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["entry"]["status"] == "CALLED"
        assert "called_at" in data
    
    def test_call_queue_entry_unauthorized(self, client: TestClient, test_queue_entry, auth_headers):
        """Test calling a queue entry without proper permissions."""
        response = client.put(
            f"/queue/{test_queue_entry.id}/call",
            headers=auth_headers
        )
        
        assert response.status_code == 403
    
    def test_call_queue_entry_not_found(self, client: TestClient, company_admin_auth_headers):
        """Test calling a non-existent queue entry."""
        response = client.put(
            "/queue/non-existent-id/call",
            headers=company_admin_auth_headers
        )
        
        assert response.status_code == 404
    
    def test_complete_queue_entry_success(self, client: TestClient, test_queue_entry, company_admin_auth_headers):
        """Test completing a queue entry."""
        # First call the entry
        client.put(
            f"/queue/{test_queue_entry.id}/call",
            headers=company_admin_auth_headers
        )
        
        # Then complete it
        response = client.put(
            f"/queue/{test_queue_entry.id}/complete",
            headers=company_admin_auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["entry"]["status"] == "COMPLETED"
        assert "completed_at" in data
    
    def test_complete_queue_entry_unauthorized(self, client: TestClient, test_queue_entry, auth_headers):
        """Test completing a queue entry without proper permissions."""
        response = client.put(
            f"/queue/{test_queue_entry.id}/complete",
            headers=auth_headers
        )
        
        assert response.status_code == 403
    
    def test_cancel_queue_entry_by_customer(self, client: TestClient, test_queue_entry, auth_headers):
        """Test customer cancelling their own queue entry."""
        response = client.delete(
            f"/queue/{test_queue_entry.id}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["entry"]["status"] == "CANCELLED"
    
    def test_cancel_queue_entry_by_staff(self, client: TestClient, test_queue_entry, company_admin_auth_headers):
        """Test staff cancelling a queue entry."""
        response = client.delete(
            f"/queue/{test_queue_entry.id}?reason=No show",
            headers=company_admin_auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["entry"]["status"] == "CANCELLED"
        assert data["reason"] == "No show"
    
    def test_cancel_queue_entry_wrong_customer(self, client: TestClient, test_queue_entry, db_session):
        """Test customer trying to cancel someone else's entry."""
        # Create another user
        from api.app.models import User
        from api.app.models.enums import UserRole
        
        other_user = User(
            uid="other-user-uid",
            email="<EMAIL>",
            display_name="Other User",
            role=UserRole.CUSTOMER
        )
        db_session.add(other_user)
        db_session.commit()
        
        # Try to cancel with different user's token
        response = client.delete(
            f"/queue/{test_queue_entry.id}",
            headers={"Authorization": "Bearer other-user-token"}
        )
        
        assert response.status_code == 403
    
    def test_get_queue_entry_success(self, client: TestClient, test_queue_entry, auth_headers):
        """Test getting a specific queue entry."""
        response = client.get(
            f"/queue/{test_queue_entry.id}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["entry"]["id"] == test_queue_entry.id
    
    def test_get_queue_entry_unauthorized(self, client: TestClient, test_queue_entry):
        """Test getting a queue entry without authentication."""
        response = client.get(f"/queue/{test_queue_entry.id}")
        
        # Should still work but with limited information
        assert response.status_code == 200
        data = response.json()
        assert data["entry"]["customer_name"] == f"Customer #{test_queue_entry.queue_number}"
    
    def test_get_queue_stats(self, client: TestClient, test_bar, company_admin_auth_headers):
        """Test getting queue statistics."""
        response = client.get(
            f"/bars/{test_bar.id}/queue/stats",
            headers=company_admin_auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "bar_id" in data
        assert "total_entries" in data
        assert "completed_entries" in data
    
    def test_get_queue_stats_unauthorized(self, client: TestClient, test_bar, auth_headers):
        """Test getting queue statistics without proper permissions."""
        response = client.get(
            f"/bars/{test_bar.id}/queue/stats",
            headers=auth_headers
        )
        
        assert response.status_code == 403


class TestQueueValidation:
    """Test queue data validation."""
    
    def test_join_queue_missing_name(self, client: TestClient, test_bar):
        """Test joining queue without customer name."""
        response = client.post(
            f"/bars/{test_bar.id}/queue",
            json={"party_size": 2}
        )
        
        assert response.status_code == 422
    
    def test_join_queue_invalid_party_size(self, client: TestClient, test_bar):
        """Test joining queue with invalid party size."""
        response = client.post(
            f"/bars/{test_bar.id}/queue",
            json={
                "customer_name": "Test Customer",
                "party_size": 0
            }
        )
        
        assert response.status_code == 422
