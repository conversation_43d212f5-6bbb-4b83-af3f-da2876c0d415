"""
Authentication router for the QueTeToca API.
"""
import logging
from datetime import timed<PERSON><PERSON>
from typing import Annotated
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from ..database import get_db
from ..schemas.user import (
    AuthTokenRequest,
    AuthTokenResponse,
    CurrentUserResponse,
    UserResponse
)
from ..services.auth import verify_firebase_token, get_or_create_user, create_access_token
from ..services.dependencies import get_current_active_user
from ..models import User
from ..config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()

router = APIRouter(prefix="/auth", tags=["Authentication"])


@router.post("/verify-token", response_model=AuthTokenResponse)
async def verify_token(
    token_request: AuthTokenRequest,
    db: Annotated[Session, Depends(get_db)]
):
    """
    Verify Firebase ID token and return user information with access token.
    
    This endpoint:
    1. Verifies the Firebase ID token
    2. Gets or creates the user in the database
    3. Returns user information with a JWT access token
    
    Args:
        token_request: Firebase ID token
        db: Database session
        
    Returns:
        User information and JWT access token
        
    Raises:
        HTTPException: If token verification fails
    """
    try:
        # Verify Firebase token
        firebase_claims = await verify_firebase_token(token_request.token)
        if not firebase_claims:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or expired Firebase token",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Get or create user
        user = await get_or_create_user(db, firebase_claims)
        
        # Create access token
        access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
        access_token = create_access_token(
            data={"sub": user.uid, "email": user.email, "role": user.role},
            expires_delta=access_token_expires
        )
        
        # Convert user to response schema
        user_response = UserResponse.model_validate(user)
        
        logger.info(f"User authenticated successfully: {user.email}")
        
        return AuthTokenResponse(
            success=True,
            message="Authentication successful",
            user=user_response,
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.access_token_expire_minutes * 60  # Convert to seconds
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Authentication error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication service error"
        )


@router.get("/me", response_model=CurrentUserResponse)
async def get_current_user_info(
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    """
    Get current authenticated user information.
    
    This endpoint returns the current user's information based on the
    provided JWT access token.
    
    Args:
        current_user: Current authenticated user (from JWT token)
        
    Returns:
        Current user information
    """
    try:
        user_response = UserResponse.model_validate(current_user)
        
        return CurrentUserResponse(
            success=True,
            message="User information retrieved successfully",
            user=user_response
        )
    
    except Exception as e:
        logger.error(f"Error getting current user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving user information"
        )


@router.post("/refresh", response_model=AuthTokenResponse)
async def refresh_token(
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    """
    Refresh JWT access token.
    
    This endpoint allows users to refresh their JWT access token
    without re-authenticating with Firebase.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        New JWT access token
    """
    try:
        # Create new access token
        access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
        access_token = create_access_token(
            data={"sub": current_user.uid, "email": current_user.email, "role": current_user.role},
            expires_delta=access_token_expires
        )
        
        # Convert user to response schema
        user_response = UserResponse.model_validate(current_user)
        
        logger.info(f"Token refreshed for user: {current_user.email}")
        
        return AuthTokenResponse(
            success=True,
            message="Token refreshed successfully",
            user=user_response,
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.access_token_expire_minutes * 60
        )
    
    except Exception as e:
        logger.error(f"Token refresh error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh service error"
        )


@router.post("/logout")
async def logout(
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    """
    Logout current user.
    
    This endpoint logs out the current user. In a stateless JWT system,
    this mainly serves as a confirmation endpoint. The client should
    discard the token.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Logout confirmation
    """
    try:
        logger.info(f"User logged out: {current_user.email}")
        
        return {
            "success": True,
            "message": "Logged out successfully"
        }
    
    except Exception as e:
        logger.error(f"Logout error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout service error"
        )
