# QueTeToca API - Guía de Inicio Rápido

## 🚀 Instalación y Configuración

### 1. Requisitos Previos
- Python 3.11 o superior
- PostgreSQL (opcional, se puede usar SQLite para desarrollo)
- Firebase Project con Authentication habilitado

### 2. Instalación Local

```bash
# Clonar el repositorio
git clone <repository-url>
cd QueTeTocaApi

# Crear entorno virtual
python -m venv venv

# Activar entorno virtual
# En Windows:
venv\Scripts\activate
# En Linux/Mac:
source venv/bin/activate

# Instalar dependencias
pip install -r requirements.txt
```

### 3. Configuración

```bash
# Copiar archivo de configuración
cp .env.example .env

# Editar .env con tus configuraciones
# - DATABASE_URL: URL de tu base de datos
# - FIREBASE_CREDENTIALS_PATH: Ruta a tu archivo de credenciales de Firebase
# - SECRET_KEY: Clave secreta para JWT
```

### 4. Configuración de Firebase

1. Ve a [Firebase Console](https://console.firebase.google.com/)
2. Crea un nuevo proyecto o usa uno existente
3. Habilita Authentication
4. Ve a Project Settings > Service Accounts
5. Genera una nueva clave privada
6. Guarda el archivo JSON como `firebase-credentials.json` en la raíz del proyecto

### 5. Ejecutar la Aplicación

```bash
# Ejecutar en modo desarrollo
uvicorn api.app.main:app --reload --host 0.0.0.0 --port 8000

# O usar el script directo
python -m api.app.main
```

La API estará disponible en: http://localhost:8000

## 🐳 Ejecución con Docker

```bash
# Construir y ejecutar con Docker Compose
docker-compose up -d

# Ver logs
docker-compose logs -f api

# Parar servicios
docker-compose down
```

## 📚 Documentación de la API

Una vez que la aplicación esté ejecutándose:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

## 🧪 Ejecutar Tests

```bash
# Ejecutar todos los tests
pytest

# Ejecutar tests con cobertura
pytest --cov=api/app --cov-report=html

# Ejecutar tests específicos
pytest api/tests/test_auth.py
pytest -k "test_auth"

# Ejecutar tests marcados
pytest -m "not slow"
```

## 🔧 Comandos Útiles

### Base de Datos

```bash
# Crear migración (si usas Alembic)
alembic revision --autogenerate -m "Initial migration"

# Aplicar migraciones
alembic upgrade head
```

### Desarrollo

```bash
# Formatear código
black api/

# Ordenar imports
isort api/

# Linting
flake8 api/

# Type checking
mypy api/
```

## 📋 Endpoints Principales

### Autenticación
- `POST /auth/verify-token` - Verificar token de Firebase
- `GET /auth/me` - Obtener usuario actual
- `POST /auth/refresh` - Refrescar token
- `POST /auth/logout` - Cerrar sesión

### Bares
- `GET /bars` - Listar bares
- `POST /bars` - Crear bar
- `GET /bars/{bar_id}` - Obtener bar
- `PUT /bars/{bar_id}` - Actualizar bar
- `DELETE /bars/{bar_id}` - Eliminar bar
- `POST /bars/{bar_id}/qr` - Generar código QR
- `GET /bars/{bar_id}/stats` - Estadísticas del bar

### Colas
- `GET /bars/{bar_id}/queue` - Obtener cola
- `POST /bars/{bar_id}/queue` - Unirse a la cola
- `PUT /queue/{entry_id}/call` - Llamar cliente
- `PUT /queue/{entry_id}/complete` - Completar servicio
- `DELETE /queue/{entry_id}` - Cancelar entrada
- `GET /queue/{entry_id}` - Obtener entrada específica
- `WS /bars/{bar_id}/queue/ws` - WebSocket para actualizaciones en tiempo real

### Empresas
- `GET /companies` - Listar empresas
- `POST /companies` - Crear empresa
- `GET /companies/{company_id}` - Obtener empresa
- `PUT /companies/{company_id}` - Actualizar empresa
- `DELETE /companies/{company_id}` - Eliminar empresa
- `GET /companies/{company_id}/stats` - Estadísticas de la empresa

## 🔐 Autenticación

La API usa Firebase Authentication para verificar usuarios y JWT tokens para autorización:

1. El cliente obtiene un ID token de Firebase
2. Envía el token a `/auth/verify-token`
3. La API verifica el token con Firebase y devuelve un JWT
4. El cliente usa el JWT en el header `Authorization: Bearer <token>`

## 🌐 WebSockets

Para actualizaciones en tiempo real de las colas:

```javascript
const ws = new WebSocket('ws://localhost:8000/bars/{bar_id}/queue/ws');

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('Queue update:', data);
};
```

## 🚨 Solución de Problemas

### Error de Base de Datos
```bash
# Verificar conexión a PostgreSQL
psql -h localhost -U username -d database_name

# Para SQLite, verificar que el archivo se puede crear
touch quetetoca.db
```

### Error de Firebase
```bash
# Verificar que el archivo de credenciales existe
ls -la firebase-credentials.json

# Verificar formato del archivo
python -c "import json; print(json.load(open('firebase-credentials.json')))"
```

### Error de Dependencias
```bash
# Reinstalar dependencias
pip install --force-reinstall -r requirements.txt

# Verificar versión de Python
python --version
```

## 📈 Monitoreo y Logs

Los logs se configuran automáticamente:
- Formato JSON en producción
- Formato console en desarrollo
- Logs estructurados con información de contexto

## 🔒 Seguridad

La API incluye:
- Rate limiting
- Headers de seguridad
- Validación de entrada
- Autenticación y autorización
- Logging de eventos de seguridad

## 🤝 Contribución

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT - ver el archivo [LICENSE](LICENSE) para detalles.
