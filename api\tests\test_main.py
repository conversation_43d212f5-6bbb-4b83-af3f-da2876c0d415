"""
Tests for main application endpoints.
"""
import pytest
from fastapi.testclient import TestClient


class TestMainEndpoints:
    """Test main application endpoints."""
    
    def test_root_endpoint(self, client: TestClient):
        """Test the root endpoint."""
        response = client.get("/")
        
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "version" in data
        assert "status" in data
        assert data["status"] == "running"
    
    def test_health_check(self, client: TestClient):
        """Test the health check endpoint."""
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "version" in data
    
    def test_app_info(self, client: TestClient):
        """Test the application info endpoint."""
        response = client.get("/info")
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "QueTeToca API"
        assert "version" in data
        assert "features" in data
        assert "endpoints" in data
        assert isinstance(data["features"], list)
        assert len(data["features"]) > 0
    
    def test_not_found_endpoint(self, client: TestClient):
        """Test accessing a non-existent endpoint."""
        response = client.get("/non-existent-endpoint")
        
        assert response.status_code == 404
        data = response.json()
        assert data["success"] is False
        assert "not found" in data["message"].lower()
        assert data["error_code"] == "NOT_FOUND"
    
    def test_method_not_allowed(self, client: TestClient):
        """Test using wrong HTTP method."""
        response = client.post("/health")  # Health endpoint only accepts GET
        
        assert response.status_code == 405
        data = response.json()
        assert data["success"] is False
        assert "method not allowed" in data["message"].lower()
        assert data["error_code"] == "METHOD_NOT_ALLOWED"
    
    def test_security_headers(self, client: TestClient):
        """Test that security headers are present."""
        response = client.get("/")
        
        assert response.status_code == 200
        
        # Check security headers
        assert "X-Content-Type-Options" in response.headers
        assert response.headers["X-Content-Type-Options"] == "nosniff"
        
        assert "X-Frame-Options" in response.headers
        assert response.headers["X-Frame-Options"] == "DENY"
        
        assert "X-XSS-Protection" in response.headers
        assert response.headers["X-XSS-Protection"] == "1; mode=block"
        
        # Check API headers
        assert "X-API-Version" in response.headers
        assert response.headers["X-API-Version"] == "1.0.0"
        
        assert "X-Powered-By" in response.headers
        assert response.headers["X-Powered-By"] == "QueTeToca API"


class TestCORSHeaders:
    """Test CORS headers."""
    
    def test_cors_preflight(self, client: TestClient):
        """Test CORS preflight request."""
        response = client.options(
            "/bars",
            headers={
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "GET",
                "Access-Control-Request-Headers": "Authorization"
            }
        )
        
        # CORS preflight should be handled
        assert response.status_code in [200, 204]
    
    def test_cors_actual_request(self, client: TestClient):
        """Test actual CORS request."""
        response = client.get(
            "/",
            headers={"Origin": "http://localhost:3000"}
        )
        
        assert response.status_code == 200
        # CORS headers should be present (handled by FastAPI CORS middleware)


class TestErrorHandling:
    """Test error handling."""
    
    def test_validation_error(self, client: TestClient):
        """Test validation error handling."""
        # Send invalid JSON to an endpoint that expects valid data
        response = client.post(
            "/auth/verify-token",
            json={"invalid_field": "value"}  # Missing required 'token' field
        )
        
        assert response.status_code == 422
        data = response.json()
        assert data["success"] is False
        assert "validation" in data["message"].lower() or "field required" in str(data)
    
    def test_json_decode_error(self, client: TestClient):
        """Test JSON decode error handling."""
        response = client.post(
            "/auth/verify-token",
            data="invalid json",
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == 422


class TestRateLimiting:
    """Test rate limiting (basic tests)."""
    
    def test_rate_limit_not_exceeded(self, client: TestClient):
        """Test that normal requests are not rate limited."""
        # Make a few requests that should not trigger rate limiting
        for _ in range(5):
            response = client.get("/")
            assert response.status_code == 200
    
    # Note: Testing actual rate limiting would require making many requests
    # which might be slow for unit tests. Integration tests would be better
    # for comprehensive rate limiting testing.


class TestDebugEndpoints:
    """Test debug endpoints (if debug mode is enabled)."""
    
    def test_debug_config_endpoint(self, client: TestClient):
        """Test debug config endpoint."""
        response = client.get("/debug/config")
        
        # This endpoint might not be available in test mode
        # depending on configuration
        assert response.status_code in [200, 404]
        
        if response.status_code == 200:
            data = response.json()
            assert "environment" in data
            assert "debug" in data
    
    def test_debug_routes_endpoint(self, client: TestClient):
        """Test debug routes endpoint."""
        response = client.get("/debug/routes")
        
        # This endpoint might not be available in test mode
        assert response.status_code in [200, 404]
        
        if response.status_code == 200:
            data = response.json()
            assert "routes" in data
            assert isinstance(data["routes"], list)
