"""
WebSocket service for real-time queue updates.
"""
import json
import logging
from typing import Dict, List, Set
from fastapi import WebSocket, WebSocketDisconnect
from datetime import datetime

from ..schemas.queue import QueueStatusUpdate
from ..models.enums import QueueStatus

logger = logging.getLogger(__name__)


class ConnectionManager:
    """Manages WebSocket connections for real-time updates."""
    
    def __init__(self):
        # Store active connections by bar_id
        self.active_connections: Dict[str, Set[WebSocket]] = {}
        # Store connection metadata
        self.connection_metadata: Dict[WebSocket, Dict] = {}
    
    async def connect(self, websocket: WebSocket, bar_id: str, user_id: str = None):
        """Accept a new WebSocket connection."""
        await websocket.accept()
        
        # Add to bar's connection list
        if bar_id not in self.active_connections:
            self.active_connections[bar_id] = set()
        
        self.active_connections[bar_id].add(websocket)
        
        # Store metadata
        self.connection_metadata[websocket] = {
            "bar_id": bar_id,
            "user_id": user_id,
            "connected_at": datetime.utcnow()
        }
        
        logger.info(f"WebSocket connected for bar {bar_id}, user {user_id}")
    
    def disconnect(self, websocket: WebSocket):
        """Remove a WebSocket connection."""
        if websocket in self.connection_metadata:
            metadata = self.connection_metadata[websocket]
            bar_id = metadata["bar_id"]
            user_id = metadata["user_id"]
            
            # Remove from bar's connection list
            if bar_id in self.active_connections:
                self.active_connections[bar_id].discard(websocket)
                
                # Clean up empty bar connection lists
                if not self.active_connections[bar_id]:
                    del self.active_connections[bar_id]
            
            # Remove metadata
            del self.connection_metadata[websocket]
            
            logger.info(f"WebSocket disconnected for bar {bar_id}, user {user_id}")
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        """Send a message to a specific WebSocket connection."""
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"Error sending personal message: {e}")
            self.disconnect(websocket)
    
    async def broadcast_to_bar(self, message: str, bar_id: str):
        """Broadcast a message to all connections for a specific bar."""
        if bar_id not in self.active_connections:
            return
        
        # Create a copy of the set to avoid modification during iteration
        connections = self.active_connections[bar_id].copy()
        
        for connection in connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"Error broadcasting to connection: {e}")
                self.disconnect(connection)
    
    async def send_queue_update(self, bar_id: str, update: QueueStatusUpdate):
        """Send a queue status update to all bar connections."""
        message = {
            "type": "queue_update",
            "data": update.model_dump(),
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await self.broadcast_to_bar(json.dumps(message), bar_id)
    
    async def send_queue_join(self, bar_id: str, entry_data: dict):
        """Send notification when someone joins the queue."""
        message = {
            "type": "queue_join",
            "data": entry_data,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await self.broadcast_to_bar(json.dumps(message), bar_id)
    
    async def send_queue_call(self, bar_id: str, entry_data: dict):
        """Send notification when a customer is called."""
        message = {
            "type": "queue_call",
            "data": entry_data,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await self.broadcast_to_bar(json.dumps(message), bar_id)
    
    async def send_queue_complete(self, bar_id: str, entry_data: dict):
        """Send notification when a customer is served."""
        message = {
            "type": "queue_complete",
            "data": entry_data,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await self.broadcast_to_bar(json.dumps(message), bar_id)
    
    async def send_queue_cancel(self, bar_id: str, entry_data: dict):
        """Send notification when a queue entry is cancelled."""
        message = {
            "type": "queue_cancel",
            "data": entry_data,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await self.broadcast_to_bar(json.dumps(message), bar_id)
    
    async def send_heartbeat(self, bar_id: str):
        """Send heartbeat to keep connections alive."""
        message = {
            "type": "heartbeat",
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await self.broadcast_to_bar(json.dumps(message), bar_id)
    
    def get_connection_count(self, bar_id: str) -> int:
        """Get the number of active connections for a bar."""
        return len(self.active_connections.get(bar_id, set()))
    
    def get_total_connections(self) -> int:
        """Get the total number of active connections."""
        return sum(len(connections) for connections in self.active_connections.values())
    
    def get_bar_connections(self) -> Dict[str, int]:
        """Get connection counts by bar."""
        return {
            bar_id: len(connections) 
            for bar_id, connections in self.active_connections.items()
        }


# Global connection manager instance
manager = ConnectionManager()


class QueueWebSocketHandler:
    """Handler for queue-related WebSocket operations."""
    
    def __init__(self, connection_manager: ConnectionManager):
        self.manager = connection_manager
    
    async def handle_connection(self, websocket: WebSocket, bar_id: str, user_id: str = None):
        """Handle a new WebSocket connection for queue updates."""
        await self.manager.connect(websocket, bar_id, user_id)
        
        try:
            # Send initial connection confirmation
            await self.manager.send_personal_message(
                json.dumps({
                    "type": "connection_established",
                    "bar_id": bar_id,
                    "timestamp": datetime.utcnow().isoformat()
                }),
                websocket
            )
            
            # Keep connection alive
            while True:
                # Wait for messages from client
                data = await websocket.receive_text()
                
                # Handle client messages (ping, etc.)
                try:
                    message = json.loads(data)
                    await self._handle_client_message(websocket, message)
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON received from client: {data}")
                
        except WebSocketDisconnect:
            logger.info("WebSocket disconnected normally")
        except Exception as e:
            logger.error(f"WebSocket error: {e}")
        finally:
            self.manager.disconnect(websocket)
    
    async def _handle_client_message(self, websocket: WebSocket, message: dict):
        """Handle messages received from clients."""
        message_type = message.get("type")
        
        if message_type == "ping":
            # Respond to ping with pong
            await self.manager.send_personal_message(
                json.dumps({
                    "type": "pong",
                    "timestamp": datetime.utcnow().isoformat()
                }),
                websocket
            )
        elif message_type == "subscribe":
            # Handle subscription to specific updates
            logger.info(f"Client subscribed to updates: {message}")
        else:
            logger.warning(f"Unknown message type: {message_type}")


# Global WebSocket handler instance
websocket_handler = QueueWebSocketHandler(manager)
