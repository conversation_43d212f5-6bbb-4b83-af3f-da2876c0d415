"""
Bar schemas for the QueTeToca API.
"""
from typing import Optional, List, Dict, Any
from pydantic import Field, EmailStr

from .base import BaseSchema, TimestampMixin, ResponseBase


class BarBase(BaseSchema):
    """Base bar schema."""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    address: str = Field(..., min_length=1)
    city: Optional[str] = None
    state: Optional[str] = None
    country: Optional[str] = None
    postal_code: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    phone: Optional[str] = None
    email: Optional[EmailStr] = None
    website: Optional[str] = None


class BarCreate(BarBase):
    """Schema for creating a bar."""
    company_id: str = Field(..., min_length=1)
    max_queue_size: int = Field(default=100, ge=1, le=1000)
    estimated_wait_time: int = Field(default=15, ge=1, le=120)
    settings: Dict[str, Any] = Field(default_factory=dict)


class BarUpdate(BaseSchema):
    """Schema for updating a bar."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    address: Optional[str] = Field(None, min_length=1)
    city: Optional[str] = None
    state: Optional[str] = None
    country: Optional[str] = None
    postal_code: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    phone: Optional[str] = None
    email: Optional[EmailStr] = None
    website: Optional[str] = None
    max_queue_size: Optional[int] = Field(None, ge=1, le=1000)
    estimated_wait_time: Optional[int] = Field(None, ge=1, le=120)
    settings: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None


class BarInDB(BarBase, TimestampMixin):
    """Bar schema as stored in database."""
    id: str
    company_id: str
    qr_code: Optional[str] = None
    qr_code_url: Optional[str] = None
    max_queue_size: int
    estimated_wait_time: int
    settings: Dict[str, Any]
    is_active: str


class BarResponse(BarInDB):
    """Bar response schema."""
    active: bool
    current_queue_size: int
    is_queue_full: bool
    next_queue_number: int
    full_address: str


class BarListResponse(ResponseBase):
    """Bar list response schema."""
    bars: List[BarResponse]


class BarDetailResponse(ResponseBase):
    """Bar detail response schema."""
    bar: BarResponse


class QRCodeResponse(ResponseBase):
    """QR code generation response."""
    bar_id: str
    qr_code: str  # Base64 encoded image
    qr_code_url: str
    download_url: str


class BarStatsResponse(ResponseBase):
    """Bar statistics response."""
    bar_id: str
    current_queue_size: int
    total_served_today: int
    total_cancelled_today: int
    average_wait_time_today: Optional[float] = None
    peak_hour_today: Optional[int] = None
    queue_entries_by_hour: List[Dict[str, Any]]
    wait_times_by_hour: List[Dict[str, Any]]


class BarSettingsUpdate(BaseSchema):
    """Schema for updating bar settings."""
    settings: Dict[str, Any]
