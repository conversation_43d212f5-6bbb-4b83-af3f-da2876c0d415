"""
User model for the QueTeToca API.
"""
from datetime import datetime
from typing import Optional, List
from sqlalchemy import Column, String, DateTime, ForeignKey, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
import uuid

from ..database import Base
from .enums import UserRole


class User(Base):
    """User model."""
    
    __tablename__ = "users"
    
    # Primary key - using Firebase UID
    uid = Column(String(128), primary_key=True, index=True)
    
    # Basic user information
    email = Column(String(255), unique=True, index=True, nullable=False)
    display_name = Column(String(255), nullable=True)
    photo_url = Column(Text, nullable=True)
    
    # Role and company association
    role = Column(String(50), default=UserRole.CUSTOMER, nullable=False)
    company_id = Column(
        String(36), 
        ForeignKey("companies.id", ondelete="SET NULL"), 
        nullable=True,
        index=True
    )
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    last_login = Column(DateTime, nullable=True)
    
    # Relationships
    company = relationship("Company", back_populates="users")
    queue_entries = relationship("QueueEntry", back_populates="customer", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<User(uid='{self.uid}', email='{self.email}', role='{self.role}')>"
    
    @property
    def is_admin(self) -> bool:
        """Check if user is an admin."""
        return self.role in [UserRole.SUPER_ADMIN, UserRole.COMPANY_ADMIN]
    
    @property
    def is_company_admin(self) -> bool:
        """Check if user is a company admin."""
        return self.role == UserRole.COMPANY_ADMIN
    
    @property
    def is_bar_manager(self) -> bool:
        """Check if user is a bar manager."""
        return self.role == UserRole.BAR_MANAGER
    
    @property
    def is_customer(self) -> bool:
        """Check if user is a customer."""
        return self.role == UserRole.CUSTOMER
    
    def can_manage_company(self, company_id: str) -> bool:
        """Check if user can manage a specific company."""
        if self.role == UserRole.SUPER_ADMIN:
            return True
        return self.role == UserRole.COMPANY_ADMIN and self.company_id == company_id
    
    def can_manage_bar(self, bar) -> bool:
        """Check if user can manage a specific bar."""
        if self.role == UserRole.SUPER_ADMIN:
            return True
        if self.role == UserRole.COMPANY_ADMIN and self.company_id == bar.company_id:
            return True
        return self.role == UserRole.BAR_MANAGER and self.company_id == bar.company_id
    
    def update_last_login(self):
        """Update the last login timestamp."""
        self.last_login = datetime.utcnow()
