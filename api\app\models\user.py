"""
User model for the QueTeToca API.
"""
from datetime import datetime
from typing import Optional, List
from sqlalchemy import Column, String, DateTime, ForeignKey, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
import uuid

from ..database import Base
from .enums import UserRole


class User(Base):
    """User model."""
    
    __tablename__ = "users"
    
    # Primary key - using UUID for local auth or Firebase UID
    uid = Column(String(128), primary_key=True, index=True)

    # Basic user information
    email = Column(String(255), unique=True, index=True, nullable=False)
    display_name = Column(String(255), nullable=True)
    photo_url = Column(Text, nullable=True)

    # Local authentication fields
    password_hash = Column(String(255), nullable=True)

    # Role and company association
    role = Column(String(50), default=UserRole.CUSTOMER, nullable=False)
    company_id = Column(String(36), ForeignKey("companies.id"), nullable=True)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relationships
    company = relationship("Company", back_populates="users")
    queue_entries = relationship("QueueEntry", back_populates="customer", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<User(uid='{self.uid}', email='{self.email}', role='{self.role}')>"
    
    def set_password(self, password: str):
        """Set password hash."""
        from passlib.context import CryptContext
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        self.password_hash = pwd_context.hash(password)

    def verify_password(self, password: str) -> bool:
        """Verify password against hash."""
        if not self.password_hash:
            return False
        from passlib.context import CryptContext
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        return pwd_context.verify(password, self.password_hash)
