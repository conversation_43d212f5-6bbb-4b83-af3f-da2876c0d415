# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/quetetoca_db
# For SQLite (development): DATABASE_URL=sqlite:///./quetetoca.db

# Firebase Configuration
FIREBASE_CREDENTIALS_PATH=path/to/firebase-credentials.json
FIREBASE_PROJECT_ID=your-firebase-project-id

# API Configuration
SECRET_KEY=your-super-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
ALLOWED_HEADERS=*

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Environment
ENVIRONMENT=development
DEBUG=true

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# QR Code Configuration
QR_CODE_BASE_URL=https://yourdomain.com/queue

# WebSocket Configuration
WS_HEARTBEAT_INTERVAL=30
