"""
Queue schemas for the QueTeToca API.
"""
from datetime import datetime
from typing import Optional, List
from pydantic import Field

from .base import BaseSchema, TimestampMixin, ResponseBase
from ..models.enums import QueueStatus


class QueueEntryBase(BaseSchema):
    """Base queue entry schema."""
    customer_name: str = Field(..., min_length=1, max_length=255)
    phone_number: Optional[str] = None
    party_size: int = Field(default=1, ge=1, le=20)
    notes: Optional[str] = None


class QueueEntryCreate(QueueEntryBase):
    """Schema for creating a queue entry."""
    pass


class QueueEntryUpdate(BaseSchema):
    """Schema for updating a queue entry."""
    customer_name: Optional[str] = Field(None, min_length=1, max_length=255)
    phone_number: Optional[str] = None
    party_size: Optional[int] = Field(None, ge=1, le=20)
    notes: Optional[str] = None


class QueueEntryInDB(QueueEntryBase, TimestampMixin):
    """Queue entry schema as stored in database."""
    id: str
    bar_id: str
    customer_uid: Optional[str] = None
    queue_number: int
    status: QueueStatus
    estimated_wait_time: Optional[int] = None
    called_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    cancelled_at: Optional[datetime] = None


class QueueEntryResponse(QueueEntryInDB):
    """Queue entry response schema."""
    is_waiting: bool
    is_called: bool
    is_completed: bool
    is_cancelled: bool
    is_active: bool
    wait_time_minutes: Optional[int] = None
    total_time_minutes: Optional[int] = None
    current_wait_time_minutes: int
    position_in_queue: int


class QueueListResponse(ResponseBase):
    """Queue list response schema."""
    bar_id: str
    entries: List[QueueEntryResponse]
    total_waiting: int
    total_called: int
    estimated_wait_time: int


class QueueEntryDetailResponse(ResponseBase):
    """Queue entry detail response schema."""
    entry: QueueEntryResponse


class QueueJoinRequest(QueueEntryBase):
    """Schema for joining a queue."""
    pass


class QueueJoinResponse(ResponseBase):
    """Queue join response schema."""
    entry: QueueEntryResponse
    estimated_wait_time: int
    position_in_queue: int


class QueueCallRequest(BaseSchema):
    """Schema for calling a queue entry."""
    entry_id: str = Field(..., min_length=1)


class QueueCallResponse(ResponseBase):
    """Queue call response schema."""
    entry: QueueEntryResponse
    called_at: datetime


class QueueCompleteRequest(BaseSchema):
    """Schema for completing a queue entry."""
    entry_id: str = Field(..., min_length=1)


class QueueCompleteResponse(ResponseBase):
    """Queue complete response schema."""
    entry: QueueEntryResponse
    completed_at: datetime
    total_time_minutes: int


class QueueCancelRequest(BaseSchema):
    """Schema for cancelling a queue entry."""
    entry_id: str = Field(..., min_length=1)
    reason: Optional[str] = None


class QueueCancelResponse(ResponseBase):
    """Queue cancel response schema."""
    entry: QueueEntryResponse
    cancelled_at: datetime
    reason: Optional[str] = None


class QueueStatusUpdate(BaseSchema):
    """Schema for queue status updates via WebSocket."""
    bar_id: str
    entry_id: str
    status: QueueStatus
    queue_number: int
    customer_name: str
    timestamp: datetime
    position_in_queue: Optional[int] = None
    estimated_wait_time: Optional[int] = None


class QueueStatsResponse(ResponseBase):
    """Queue statistics response."""
    bar_id: str
    date: str
    total_entries: int
    completed_entries: int
    cancelled_entries: int
    average_wait_time: Optional[float] = None
    average_service_time: Optional[float] = None
    peak_hour: Optional[int] = None
    hourly_stats: List[dict]
