"""
Main FastAPI application for the QueTeToca API.
"""
import logging
from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, Request
from fastapi.responses import JSONResponse

from .config import get_settings
from .database import init_database, close_database
from .middleware import (
    add_cors_middleware,
    add_rate_limiting_middleware,
    add_error_handlers,
    ErrorHandlingMiddleware
)
from .utils.logging import setup_logging, RequestLoggingMiddleware
from .services.auth import initialize_firebase
from .routers import auth, bars, queue, companies

# Setup logging first
setup_logging()
logger = logging.getLogger(__name__)

settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting QueTeToca API...")
    
    try:
        # Initialize database
        await init_database()
        logger.info("Database initialized")
        
        # Initialize Firebase
        initialize_firebase()
        logger.info("Firebase initialized")
        
        logger.info("QueTeToca API started successfully")
        
    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down QueTeToca API...")
    
    try:
        # Close database connections
        await close_database()
        logger.info("Database connections closed")
        
        logger.info("QueTeToca API shutdown complete")
        
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")


# Create FastAPI application
app = FastAPI(
    title="QueTeToca API",
    description="API REST completa para la gestión de colas en bares",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan
)

# Add middleware (order matters!)
# 1. Error handling middleware (should be first)
app.add_middleware(ErrorHandlingMiddleware)

# 2. Request logging middleware
app.add_middleware(RequestLoggingMiddleware)

# 3. CORS middleware
add_cors_middleware(app)

# 4. Rate limiting middleware
add_rate_limiting_middleware(app)

# Add error handlers
add_error_handlers(app)

# Include routers
app.include_router(auth.router)
app.include_router(bars.router)
app.include_router(queue.router)
app.include_router(companies.router)


@app.get("/", tags=["Root"])
async def root():
    """Root endpoint."""
    return {
        "message": "QueTeToca API",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "running"
    }


@app.get("/health", tags=["Health"])
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": "2024-01-01T00:00:00Z",
        "version": "1.0.0",
        "environment": settings.environment
    }


@app.get("/info", tags=["Info"])
async def app_info():
    """Application information endpoint."""
    return {
        "name": "QueTeToca API",
        "version": "1.0.0",
        "description": "API REST completa para la gestión de colas en bares",
        "environment": settings.environment,
        "debug": settings.debug,
        "features": [
            "Authentication with Firebase",
            "Real-time queue updates with WebSockets",
            "QR code generation",
            "Statistics and analytics",
            "Rate limiting",
            "CORS support",
            "Structured logging"
        ],
        "endpoints": {
            "auth": "/auth",
            "bars": "/bars",
            "queue": "/queue",
            "companies": "/companies",
            "docs": "/docs",
            "health": "/health"
        }
    }


@app.middleware("http")
async def add_security_headers(request: Request, call_next):
    """Add security headers to all responses."""
    response = await call_next(request)
    
    # Security headers
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
    
    # API headers
    response.headers["X-API-Version"] = "1.0.0"
    response.headers["X-Powered-By"] = "QueTeToca API"
    
    return response


@app.exception_handler(404)
async def not_found_handler(request: Request, exc):
    """Custom 404 handler."""
    return JSONResponse(
        status_code=404,
        content={
            "success": False,
            "message": "Endpoint not found",
            "error_code": "NOT_FOUND",
            "path": str(request.url.path),
            "method": request.method
        }
    )


@app.exception_handler(405)
async def method_not_allowed_handler(request: Request, exc):
    """Custom 405 handler."""
    return JSONResponse(
        status_code=405,
        content={
            "success": False,
            "message": "Method not allowed",
            "error_code": "METHOD_NOT_ALLOWED",
            "path": str(request.url.path),
            "method": request.method
        }
    )


# Development endpoints (only available in debug mode)
if settings.debug:
    
    @app.get("/debug/config", tags=["Debug"])
    async def debug_config():
        """Debug endpoint to view configuration (debug mode only)."""
        return {
            "environment": settings.environment,
            "debug": settings.debug,
            "database_url": settings.database_url.split("@")[-1] if "@" in settings.database_url else "sqlite",
            "log_level": settings.log_level,
            "cors_origins": settings.allowed_origins,
            "rate_limit": f"{settings.rate_limit_requests}/{settings.rate_limit_window}s"
        }
    
    @app.get("/debug/routes", tags=["Debug"])
    async def debug_routes():
        """Debug endpoint to list all routes (debug mode only)."""
        routes = []
        for route in app.routes:
            if hasattr(route, 'methods') and hasattr(route, 'path'):
                routes.append({
                    "path": route.path,
                    "methods": list(route.methods),
                    "name": getattr(route, 'name', None)
                })
        return {"routes": routes}


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
