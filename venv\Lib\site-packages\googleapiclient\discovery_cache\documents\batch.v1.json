{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://batch.googleapis.com/", "batchPath": "batch", "canonicalName": "<PERSON><PERSON>", "description": "An API to manage the running of Batch resources on Google Cloud Platform.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/batch/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "batch:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://batch.mtls.googleapis.com/", "name": "batch", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"methods": {"get": {"description": "Gets information about a location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}", "httpMethod": "GET", "id": "batch.projects.locations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for the location.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Location"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1/projects/{projectsId}/locations", "httpMethod": "GET", "id": "batch.projects.locations.list", "parameterOrder": ["name"], "parameters": {"extraLocationTypes": {"description": "Optional. Do not use this field. It is unsupported and is ignored unless explicitly documented otherwise. This is primarily for internal usage.", "location": "query", "repeated": true, "type": "string"}, "filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like `\"displayName=tokyo\"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).", "location": "query", "type": "string"}, "name": {"description": "The resource that owns the locations collection, if applicable.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}}, "path": "v1/{+name}/locations", "response": {"$ref": "ListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"jobs": {"methods": {"cancel": {"description": "Cancel a Job.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}:cancel", "httpMethod": "POST", "id": "batch.projects.locations.jobs.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Job name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/jobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "request": {"$ref": "CancelJobRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Create a Job.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/jobs", "httpMethod": "POST", "id": "batch.projects.locations.jobs.create", "parameterOrder": ["parent"], "parameters": {"jobId": {"description": "ID used to uniquely identify the Job within its parent scope. This field should contain at most 63 characters and must start with lowercase characters. Only lowercase characters, numbers and '-' are accepted. The '-' character cannot be the first or the last one. A system generated ID will be used if the field is not set. The job.name field in the request will be ignored and the created resource name of the Job will be \"{parent}/jobs/{job_id}\".", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource name where the Job will be created. Pattern: \"projects/{project}/locations/{location}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/jobs", "request": {"$ref": "Job"}, "response": {"$ref": "Job"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete a Job.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}", "httpMethod": "DELETE", "id": "batch.projects.locations.jobs.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Job name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/jobs/[^/]+$", "required": true, "type": "string"}, "reason": {"description": "Optional. Reason for this deletion.", "location": "query", "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get a Job specified by its resource name.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}", "httpMethod": "GET", "id": "batch.projects.locations.jobs.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Job name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/jobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Job"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List all Jobs for a project within a region.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/jobs", "httpMethod": "GET", "id": "batch.projects.locations.jobs.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "List filter.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Sort results. Supported are \"name\", \"name desc\", \"create_time\", and \"create_time desc\".", "location": "query", "type": "string"}, "pageSize": {"description": "Page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token.", "location": "query", "type": "string"}, "parent": {"description": "Parent path.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/jobs", "response": {"$ref": "ListJobsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"taskGroups": {"resources": {"tasks": {"methods": {"get": {"description": "Return a single Task.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}/taskGroups/{taskGroupsId}/tasks/{tasksId}", "httpMethod": "GET", "id": "batch.projects.locations.jobs.taskGroups.tasks.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Task name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/jobs/[^/]+/taskGroups/[^/]+/tasks/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Task"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List Tasks associated with a job.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}/taskGroups/{taskGroupsId}/tasks", "httpMethod": "GET", "id": "batch.projects.locations.jobs.taskGroups.tasks.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Task filter, null filter matches all Tasks. Filter string should be of the format State=TaskStatus.State e.g. State=RUNNING", "location": "query", "type": "string"}, "pageSize": {"description": "Page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Name of a TaskGroup from which Tasks are being requested. Pattern: \"projects/{project}/locations/{location}/jobs/{job}/taskGroups/{task_group}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/jobs/[^/]+/taskGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/tasks", "response": {"$ref": "ListTasksResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "batch.projects.locations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "request": {"$ref": "CancelOperationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "batch.projects.locations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "batch.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "batch.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "state": {"methods": {"report": {"description": "Report agent's state, e.g. agent status and tasks information", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/state:report", "httpMethod": "POST", "id": "batch.projects.locations.state.report", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Format: projects/{project}/locations/{location} {project} should be a project number.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/state:report", "request": {"$ref": "ReportAgentStateRequest"}, "response": {"$ref": "ReportAgentStateResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "20250813", "rootUrl": "https://batch.googleapis.com/", "schemas": {"Accelerator": {"description": "Accelerator describes Compute Engine accelerators to be attached to the VM.", "id": "Accelerator", "properties": {"count": {"description": "The number of accelerators of this type.", "format": "int64", "type": "string"}, "driverVersion": {"description": "Optional. The NVIDIA GPU driver version that should be installed for this type. You can define the specific driver version such as \"470.103.01\", following the driver version requirements in https://cloud.google.com/compute/docs/gpus/install-drivers-gpu#minimum-driver. Batch will install the specific accelerator driver if qualified.", "type": "string"}, "installGpuDrivers": {"deprecated": true, "description": "Deprecated: please use instances[0].install_gpu_drivers instead.", "type": "boolean"}, "type": {"description": "The accelerator type. For example, \"nvidia-tesla-t4\". See `gcloud compute accelerator-types list`.", "type": "string"}}, "type": "object"}, "ActionCondition": {"description": "Conditions for actions to deal with task failures.", "id": "ActionCondition", "properties": {"exitCodes": {"description": "Exit codes of a task execution. If there are more than 1 exit codes, when task executes with any of the exit code in the list, the condition is met and the action will be executed.", "items": {"format": "int32", "type": "integer"}, "type": "array"}}, "type": "object"}, "AgentContainer": {"description": "Container runnable representation on the agent side.", "id": "Agent<PERSON><PERSON><PERSON>", "properties": {"commands": {"description": "Overrides the `CMD` specified in the container. If there is an ENTRYPOINT (either in the container image or with the entrypoint field below) then commands are appended as arguments to the ENTRYPOINT.", "items": {"type": "string"}, "type": "array"}, "entrypoint": {"description": "Overrides the `ENTRYPOINT` specified in the container.", "type": "string"}, "imageUri": {"description": "The URI to pull the container image from.", "type": "string"}, "options": {"description": "Arbitrary additional options to include in the \"docker run\" command when running this container, e.g. \"--network host\".", "type": "string"}, "volumes": {"description": "Volumes to mount (bind mount) from the host machine files or directories into the container, formatted to match docker run's --volume option, e.g. /foo:/bar, or /foo:/bar:ro", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "AgentEnvironment": {"description": "AgentEnvironment is the Environment representation between Agent and CLH communication. The environment is used in both task level and agent level.", "id": "AgentEnvironment", "properties": {"encryptedVariables": {"$ref": "AgentKMSEnvMap", "description": "An encrypted JSON dictionary where the key/value pairs correspond to environment variable names and their values."}, "secretVariables": {"additionalProperties": {"type": "string"}, "description": "A map of environment variable names to Secret Manager secret names. The VM will access the named secrets to set the value of each environment variable.", "type": "object"}, "variables": {"additionalProperties": {"type": "string"}, "description": "A map of environment variable names to values.", "type": "object"}}, "type": "object"}, "AgentInfo": {"description": "VM Agent Info.", "id": "AgentInfo", "properties": {"jobId": {"description": "Optional. The assigned Job ID", "type": "string"}, "reportTime": {"description": "When the AgentInfo is generated.", "format": "google-datetime", "type": "string"}, "state": {"description": "Agent state.", "enum": ["AGENT_STATE_UNSPECIFIED", "AGENT_STARTING", "AGENT_RUNNING", "AGENT_STOPPED"], "enumDescriptions": ["Unspecified state.", "The agent is starting on the VM instance.", "The agent is running. The agent in the RUNNING state can never go back to the STARTING state.", "The agent has stopped, either on request or due to a failure."], "type": "string"}, "taskGroupId": {"description": "The assigned task group ID.", "type": "string"}, "tasks": {"description": "Task Info.", "items": {"$ref": "AgentTaskInfo"}, "type": "array"}}, "type": "object"}, "AgentKMSEnvMap": {"description": "AgentKMSEnvMap contains the encrypted key/value pair to be used in the environment on the Agent side.", "id": "AgentKMSEnvMap", "properties": {"cipherText": {"description": "The value of the cipherText response from the `encrypt` method.", "type": "string"}, "keyName": {"description": "The name of the KMS key that will be used to decrypt the cipher text.", "type": "string"}}, "type": "object"}, "AgentMetadata": {"description": "VM Agent <PERSON>.", "id": "AgentMetadata", "properties": {"creationTime": {"deprecated": true, "description": "When the VM agent started. Use agent_startup_time instead.", "format": "google-datetime", "type": "string"}, "creator": {"description": "Full name of the entity that created this vm. For MIG, this path is: projects/{project}/regions/{region}/InstanceGroupManagers/{igm} The value is retrieved from the vm metadata key of \"created-by\".", "type": "string"}, "imageVersion": {"description": "image version for the VM that this agent is installed on.", "type": "string"}, "instance": {"description": "GCP instance name (go/instance-name).", "type": "string"}, "instanceId": {"description": "GCP instance ID (go/instance-id).", "format": "uint64", "type": "string"}, "instancePreemptionNoticeReceived": {"description": "If the GCP instance has received preemption notice.", "type": "boolean"}, "machineType": {"description": "Optional. machine type of the VM", "type": "string"}, "osRelease": {"additionalProperties": {"type": "string"}, "description": "parsed contents of /etc/os-release", "type": "object"}, "version": {"description": "agent binary version running on VM", "type": "string"}, "zone": {"description": "Agent zone.", "type": "string"}}, "type": "object"}, "AgentScript": {"description": "<PERSON><PERSON>t runnable representation on the agent side.", "id": "AgentScript", "properties": {"path": {"description": "Script file path on the host VM. To specify an interpreter, please add a `#!`(also known as [shebang line](https://en.wikipedia.org/wiki/Shebang_(Unix))) as the first line of the file.(For example, to execute the script using bash, `#!/bin/bash` should be the first line of the file. To execute the script using`Python3`, `#!/usr/bin/env python3` should be the first line of the file.) Otherwise, the file will by default be executed by `/bin/sh`.", "type": "string"}, "text": {"description": "Shell script text. To specify an interpreter, please add a `#!\\n` at the beginning of the text.(For example, to execute the script using bash, `#!/bin/bash\\n` should be added. To execute the script using`Python3`, `#!/usr/bin/env python3\\n` should be added.) Otherwise, the script will by default be executed by `/bin/sh`.", "type": "string"}}, "type": "object"}, "AgentTask": {"description": "TODO(b/182501497) The message needs to be redefined when the Agent API server updates data in storage per the backend design.", "id": "AgentTask", "properties": {"agentTaskSpec": {"$ref": "AgentTaskSpec", "description": "AgentTaskSpec is the taskSpec representation between Agent and CLH communication. This field will replace the TaskSpec field above in future to have a better separation between user-facaing API and internal API."}, "intendedState": {"description": "The intended state of the task.", "enum": ["INTENDED_STATE_UNSPECIFIED", "ASSIGNED", "CANCELLED", "DELETED"], "enumDescriptions": ["Unspecified state.", "Assigned state (includes running and finished).", "The agent should cancel the execution of this task.", "Delete task from agent storage, stop reporting its state."], "type": "string"}, "reachedBarrier": {"description": "The highest barrier reached by all tasks in the task's TaskGroup.", "format": "int64", "type": "string"}, "spec": {"$ref": "TaskSpec", "description": "Task Spec. This field will be replaced by agent_task_spec below in future."}, "status": {"$ref": "TaskStatus", "description": "Task status."}, "task": {"description": "Task name.", "type": "string"}, "taskSource": {"description": "TaskSource represents the source of the task.", "enum": ["TASK_SOURCE_UNSPECIFIED", "BATCH_INTERNAL", "USER"], "enumDescriptions": ["Unspecified task source.", "The AgentTask from this source is generated by Batch server. E.g. all the VMActions are from this source. When Batch Agent execute AgentTask from BATCH_INTERNAL, it will log stdout/err with \"batch_agent_logs\" log name.", "The AgentTask from this source is provided by Batch users. When Batch Agent execute AgentTask from USER, it will log stdout/err with \"batch_task_logs\" log name."], "type": "string"}}, "type": "object"}, "AgentTaskInfo": {"description": "Task Info", "id": "AgentTaskInfo", "properties": {"runnable": {"description": "The highest index of a runnable started by the agent for this task. The runnables are indexed from 1. Value 0 is undefined.", "format": "int64", "type": "string"}, "taskId": {"description": "ID of the Task", "type": "string"}, "taskStatus": {"$ref": "TaskStatus", "description": "The status of the Task. If we need agent specific fields we should fork the public TaskStatus into an agent specific one. Or add them below."}}, "type": "object"}, "AgentTaskLoggingOption": {"description": "AgentTaskLoggingOption contains the options for the logging of the task.", "id": "AgentTaskLoggingOption", "properties": {"labels": {"additionalProperties": {"type": "string"}, "description": "Labels to be added to the log entry. Now only cloud logging is supported.", "type": "object"}}, "type": "object"}, "AgentTaskRunnable": {"description": "AgentTaskRunnable is the Runnable representation between Agent and CLH communication.", "id": "AgentTaskRunnable", "properties": {"alwaysRun": {"description": "By default, after a Runnable fails, no further Runnable are executed. This flag indicates that this Runnable must be run even if the Task has already failed. This is useful for Runnables that copy output files off of the VM or for debugging. The always_run flag does not override the Task's overall max_run_duration. If the max_run_duration has expired then no further Runnables will execute, not even always_run Runnables.", "type": "boolean"}, "background": {"description": "This flag allows a Runnable to continue running in the background while the Task executes subsequent Runnables. This is useful to provide services to other Runnables (or to provide debugging support tools like SSH servers).", "type": "boolean"}, "container": {"$ref": "Agent<PERSON><PERSON><PERSON>", "description": "Container runnable."}, "environment": {"$ref": "AgentEnvironment", "description": "Environment variables for this Runnable (overrides variables set for the whole Task or TaskGroup)."}, "ignoreExitStatus": {"description": "Normally, a non-zero exit status causes the Task to fail. This flag allows execution of other Runnables to continue instead.", "type": "boolean"}, "script": {"$ref": "AgentScript", "description": "<PERSON><PERSON><PERSON> runnable."}, "timeout": {"description": "Timeout for this Runnable.", "format": "google-duration", "type": "string"}}, "type": "object"}, "AgentTaskSpec": {"description": "AgentTaskSpec is the user's TaskSpec representation between Agent and CLH communication.", "id": "AgentTaskSpec", "properties": {"environment": {"$ref": "AgentEnvironment", "description": "Environment variables to set before running the Task."}, "loggingOption": {"$ref": "AgentTaskLoggingOption", "description": "Logging option for the task."}, "maxRunDuration": {"description": "Maximum duration the task should run before being automatically retried (if enabled) or automatically failed. Format the value of this field as a time limit in seconds followed by `s`—for example, `3600s` for 1 hour. The field accepts any value between 0 and the maximum listed for the `Duration` field type at https://protobuf.dev/reference/protobuf/google.protobuf/#duration; however, the actual maximum run time for a job will be limited to the maximum run time for a job listed at https://cloud.google.com/batch/quotas#max-job-duration.", "format": "google-duration", "type": "string"}, "runnables": {"description": "AgentTaskRunnable is runanbles that will be executed on the agent.", "items": {"$ref": "AgentTaskRunnable"}, "type": "array"}, "userAccount": {"$ref": "AgentTaskUserAccount", "description": "User account on the VM to run the runnables in the agentTaskSpec. If not set, the runnable will be run under root user."}}, "type": "object"}, "AgentTaskUserAccount": {"description": "AgentTaskUserAccount contains the information of a POSIX account on the guest os which is used to execute the runnables.", "id": "AgentTaskUserAccount", "properties": {"gid": {"description": "gid id an unique identifier of the POSIX account group corresponding to the user account.", "format": "int64", "type": "string"}, "uid": {"description": "uid is an unique identifier of the POSIX account corresponding to the user account.", "format": "int64", "type": "string"}}, "type": "object"}, "AgentTimingInfo": {"description": "VM timing information", "id": "AgentTimingInfo", "properties": {"agentStartupTime": {"description": "Agent startup time", "format": "google-datetime", "type": "string"}, "bootTime": {"description": "Boot timestamp of the VM OS", "format": "google-datetime", "type": "string"}, "scriptStartupTime": {"description": "Startup time of the Batch VM script.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "AllocationPolicy": {"description": "A Job's resource allocation policy describes when, where, and how compute resources should be allocated for the Job.", "id": "AllocationPolicy", "properties": {"instances": {"description": "Describe instances that can be created by this AllocationPolicy. Only instances[0] is supported now.", "items": {"$ref": "InstancePolicyOrTemplate"}, "type": "array"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Custom labels to apply to the job and all the Compute Engine resources that both are created by this allocation policy and support labels. Use labels to group and describe the resources they are applied to. <PERSON>ch automatically applies predefined labels and supports multiple `labels` fields for each job, which each let you apply custom labels to various resources. Label names that start with \"goog-\" or \"google-\" are reserved for predefined labels. For more information about labels with <PERSON><PERSON>, see [Organize resources using labels](https://cloud.google.com/batch/docs/organize-resources-using-labels).", "type": "object"}, "location": {"$ref": "LocationPolicy", "description": "Location where compute resources should be allocated for the Job."}, "network": {"$ref": "NetworkPolicy", "description": "The network policy. If you define an instance template in the `InstancePolicyOrTemplate` field, <PERSON><PERSON> will use the network settings in the instance template instead of this field."}, "placement": {"$ref": "PlacementPolicy", "description": "The placement policy."}, "serviceAccount": {"$ref": "ServiceAccount", "description": "Defines the service account for Batch-created VMs. If omitted, the [default Compute Engine service account](https://cloud.google.com/compute/docs/access/service-accounts#default_service_account) is used. Must match the service account specified in any used instance template configured in the Batch job. Includes the following fields: * email: The service account's email address. If not set, the default Compute Engine service account is used. * scopes: Additional OAuth scopes to grant the service account, beyond the default cloud-platform scope. (list of strings)"}, "tags": {"description": "Optional. Tags applied to the VM instances. The tags identify valid sources or targets for network firewalls. Each tag must be 1-63 characters long, and comply with [RFC1035](https://www.ietf.org/rfc/rfc1035.txt).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "AttachedDisk": {"description": "A new or an existing persistent disk (PD) or a local ssd attached to a VM instance.", "id": "AttachedDisk", "properties": {"deviceName": {"description": "Device name that the guest operating system will see. It is used by Runnable.volumes field to mount disks. So please specify the device_name if you want <PERSON><PERSON> to help mount the disk, and it should match the device_name field in volumes.", "type": "string"}, "existingDisk": {"description": "Name of an existing PD.", "type": "string"}, "newDisk": {"$ref": "Disk"}}, "type": "object"}, "Barrier": {"description": "A barrier runnable automatically blocks the execution of subsequent runnables until all the tasks in the task group reach the barrier.", "id": "Barrier", "properties": {"name": {"description": "Barriers are identified by their index in runnable list. Names are not required, but if present should be an identifier.", "type": "string"}}, "type": "object"}, "CancelJobRequest": {"description": "CancelJob Request.", "id": "CancelJobRequest", "properties": {"requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}}, "type": "object"}, "CancelOperationRequest": {"description": "The request message for Operations.CancelOperation.", "id": "CancelOperationRequest", "properties": {}, "type": "object"}, "CloudLoggingOption": {"description": "`CloudLoggingOption` contains additional settings for Cloud Logging logs generated by Batch job.", "id": "CloudLoggingOption", "properties": {"useGenericTaskMonitoredResource": {"description": "Optional. Set this field to `true` to change the [monitored resource type](https://cloud.google.com/monitoring/api/resources) for Cloud Logging logs generated by this Batch job from the [`batch.googleapis.com/Job`](https://cloud.google.com/monitoring/api/resources#tag_batch.googleapis.com/Job) type to the formerly used [`generic_task`](https://cloud.google.com/monitoring/api/resources#tag_generic_task) type.", "type": "boolean"}}, "type": "object"}, "ComputeResource": {"description": "Compute resource requirements. ComputeResource defines the amount of resources required for each task. Make sure your tasks have enough resources to successfully run. If you also define the types of resources for a job to use with the [InstancePolicyOrTemplate](https://cloud.google.com/batch/docs/reference/rest/v1/projects.locations.jobs#instancepolicyortemplate) field, make sure both fields are compatible with each other.", "id": "ComputeResource", "properties": {"bootDiskMib": {"description": "Extra boot disk size in MiB for each task.", "format": "int64", "type": "string"}, "cpuMilli": {"description": "The milliCPU count. `cpuMilli` defines the amount of CPU resources per task in milliCPU units. For example, `1000` corresponds to 1 vCPU per task. If undefined, the default value is `2000`. If you also define the VM's machine type using the `machineType` in [InstancePolicy](https://cloud.google.com/batch/docs/reference/rest/v1/projects.locations.jobs#instancepolicy) field or inside the `instanceTemplate` in the [InstancePolicyOrTemplate](https://cloud.google.com/batch/docs/reference/rest/v1/projects.locations.jobs#instancepolicyortemplate) field, make sure the CPU resources for both fields are compatible with each other and with how many tasks you want to allow to run on the same VM at the same time. For example, if you specify the `n2-standard-2` machine type, which has 2 vCPUs each, you are recommended to set `cpuMilli` no more than `2000`, or you are recommended to run two tasks on the same VM if you set `cpuMilli` to `1000` or less.", "format": "int64", "type": "string"}, "memoryMib": {"description": "Memory in MiB. `memoryMib` defines the amount of memory per task in MiB units. If undefined, the default value is `2000`. If you also define the VM's machine type using the `machineType` in [InstancePolicy](https://cloud.google.com/batch/docs/reference/rest/v1/projects.locations.jobs#instancepolicy) field or inside the `instanceTemplate` in the [InstancePolicyOrTemplate](https://cloud.google.com/batch/docs/reference/rest/v1/projects.locations.jobs#instancepolicyortemplate) field, make sure the memory resources for both fields are compatible with each other and with how many tasks you want to allow to run on the same VM at the same time. For example, if you specify the `n2-standard-2` machine type, which has 8 GiB each, you are recommended to set `memoryMib` to no more than `8192`, or you are recommended to run two tasks on the same VM if you set `memoryMib` to `4096` or less.", "format": "int64", "type": "string"}}, "type": "object"}, "Container": {"description": "Container runnable.", "id": "Container", "properties": {"blockExternalNetwork": {"description": "If set to true, external network access to and from container will be blocked, containers that are with block_external_network as true can still communicate with each other, network cannot be specified in the `container.options` field.", "type": "boolean"}, "commands": {"description": "Required for some container images. Overrides the `CMD` specified in the container. If there is an `ENTRYPOINT` (either in the container image or with the `entrypoint` field below) then these commands are appended as arguments to the `ENTRYPOINT`.", "items": {"type": "string"}, "type": "array"}, "enableImageStreaming": {"description": "Optional. If set to true, this container runnable uses Image streaming. Use Image streaming to allow the runnable to initialize without waiting for the entire container image to download, which can significantly reduce startup time for large container images. When `enableImageStreaming` is set to true, the container runtime is [containerd](https://containerd.io/) instead of Docker. Additionally, this container runnable only supports the following `container` subfields: `imageUri`, `commands[]`, `entrypoint`, and `volumes[]`; any other `container` subfields are ignored. For more information about the requirements and limitations for using Image streaming with Batch, see the [`image-streaming` sample on GitHub](https://github.com/GoogleCloudPlatform/batch-samples/tree/main/api-samples/image-streaming).", "type": "boolean"}, "entrypoint": {"description": "Required for some container images. Overrides the `ENTRYPOINT` specified in the container.", "type": "string"}, "imageUri": {"description": "Required. The URI to pull the container image from.", "type": "string"}, "options": {"description": "Required for some container images. Arbitrary additional options to include in the `docker run` command when running this container—for example, `--network host`. For the `--volume` option, use the `volumes` field for the container.", "type": "string"}, "password": {"description": "Required if the container image is from a private Docker registry. The password to login to the Docker registry that contains the image. For security, it is strongly recommended to specify an encrypted password by using a Secret Manager secret: `projects/*/secrets/*/versions/*`. Warning: If you specify the password using plain text, you risk the password being exposed to any users who can view the job or its logs. To avoid this risk, specify a secret that contains the password instead. Learn more about [Secret Manager](https://cloud.google.com/secret-manager/docs/) and [using Secret Manager with Batch](https://cloud.google.com/batch/docs/create-run-job-secret-manager).", "type": "string"}, "username": {"description": "Required if the container image is from a private Docker registry. The username to login to the Docker registry that contains the image. You can either specify the username directly by using plain text or specify an encrypted username by using a Secret Manager secret: `projects/*/secrets/*/versions/*`. However, using a secret is recommended for enhanced security. Caution: If you specify the username using plain text, you risk the username being exposed to any users who can view the job or its logs. To avoid this risk, specify a secret that contains the username instead. Learn more about [Secret Manager](https://cloud.google.com/secret-manager/docs/) and [using Secret Manager with Batch](https://cloud.google.com/batch/docs/create-run-job-secret-manager).", "type": "string"}, "volumes": {"description": "Volumes to mount (bind mount) from the host machine files or directories into the container, formatted to match `--volume` option for the `docker run` command—for example, `/foo:/bar` or `/foo:/bar:ro`. If the `TaskSpec.Volumes` field is specified but this field is not, <PERSON><PERSON> will mount each volume from the host machine to the container with the same mount path by default. In this case, the default mount option for containers will be read-only (`ro`) for existing persistent disks and read-write (`rw`) for other volume types, regardless of the original mount options specified in `TaskSpec.Volumes`. If you need different mount settings, you can explicitly configure them in this field.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Disk": {"description": "A new persistent disk or a local ssd. A VM can only have one local SSD setting but multiple local SSD partitions. See https://cloud.google.com/compute/docs/disks#pdspecs and https://cloud.google.com/compute/docs/disks#localssds.", "id": "Disk", "properties": {"diskInterface": {"description": "Local SSDs are available through both \"SCSI\" and \"NVMe\" interfaces. If not indicated, \"NVMe\" will be the default one for local ssds. This field is ignored for persistent disks as the interface is chosen automatically. See https://cloud.google.com/compute/docs/disks/persistent-disks#choose_an_interface.", "type": "string"}, "image": {"description": "URL for a VM image to use as the data source for this disk. For example, the following are all valid URLs: * Specify the image by its family name: projects/{project}/global/images/family/{image_family} * Specify the image version: projects/{project}/global/images/{image_version} You can also use Batch customized image in short names. The following image values are supported for a boot disk: * `batch-debian`: use Batch Debian images. * `batch-cos`: use Batch Container-Optimized images. * `batch-hpc-rocky`: use Batch HPC Rocky Linux images.", "type": "string"}, "sizeGb": {"description": "Disk size in GB. **Non-Boot Disk**: If the `type` specifies a persistent disk, this field is ignored if `data_source` is set as `image` or `snapshot`. If the `type` specifies a local SSD, this field should be a multiple of 375 GB, otherwise, the final size will be the next greater multiple of 375 GB. **Boot Disk**: <PERSON><PERSON> will calculate the boot disk size based on source image and task requirements if you do not speicify the size. If both this field and the `boot_disk_mib` field in task spec's `compute_resource` are defined, <PERSON><PERSON> will only honor this field. Also, this field should be no smaller than the source disk's size when the `data_source` is set as `snapshot` or `image`. For example, if you set an image as the `data_source` field and the image's default disk size 30 GB, you can only use this field to make the disk larger or equal to 30 GB.", "format": "int64", "type": "string"}, "snapshot": {"description": "Name of a snapshot used as the data source. Snapshot is not supported as boot disk now.", "type": "string"}, "type": {"description": "Disk type as shown in `gcloud compute disk-types list`. For example, local SSD uses type \"local-ssd\". Persistent disks and boot disks use \"pd-balanced\", \"pd-extreme\", \"pd-ssd\" or \"pd-standard\". If not specified, \"pd-standard\" will be used as the default type for non-boot disks, \"pd-balanced\" will be used as the default type for boot disks.", "type": "string"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "Environment": {"description": "An Environment describes a collection of environment variables to set when executing Tasks.", "id": "Environment", "properties": {"encryptedVariables": {"$ref": "KMSEnvMap", "description": "An encrypted JSON dictionary where the key/value pairs correspond to environment variable names and their values."}, "secretVariables": {"additionalProperties": {"type": "string"}, "description": "A map of environment variable names to Secret Manager secret names. The VM will access the named secrets to set the value of each environment variable.", "type": "object"}, "variables": {"additionalProperties": {"type": "string"}, "description": "A map of environment variable names to values.", "type": "object"}}, "type": "object"}, "GCS": {"description": "Represents a Google Cloud Storage volume.", "id": "GCS", "properties": {"remotePath": {"description": "Remote path, either a bucket name or a subdirectory of a bucket, e.g.: bucket_name, bucket_name/subdirectory/", "type": "string"}}, "type": "object"}, "InstancePolicy": {"description": "InstancePolicy describes an instance type and resources attached to each VM created by this InstancePolicy.", "id": "InstancePolicy", "properties": {"accelerators": {"description": "The accelerators attached to each VM instance.", "items": {"$ref": "Accelerator"}, "type": "array"}, "bootDisk": {"$ref": "Disk", "description": "Boot disk to be created and attached to each VM by this InstancePolicy. Boot disk will be deleted when the VM is deleted. Batch API now only supports booting from image."}, "disks": {"description": "Non-boot disks to be attached for each VM created by this InstancePolicy. New disks will be deleted when the VM is deleted. A non-boot disk is a disk that can be of a device with a file system or a raw storage drive that is not ready for data storage and accessing.", "items": {"$ref": "AttachedDisk"}, "type": "array"}, "machineType": {"description": "The Compute Engine machine type.", "type": "string"}, "minCpuPlatform": {"description": "The minimum CPU platform. See https://cloud.google.com/compute/docs/instances/specify-min-cpu-platform.", "type": "string"}, "provisioningModel": {"description": "The provisioning model.", "enum": ["PROVISIONING_MODEL_UNSPECIFIED", "STANDARD", "SPOT", "PREEMPTIBLE", "RESERVATION_BOUND"], "enumDeprecated": [false, false, false, true, false], "enumDescriptions": ["Unspecified.", "Standard VM.", "SPOT VM.", "Preemptible VM (PVM). Above SPOT VM is the preferable model for preemptible VM instances: the old preemptible VM model (indicated by this field) is the older model, and has been migrated to use the SPOT model as the underlying technology. This old model will still be supported.", "Bound to the lifecycle of the reservation in which it is provisioned."], "type": "string"}, "reservation": {"description": "Optional. If not specified (default), VMs will consume any applicable reservation. If \"NO_RESERVATION\" is specified, VMs will not consume any reservation. Otherwise, if specified, VMs will consume only the specified reservation.", "type": "string"}}, "type": "object"}, "InstancePolicyOrTemplate": {"description": "InstancePolicyOrTemplate lets you define the type of resources to use for this job either with an InstancePolicy or an instance template. If undefined, <PERSON><PERSON> picks the type of VM to use and doesn't include optional VM resources such as GPUs and extra disks.", "id": "InstancePolicyOrTemplate", "properties": {"blockProjectSshKeys": {"description": "Optional. Set this field to `true` if you want <PERSON><PERSON> to block project-level SSH keys from accessing this job's VMs. Alternatively, you can configure the job to specify a VM instance template that blocks project-level SSH keys. In either case, <PERSON><PERSON> blocks project-level SSH keys while creating the VMs for this job. Batch allows project-level SSH keys for a job's VMs only if all the following are true: + This field is undefined or set to `false`. + The job's VM instance template (if any) doesn't block project-level SSH keys. Notably, you can override this behavior by manually updating a VM to block or allow project-level SSH keys. For more information about blocking project-level SSH keys, see the Compute Engine documentation: https://cloud.google.com/compute/docs/connect/restrict-ssh-keys#block-keys", "type": "boolean"}, "installGpuDrivers": {"description": "Set this field true if you want <PERSON><PERSON> to help fetch drivers from a third party location and install them for GPUs specified in `policy.accelerators` or `instance_template` on your behalf. Default is false. For Container-Optimized Image cases, <PERSON><PERSON> will install the accelerator driver following milestones of https://cloud.google.com/container-optimized-os/docs/release-notes. For non Container-Optimized Image cases, following https://github.com/GoogleCloudPlatform/compute-gpu-installation/blob/main/linux/install_gpu_driver.py.", "type": "boolean"}, "installOpsAgent": {"description": "Optional. Set this field true if you want <PERSON><PERSON> to install Ops Agent on your behalf. Default is false.", "type": "boolean"}, "instanceTemplate": {"description": "Name of an instance template used to create VMs. Named the field as 'instance_template' instead of 'template' to avoid C++ keyword conflict. Batch only supports global instance templates from the same project as the job. You can specify the global instance template as a full or partial URL.", "type": "string"}, "policy": {"$ref": "InstancePolicy", "description": "InstancePolicy."}}, "type": "object"}, "InstanceStatus": {"description": "VM instance status.", "id": "InstanceStatus", "properties": {"bootDisk": {"$ref": "Disk", "description": "The VM boot disk."}, "machineType": {"description": "The Compute Engine machine type.", "type": "string"}, "provisioningModel": {"description": "The VM instance provisioning model.", "enum": ["PROVISIONING_MODEL_UNSPECIFIED", "STANDARD", "SPOT", "PREEMPTIBLE", "RESERVATION_BOUND"], "enumDeprecated": [false, false, false, true, false], "enumDescriptions": ["Unspecified.", "Standard VM.", "SPOT VM.", "Preemptible VM (PVM). Above SPOT VM is the preferable model for preemptible VM instances: the old preemptible VM model (indicated by this field) is the older model, and has been migrated to use the SPOT model as the underlying technology. This old model will still be supported.", "Bound to the lifecycle of the reservation in which it is provisioned."], "type": "string"}, "taskPack": {"description": "The max number of tasks can be assigned to this instance type.", "format": "int64", "type": "string"}}, "type": "object"}, "Job": {"description": "The Cloud Batch Job description.", "id": "Job", "properties": {"allocationPolicy": {"$ref": "AllocationPolicy", "description": "Compute resource allocation for all TaskGroups in the Job."}, "createTime": {"description": "Output only. When the Job was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Custom labels to apply to the job and any Cloud Logging [LogEntry](https://cloud.google.com/logging/docs/reference/v2/rest/v2/LogEntry) that it generates. Use labels to group and describe the resources they are applied to. Batch automatically applies predefined labels and supports multiple `labels` fields for each job, which each let you apply custom labels to various resources. Label names that start with \"goog-\" or \"google-\" are reserved for predefined labels. For more information about labels with <PERSON><PERSON>, see [Organize resources using labels](https://cloud.google.com/batch/docs/organize-resources-using-labels).", "type": "object"}, "logsPolicy": {"$ref": "LogsPolicy", "description": "Log preservation policy for the Job."}, "name": {"description": "Output only. Job name. For example: \"projects/123456/locations/us-central1/jobs/job01\".", "readOnly": true, "type": "string"}, "notifications": {"description": "Notification configurations.", "items": {"$ref": "JobNotification"}, "type": "array"}, "priority": {"description": "Priority of the Job. The valid value range is [0, 100). Default value is 0. Higher value indicates higher priority. A job with higher priority value is more likely to run earlier if all other requirements are satisfied.", "format": "int64", "type": "string"}, "status": {"$ref": "JobStatus", "description": "Output only. Job status. It is read only for users.", "readOnly": true}, "taskGroups": {"description": "Required. TaskGroups in the Job. Only one TaskGroup is supported now.", "items": {"$ref": "TaskGroup"}, "type": "array"}, "uid": {"description": "Output only. A system generated unique ID for the Job.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The last time the Job was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "JobNotification": {"description": "Notification configurations.", "id": "JobNotification", "properties": {"message": {"$ref": "Message", "description": "The attribute requirements of messages to be sent to this Pub/Sub topic. Without this field, no message will be sent."}, "pubsubTopic": {"description": "The Pub/Sub topic where notifications for the job, like state changes, will be published. If undefined, no Pub/Sub notifications are sent for this job. Specify the topic using the following format: `projects/{project}/topics/{topic}`. Notably, if you want to specify a Pub/Sub topic that is in a different project than the job, your administrator must grant your project's Batch service agent permission to publish to that topic. For more information about configuring Pub/Sub notifications for a job, see https://cloud.google.com/batch/docs/enable-notifications.", "type": "string"}}, "type": "object"}, "JobStatus": {"description": "Job status.", "id": "JobStatus", "properties": {"runDuration": {"description": "The duration of time that the Job spent in status RUNNING.", "format": "google-duration", "type": "string"}, "state": {"description": "Job state", "enum": ["STATE_UNSPECIFIED", "QUEUED", "SCHEDULED", "RUNNING", "SUCCEEDED", "FAILED", "DELETION_IN_PROGRESS", "CANCELLATION_IN_PROGRESS", "CANCELLED"], "enumDescriptions": ["Job state unspecified.", "Job is admitted (validated and persisted) and waiting for resources.", "Job is scheduled to run as soon as resource allocation is ready. The resource allocation may happen at a later time but with a high chance to succeed.", "Resource allocation has been successful. At least one Task in the Job is RUNNING.", "All Tasks in the Job have finished successfully.", "At least one Task in the Job has failed.", "The Job will be deleted, but has not been deleted yet. Typically this is because resources used by the Job are still being cleaned up.", "The Job cancellation is in progress, this is because the resources used by the Job are still being cleaned up.", "The Job has been cancelled, the task executions were stopped and the resources were cleaned up."], "type": "string"}, "statusEvents": {"description": "Job status events", "items": {"$ref": "StatusEvent"}, "type": "array"}, "taskGroups": {"additionalProperties": {"$ref": "TaskGroupStatus"}, "description": "Aggregated task status for each TaskGroup in the Job. The map key is TaskGroup ID.", "type": "object"}}, "type": "object"}, "KMSEnvMap": {"id": "KMSEnvMap", "properties": {"cipherText": {"description": "The value of the cipherText response from the `encrypt` method.", "type": "string"}, "keyName": {"description": "The name of the KMS key that will be used to decrypt the cipher text.", "type": "string"}}, "type": "object"}, "LifecyclePolicy": {"description": "LifecyclePolicy describes how to deal with task failures based on different conditions.", "id": "LifecyclePolicy", "properties": {"action": {"description": "Action to execute when ActionCondition is true. When RETRY_TASK is specified, we will retry failed tasks if we notice any exit code match and fail tasks if no match is found. Likewise, when FAIL_TASK is specified, we will fail tasks if we notice any exit code match and retry tasks if no match is found.", "enum": ["ACTION_UNSPECIFIED", "RETRY_TASK", "FAIL_TASK"], "enumDescriptions": ["Action unspecified.", "Action that tasks in the group will be scheduled to re-execute.", "Action that tasks in the group will be stopped immediately."], "type": "string"}, "actionCondition": {"$ref": "ActionCondition", "description": "Conditions that decide why a task failure is dealt with a specific action."}}, "type": "object"}, "ListJobsResponse": {"description": "ListJob Response.", "id": "ListJobsResponse", "properties": {"jobs": {"description": "Jobs.", "items": {"$ref": "Job"}, "type": "array"}, "nextPageToken": {"description": "Next page token.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListLocationsResponse": {"description": "The response message for Locations.ListLocations.", "id": "ListLocationsResponse", "properties": {"locations": {"description": "A list of locations that matches the specified filter in the request.", "items": {"$ref": "Location"}, "type": "array"}, "nextPageToken": {"description": "The standard List next-page token.", "type": "string"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "ListTasksResponse": {"description": "ListTasks Response.", "id": "ListTasksResponse", "properties": {"nextPageToken": {"description": "Next page token.", "type": "string"}, "tasks": {"description": "Tasks.", "items": {"$ref": "Task"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Location": {"description": "A resource that represents a Google Cloud location.", "id": "Location", "properties": {"displayName": {"description": "The friendly name for this location, typically a nearby city name. For example, \"Tokyo\".", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cross-service attributes for the location. For example {\"cloud.googleapis.com/region\": \"us-east1\"}", "type": "object"}, "locationId": {"description": "The canonical id for this location. For example: `\"us-east1\"`.", "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata. For example the available capacity at the given location.", "type": "object"}, "name": {"description": "Resource name for the location, which may vary between implementations. For example: `\"projects/example-project/locations/us-east1\"`", "type": "string"}}, "type": "object"}, "LocationPolicy": {"id": "LocationPolicy", "properties": {"allowedLocations": {"description": "A list of allowed location names represented by internal URLs. Each location can be a region or a zone. Only one region or multiple zones in one region is supported now. For example, [\"regions/us-central1\"] allow VMs in any zones in region us-central1. [\"zones/us-central1-a\", \"zones/us-central1-c\"] only allow VMs in zones us-central1-a and us-central1-c. Mixing locations from different regions would cause errors. For example, [\"regions/us-central1\", \"zones/us-central1-a\", \"zones/us-central1-b\", \"zones/us-west1-a\"] contains locations from two distinct regions: us-central1 and us-west1. This combination will trigger an error.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "LogsPolicy": {"description": "LogsPolicy describes if and how a job's logs are preserved. Logs include information that is automatically written by the Batch service agent and any information that you configured the job's runnables to write to the `stdout` or `stderr` streams.", "id": "LogsPolicy", "properties": {"cloudLoggingOption": {"$ref": "CloudLoggingOption", "description": "Optional. When `destination` is set to `CLOUD_LOGGING`, you can optionally set this field to configure additional settings for Cloud Logging."}, "destination": {"description": "If and where logs should be saved.", "enum": ["DESTINATION_UNSPECIFIED", "CLOUD_LOGGING", "PATH"], "enumDescriptions": ["(Default) Logs are not preserved.", "Logs are streamed to Cloud Logging. Optionally, you can configure additional settings in the `cloudLoggingOption` field.", "Logs are saved to the file path specified in the `logsPath` field."], "type": "string"}, "logsPath": {"description": "When `destination` is set to `PATH`, you must set this field to the path where you want logs to be saved. This path can point to a local directory on the VM or (if congifured) a directory under the mount path of any Cloud Storage bucket, network file system (NFS), or writable persistent disk that is mounted to the job. For example, if the job has a bucket with `mountPath` set to `/mnt/disks/my-bucket`, you can write logs to the root directory of the `remotePath` of that bucket by setting this field to `/mnt/disks/my-bucket/`.", "type": "string"}}, "type": "object"}, "Message": {"description": "Message details. Describe the conditions under which messages will be sent. If no attribute is defined, no message will be sent by default. One message should specify either the job or the task level attributes, but not both. For example, job level: JOB_STATE_CHANGED and/or a specified new_job_state; task level: TASK_STATE_CHANGED and/or a specified new_task_state.", "id": "Message", "properties": {"newJobState": {"description": "The new job state.", "enum": ["STATE_UNSPECIFIED", "QUEUED", "SCHEDULED", "RUNNING", "SUCCEEDED", "FAILED", "DELETION_IN_PROGRESS", "CANCELLATION_IN_PROGRESS", "CANCELLED"], "enumDescriptions": ["Job state unspecified.", "Job is admitted (validated and persisted) and waiting for resources.", "Job is scheduled to run as soon as resource allocation is ready. The resource allocation may happen at a later time but with a high chance to succeed.", "Resource allocation has been successful. At least one Task in the Job is RUNNING.", "All Tasks in the Job have finished successfully.", "At least one Task in the Job has failed.", "The Job will be deleted, but has not been deleted yet. Typically this is because resources used by the Job are still being cleaned up.", "The Job cancellation is in progress, this is because the resources used by the Job are still being cleaned up.", "The Job has been cancelled, the task executions were stopped and the resources were cleaned up."], "type": "string"}, "newTaskState": {"description": "The new task state.", "enum": ["STATE_UNSPECIFIED", "PENDING", "ASSIGNED", "RUNNING", "FAILED", "SUCCEEDED", "UNEXECUTED"], "enumDescriptions": ["Unknown state.", "The Task is created and waiting for resources.", "The Task is assigned to at least one VM.", "The Task is running.", "The Task has failed.", "The Task has succeeded.", "The Task has not been executed when the <PERSON> finishes."], "type": "string"}, "type": {"description": "The message type.", "enum": ["TYPE_UNSPECIFIED", "JOB_STATE_CHANGED", "TASK_STATE_CHANGED"], "enumDescriptions": ["Unspecified.", "Notify users that the job state has changed.", "Notify users that the task state has changed."], "type": "string"}}, "type": "object"}, "NFS": {"description": "Represents an NFS volume.", "id": "NFS", "properties": {"remotePath": {"description": "Remote source path exported from the NFS, e.g., \"/share\".", "type": "string"}, "server": {"description": "The IP address of the NFS.", "type": "string"}}, "type": "object"}, "NetworkInterface": {"description": "A network interface.", "id": "NetworkInterface", "properties": {"network": {"description": "The URL of an existing network resource. You can specify the network as a full or partial URL. For example, the following are all valid URLs: * https://www.googleapis.com/compute/v1/projects/{project}/global/networks/{network} * projects/{project}/global/networks/{network} * global/networks/{network}", "type": "string"}, "noExternalIpAddress": {"description": "Default is false (with an external IP address). Required if no external public IP address is attached to the VM. If no external public IP address, additional configuration is required to allow the VM to access Google Services. See https://cloud.google.com/vpc/docs/configure-private-google-access and https://cloud.google.com/nat/docs/gce-example#create-nat for more information.", "type": "boolean"}, "subnetwork": {"description": "The URL of an existing subnetwork resource in the network. You can specify the subnetwork as a full or partial URL. For example, the following are all valid URLs: * https://www.googleapis.com/compute/v1/projects/{project}/regions/{region}/subnetworks/{subnetwork} * projects/{project}/regions/{region}/subnetworks/{subnetwork} * regions/{region}/subnetworks/{subnetwork}", "type": "string"}}, "type": "object"}, "NetworkPolicy": {"description": "NetworkPolicy describes VM instance network configurations.", "id": "NetworkPolicy", "properties": {"networkInterfaces": {"description": "Network configurations.", "items": {"$ref": "NetworkInterface"}, "type": "array"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "OperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "OperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have google.longrunning.Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "PlacementPolicy": {"description": "PlacementPolicy describes a group placement policy for the VMs controlled by this AllocationPolicy.", "id": "PlacementPolicy", "properties": {"collocation": {"description": "UNSPECIFIED vs. COLLOCATED (default UNSPECIFIED). Use COLLOCATED when you want VMs to be located close to each other for low network latency between the VMs. No placement policy will be generated when collocation is UNSPECIFIED.", "type": "string"}, "maxDistance": {"description": "When specified, causes the job to fail if more than max_distance logical switches are required between VMs. <PERSON><PERSON> uses the most compact possible placement of VMs even when max_distance is not specified. An explicit max_distance makes that level of compactness a strict requirement. Not yet implemented", "format": "int64", "type": "string"}}, "type": "object"}, "ReportAgentStateRequest": {"description": "Request to report agent's state. The Request itself implies the agent is healthy.", "id": "ReportAgentStateRequest", "properties": {"agentInfo": {"$ref": "AgentInfo", "description": "Agent info."}, "agentTimingInfo": {"$ref": "AgentTimingInfo", "description": "Agent timing info."}, "metadata": {"$ref": "AgentMetadata", "description": "Agent metadata."}}, "type": "object"}, "ReportAgentStateResponse": {"description": "Response to ReportAgentStateRequest.", "id": "ReportAgentStateResponse", "properties": {"defaultReportInterval": {"description": "Default report interval override", "format": "google-duration", "type": "string"}, "minReportInterval": {"description": "Minimum report interval override", "format": "google-duration", "type": "string"}, "tasks": {"description": "Tasks assigned to the agent", "items": {"$ref": "AgentTask"}, "type": "array"}, "useBatchMonitoredResource": {"description": "If true, the cloud logging for batch agent will use batch.googleapis.com/Job as monitored resource for Batch job related logging.", "type": "boolean"}}, "type": "object"}, "Runnable": {"description": "Runnable describes instructions for executing a specific script or container as part of a Task.", "id": "Runnable", "properties": {"alwaysRun": {"description": "By default, after a Runnable fails, no further Runnable are executed. This flag indicates that this Runnable must be run even if the Task has already failed. This is useful for Runnables that copy output files off of the VM or for debugging. The always_run flag does not override the Task's overall max_run_duration. If the max_run_duration has expired then no further Runnables will execute, not even always_run Runnables.", "type": "boolean"}, "background": {"description": "Normally, a runnable that doesn't exit causes its task to fail. However, you can set this field to `true` to configure a background runnable. Background runnables are allowed continue running in the background while the task executes subsequent runnables. For example, background runnables are useful for providing services to other runnables or providing debugging-support tools like SSH servers. Specifically, background runnables are killed automatically (if they have not already exited) a short time after all foreground runnables have completed. Even though this is likely to result in a non-zero exit status for the background runnable, these automatic kills are not treated as task failures.", "type": "boolean"}, "barrier": {"$ref": "Barrier", "description": "Barrier runnable."}, "container": {"$ref": "Container", "description": "Container runnable."}, "displayName": {"description": "Optional. DisplayName is an optional field that can be provided by the caller. If provided, it will be used in logs and other outputs to identify the script, making it easier for users to understand the logs. If not provided the index of the runnable will be used for outputs.", "type": "string"}, "environment": {"$ref": "Environment", "description": "Environment variables for this Runnable (overrides variables set for the whole Task or TaskGroup)."}, "ignoreExitStatus": {"description": "Normally, a runnable that returns a non-zero exit status fails and causes the task to fail. However, you can set this field to `true` to allow the task to continue executing its other runnables even if this runnable fails.", "type": "boolean"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Labels for this Runnable.", "type": "object"}, "script": {"$ref": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> runnable."}, "timeout": {"description": "Timeout for this Runnable.", "format": "google-duration", "type": "string"}}, "type": "object"}, "Script": {"description": "<PERSON><PERSON><PERSON> runnable.", "id": "<PERSON><PERSON><PERSON>", "properties": {"path": {"description": "The path to a script file that is accessible from the host VM(s). Unless the script file supports the default `#!/bin/sh` shell interpreter, you must specify an interpreter by including a [shebang line](https://en.wikipedia.org/wiki/Shebang_(Unix) as the first line of the file. For example, to execute the script using bash, include `#!/bin/bash` as the first line of the file. Alternatively, to execute the script using Python3, include `#!/usr/bin/env python3` as the first line of the file.", "type": "string"}, "text": {"description": "The text for a script. Unless the script text supports the default `#!/bin/sh` shell interpreter, you must specify an interpreter by including a [shebang line](https://en.wikipedia.org/wiki/Shebang_(Unix) at the beginning of the text. For example, to execute the script using bash, include `#!/bin/bash\\n` at the beginning of the text. Alternatively, to execute the script using Python3, include `#!/usr/bin/env python3\\n` at the beginning of the text.", "type": "string"}}, "type": "object"}, "ServiceAccount": {"description": "Carries information about a Google Cloud service account.", "id": "ServiceAccount", "properties": {"email": {"description": "Email address of the service account.", "type": "string"}, "scopes": {"description": "List of scopes to be enabled for this service account.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "StatusEvent": {"description": "Status event.", "id": "StatusEvent", "properties": {"description": {"description": "Description of the event.", "type": "string"}, "eventTime": {"description": "The time this event occurred.", "format": "google-datetime", "type": "string"}, "taskExecution": {"$ref": "TaskExecution", "description": "Task Execution. This field is only defined for task-level status events where the task fails."}, "taskState": {"description": "Task State. This field is only defined for task-level status events.", "enum": ["STATE_UNSPECIFIED", "PENDING", "ASSIGNED", "RUNNING", "FAILED", "SUCCEEDED", "UNEXECUTED"], "enumDescriptions": ["Unknown state.", "The Task is created and waiting for resources.", "The Task is assigned to at least one VM.", "The Task is running.", "The Task has failed.", "The Task has succeeded.", "The Task has not been executed when the <PERSON> finishes."], "type": "string"}, "type": {"description": "Type of the event.", "type": "string"}}, "type": "object"}, "Task": {"description": "A Cloud Batch task.", "id": "Task", "properties": {"name": {"description": "Task name. The name is generated from the parent TaskGroup name and 'id' field. For example: \"projects/123456/locations/us-west1/jobs/job01/taskGroups/group01/tasks/task01\".", "type": "string"}, "status": {"$ref": "TaskStatus", "description": "Task Status."}}, "type": "object"}, "TaskExecution": {"description": "This Task Execution field includes detail information for task execution procedures, based on StatusEvent types.", "id": "TaskExecution", "properties": {"exitCode": {"description": "The exit code of a finished task. If the task succeeded, the exit code will be 0. If the task failed but not due to the following reasons, the exit code will be 50000. Otherwise, it can be from different sources: * Batch known failures: https://cloud.google.com/batch/docs/troubleshooting#reserved-exit-codes. * Batch runnable execution failures; you can rely on Batch logs to further diagnose: https://cloud.google.com/batch/docs/analyze-job-using-logs. If there are multiple runnables failures, <PERSON><PERSON> only exposes the first error.", "format": "int32", "type": "integer"}}, "type": "object"}, "TaskGroup": {"description": "A TaskGroup defines one or more Tasks that all share the same TaskSpec.", "id": "TaskGroup", "properties": {"name": {"description": "Output only. TaskGroup name. The system generates this field based on parent Job name. For example: \"projects/123456/locations/us-west1/jobs/job01/taskGroups/group01\".", "readOnly": true, "type": "string"}, "parallelism": {"description": "Max number of tasks that can run in parallel. Default to min(task_count, parallel tasks per job limit). See: [Job Limits](https://cloud.google.com/batch/quotas#job_limits). Field parallelism must be 1 if the scheduling_policy is IN_ORDER.", "format": "int64", "type": "string"}, "permissiveSsh": {"description": "When true, <PERSON><PERSON> will configure SSH to allow passwordless login between VMs running the Batch tasks in the same TaskGroup.", "type": "boolean"}, "requireHostsFile": {"description": "When true, <PERSON><PERSON> will populate a file with a list of all VMs assigned to the TaskGroup and set the BATCH_HOSTS_FILE environment variable to the path of that file. Defaults to false. The host file supports up to 1000 VMs.", "type": "boolean"}, "runAsNonRoot": {"description": "Optional. If not set or set to false, <PERSON><PERSON> uses the root user to execute runnables. If set to true, <PERSON><PERSON> runs the runnables using a non-root user. Currently, the non-root user <PERSON><PERSON> used is generated by OS Login. For more information, see [About OS Login](https://cloud.google.com/compute/docs/oslogin).", "type": "boolean"}, "schedulingPolicy": {"description": "Scheduling policy for Tasks in the TaskGroup. The default value is AS_SOON_AS_POSSIBLE.", "enum": ["SCHEDULING_POLICY_UNSPECIFIED", "AS_SOON_AS_POSSIBLE", "IN_ORDER"], "enumDescriptions": ["Unspecified.", "Run Tasks as soon as resources are available. Tasks might be executed in parallel depending on parallelism and task_count values.", "Run Tasks sequentially with increased task index."], "type": "string"}, "taskCount": {"description": "Number of Tasks in the TaskGroup. Default is 1.", "format": "int64", "type": "string"}, "taskCountPerNode": {"description": "Max number of tasks that can be run on a VM at the same time. If not specified, the system will decide a value based on available compute resources on a VM and task requirements.", "format": "int64", "type": "string"}, "taskEnvironments": {"description": "An array of environment variable mappings, which are passed to Tasks with matching indices. If task_environments is used then task_count should not be specified in the request (and will be ignored). Task count will be the length of task_environments. Tasks get a BATCH_TASK_INDEX and BATCH_TASK_COUNT environment variable, in addition to any environment variables set in task_environments, specifying the number of Tasks in the Task's parent TaskGroup, and the specific Task's index in the TaskGroup (0 through BATCH_TASK_COUNT - 1).", "items": {"$ref": "Environment"}, "type": "array"}, "taskSpec": {"$ref": "TaskSpec", "description": "Required. Tasks in the group share the same task spec."}}, "type": "object"}, "TaskGroupStatus": {"description": "Aggregated task status for a TaskGroup.", "id": "TaskGroupStatus", "properties": {"counts": {"additionalProperties": {"format": "int64", "type": "string"}, "description": "Count of task in each state in the TaskGroup. The map key is task state name.", "type": "object"}, "instances": {"description": "Status of instances allocated for the TaskGroup.", "items": {"$ref": "InstanceStatus"}, "type": "array"}}, "type": "object"}, "TaskSpec": {"description": "Spec of a task", "id": "TaskSpec", "properties": {"computeResource": {"$ref": "ComputeResource", "description": "ComputeResource requirements."}, "environment": {"$ref": "Environment", "description": "Environment variables to set before running the Task."}, "environments": {"additionalProperties": {"type": "string"}, "deprecated": true, "description": "Deprecated: please use environment(non-plural) instead.", "type": "object"}, "lifecyclePolicies": {"description": "Lifecycle management schema when any task in a task group is failed. Currently we only support one lifecycle policy. When the lifecycle policy condition is met, the action in the policy will execute. If task execution result does not meet with the defined lifecycle policy, we consider it as the default policy. Default policy means if the exit code is 0, exit task. If task ends with non-zero exit code, retry the task with max_retry_count.", "items": {"$ref": "LifecyclePolicy"}, "type": "array"}, "maxRetryCount": {"description": "Maximum number of retries on failures. The default, 0, which means never retry. The valid value range is [0, 10].", "format": "int32", "type": "integer"}, "maxRunDuration": {"description": "Maximum duration the task should run before being automatically retried (if enabled) or automatically failed. Format the value of this field as a time limit in seconds followed by `s`—for example, `3600s` for 1 hour. The field accepts any value between 0 and the maximum listed for the `Duration` field type at https://protobuf.dev/reference/protobuf/google.protobuf/#duration; however, the actual maximum run time for a job will be limited to the maximum run time for a job listed at https://cloud.google.com/batch/quotas#max-job-duration.", "format": "google-duration", "type": "string"}, "runnables": {"description": "Required. The sequence of one or more runnables (executable scripts, executable containers, and/or barriers) for each task in this task group to run. Each task runs this list of runnables in order. For a task to succeed, all of its script and container runnables each must meet at least one of the following conditions: + The runnable exited with a zero status. + The runnable didn't finish, but you enabled its `background` subfield. + The runnable exited with a non-zero status, but you enabled its `ignore_exit_status` subfield.", "items": {"$ref": "Runnable"}, "type": "array"}, "volumes": {"description": "Volumes to mount before running Tasks using this TaskSpec.", "items": {"$ref": "Volume"}, "type": "array"}}, "type": "object"}, "TaskStatus": {"description": "Status of a task.", "id": "TaskStatus", "properties": {"state": {"description": "Task state.", "enum": ["STATE_UNSPECIFIED", "PENDING", "ASSIGNED", "RUNNING", "FAILED", "SUCCEEDED", "UNEXECUTED"], "enumDescriptions": ["Unknown state.", "The Task is created and waiting for resources.", "The Task is assigned to at least one VM.", "The Task is running.", "The Task has failed.", "The Task has succeeded.", "The Task has not been executed when the <PERSON> finishes."], "type": "string"}, "statusEvents": {"description": "Detailed info about why the state is reached.", "items": {"$ref": "StatusEvent"}, "type": "array"}}, "type": "object"}, "Volume": {"description": "Volume describes a volume and parameters for it to be mounted to a VM.", "id": "Volume", "properties": {"deviceName": {"description": "Device name of an attached disk volume, which should align with a device_name specified by job.allocation_policy.instances[0].policy.disks[i].device_name or defined by the given instance template in job.allocation_policy.instances[0].instance_template.", "type": "string"}, "gcs": {"$ref": "GCS", "description": "A Google Cloud Storage (GCS) volume."}, "mountOptions": {"description": "Mount options vary based on the type of storage volume: * For a Cloud Storage bucket, all the mount options provided by the [`gcsfuse` tool](https://cloud.google.com/storage/docs/gcsfuse-cli) are supported. * For an existing persistent disk, all mount options provided by the [`mount` command](https://man7.org/linux/man-pages/man8/mount.8.html) except writing are supported. This is due to restrictions of [multi-writer mode](https://cloud.google.com/compute/docs/disks/sharing-disks-between-vms). * For any other disk or a Network File System (NFS), all the mount options provided by the `mount` command are supported.", "items": {"type": "string"}, "type": "array"}, "mountPath": {"description": "The mount path for the volume, e.g. /mnt/disks/share.", "type": "string"}, "nfs": {"$ref": "NFS", "description": "A Network File System (NFS) volume. For example, a Filestore file share."}}, "type": "object"}}, "servicePath": "", "title": "Batch API", "version": "v1", "version_module": true}