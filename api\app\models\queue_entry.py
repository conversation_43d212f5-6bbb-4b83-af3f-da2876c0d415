"""
Queue Entry model for the QueTeToca API.
"""
from datetime import datetime, timed<PERSON>ta
from typing import Optional
from sqlalchemy import Column, String, DateTime, ForeignKey, Integer, Text
from sqlalchemy.orm import relationship
import uuid

from ..database import Base
from .enums import QueueStatus


class QueueEntry(Base):
    """Queue entry model."""
    
    __tablename__ = "queue_entries"
    
    # Primary key
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), index=True)
    
    # Bar association
    bar_id = Column(
        String(36), 
        ForeignKey("bars.id", ondelete="CASCADE"), 
        nullable=False,
        index=True
    )
    
    # Customer information
    customer_uid = Column(
        String(128), 
        ForeignKey("users.uid", ondelete="SET NULL"), 
        nullable=True,
        index=True
    )
    customer_name = Column(String(255), nullable=False)
    phone_number = Column(String(50), nullable=True)
    
    # Queue information
    party_size = Column(Integer, default=1, nullable=False)
    queue_number = Column(Integer, nullable=False, index=True)
    status = Column(String(20), default=QueueStatus.WAITING, nullable=False, index=True)
    
    # Additional information
    notes = Column(Text, nullable=True)
    estimated_wait_time = Column(Integer, nullable=True)  # minutes
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    called_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    cancelled_at = Column(DateTime, nullable=True)
    
    # Relationships
    bar = relationship("Bar", back_populates="queue_entries")
    customer = relationship("User", back_populates="queue_entries")
    
    def __repr__(self):
        return f"<QueueEntry(id='{self.id}', queue_number={self.queue_number}, status='{self.status}')>"
    
    @property
    def is_waiting(self) -> bool:
        """Check if entry is waiting."""
        return self.status == QueueStatus.WAITING
    
    @property
    def is_called(self) -> bool:
        """Check if entry has been called."""
        return self.status == QueueStatus.CALLED
    
    @property
    def is_completed(self) -> bool:
        """Check if entry is completed."""
        return self.status == QueueStatus.COMPLETED
    
    @property
    def is_cancelled(self) -> bool:
        """Check if entry is cancelled."""
        return self.status == QueueStatus.CANCELLED
    
    @property
    def is_active(self) -> bool:
        """Check if entry is active (waiting or called)."""
        return self.status in [QueueStatus.WAITING, QueueStatus.CALLED]
    
    @property
    def wait_time_minutes(self) -> Optional[int]:
        """Get actual wait time in minutes."""
        if not self.called_at:
            return None
        return int((self.called_at - self.created_at).total_seconds() / 60)
    
    @property
    def total_time_minutes(self) -> Optional[int]:
        """Get total time from creation to completion in minutes."""
        end_time = self.completed_at or self.cancelled_at
        if not end_time:
            return None
        return int((end_time - self.created_at).total_seconds() / 60)
    
    @property
    def current_wait_time_minutes(self) -> int:
        """Get current wait time in minutes."""
        return int((datetime.utcnow() - self.created_at).total_seconds() / 60)
    
    def call(self) -> None:
        """Mark entry as called."""
        if self.status == QueueStatus.WAITING:
            self.status = QueueStatus.CALLED
            self.called_at = datetime.utcnow()
    
    def complete(self) -> None:
        """Mark entry as completed."""
        if self.status in [QueueStatus.WAITING, QueueStatus.CALLED]:
            self.status = QueueStatus.COMPLETED
            self.completed_at = datetime.utcnow()
            if not self.called_at:
                self.called_at = self.completed_at
    
    def cancel(self) -> None:
        """Mark entry as cancelled."""
        if self.status in [QueueStatus.WAITING, QueueStatus.CALLED]:
            self.status = QueueStatus.CANCELLED
            self.cancelled_at = datetime.utcnow()
    
    def estimate_wait_time(self, average_service_time: int = 15) -> int:
        """Estimate wait time based on position in queue."""
        if not self.is_waiting:
            return 0
        
        # Count entries ahead in queue
        ahead_count = 0
        for entry in self.bar.queue_entries:
            if (entry.status == QueueStatus.WAITING and 
                entry.queue_number < self.queue_number and
                entry.created_at.date() == self.created_at.date()):
                ahead_count += 1
        
        return ahead_count * average_service_time
    
    def get_position_in_queue(self) -> int:
        """Get current position in queue (1-based)."""
        if not self.is_waiting:
            return 0
        
        position = 1
        for entry in self.bar.queue_entries:
            if (entry.status == QueueStatus.WAITING and 
                entry.queue_number < self.queue_number and
                entry.created_at.date() == self.created_at.date()):
                position += 1
        
        return position
