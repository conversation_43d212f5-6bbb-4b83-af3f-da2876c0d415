"""
Queue service for the QueTeToca API.
"""
import logging
from datetime import datetime, date
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, func, extract

from ..models import QueueEntry, Bar, User
from ..models.enums import QueueStatus
from ..schemas.queue import QueueEntryCreate, QueueEntryUpdate

logger = logging.getLogger(__name__)


class QueueService:
    """Service for queue operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_queue_entry_by_id(self, entry_id: str) -> Optional[QueueEntry]:
        """Get queue entry by ID."""
        return self.db.query(QueueEntry).filter(QueueEntry.id == entry_id).first()
    
    def get_bar_queue(self, bar_id: str, include_completed: bool = False) -> List[QueueEntry]:
        """Get queue entries for a bar."""
        query = self.db.query(QueueEntry).filter(QueueEntry.bar_id == bar_id)
        
        if not include_completed:
            query = query.filter(
                QueueEntry.status.in_([QueueStatus.WAITING, QueueStatus.CALLED])
            )
        
        return query.order_by(QueueEntry.queue_number).all()
    
    def get_waiting_queue(self, bar_id: str) -> List[QueueEntry]:
        """Get only waiting queue entries for a bar."""
        return self.db.query(QueueEntry).filter(
            and_(
                QueueEntry.bar_id == bar_id,
                QueueEntry.status == QueueStatus.WAITING
            )
        ).order_by(QueueEntry.queue_number).all()
    
    def join_queue(self, bar_id: str, entry_data: QueueEntryCreate, 
                   customer_uid: Optional[str] = None) -> QueueEntry:
        """Add a new entry to the queue."""
        # Get the bar
        bar = self.db.query(Bar).filter(Bar.id == bar_id).first()
        if not bar:
            raise ValueError(f"Bar with ID {bar_id} not found")
        
        if not bar.active:
            raise ValueError("Bar is not currently active")
        
        # Check if queue is full
        if bar.is_queue_full:
            raise ValueError("Queue is currently full")
        
        # Check if customer already has an active entry
        if customer_uid:
            existing_entry = self.db.query(QueueEntry).filter(
                and_(
                    QueueEntry.bar_id == bar_id,
                    QueueEntry.customer_uid == customer_uid,
                    QueueEntry.status.in_([QueueStatus.WAITING, QueueStatus.CALLED])
                )
            ).first()
            
            if existing_entry:
                raise ValueError("Customer already has an active queue entry")
        
        # Create queue entry
        queue_entry = QueueEntry(
            bar_id=bar_id,
            customer_uid=customer_uid,
            queue_number=bar.next_queue_number,
            estimated_wait_time=self._estimate_wait_time(bar_id),
            **entry_data.model_dump()
        )
        
        self.db.add(queue_entry)
        self.db.commit()
        self.db.refresh(queue_entry)
        
        logger.info(f"Customer {entry_data.customer_name} joined queue for bar {bar_id}")
        return queue_entry
    
    def call_next(self, bar_id: str) -> Optional[QueueEntry]:
        """Call the next customer in queue."""
        # Get the next waiting entry
        next_entry = self.db.query(QueueEntry).filter(
            and_(
                QueueEntry.bar_id == bar_id,
                QueueEntry.status == QueueStatus.WAITING
            )
        ).order_by(QueueEntry.queue_number).first()
        
        if not next_entry:
            return None
        
        next_entry.call()
        self.db.commit()
        self.db.refresh(next_entry)
        
        logger.info(f"Called customer {next_entry.customer_name} (#{next_entry.queue_number})")
        return next_entry
    
    def call_entry(self, entry_id: str) -> Optional[QueueEntry]:
        """Call a specific queue entry."""
        entry = self.get_queue_entry_by_id(entry_id)
        if not entry:
            return None
        
        if entry.status != QueueStatus.WAITING:
            raise ValueError(f"Cannot call entry with status {entry.status}")
        
        entry.call()
        self.db.commit()
        self.db.refresh(entry)
        
        logger.info(f"Called customer {entry.customer_name} (#{entry.queue_number})")
        return entry
    
    def complete_entry(self, entry_id: str) -> Optional[QueueEntry]:
        """Mark a queue entry as completed."""
        entry = self.get_queue_entry_by_id(entry_id)
        if not entry:
            return None
        
        if entry.status not in [QueueStatus.WAITING, QueueStatus.CALLED]:
            raise ValueError(f"Cannot complete entry with status {entry.status}")
        
        entry.complete()
        self.db.commit()
        self.db.refresh(entry)
        
        logger.info(f"Completed service for {entry.customer_name} (#{entry.queue_number})")
        return entry
    
    def cancel_entry(self, entry_id: str, reason: Optional[str] = None) -> Optional[QueueEntry]:
        """Cancel a queue entry."""
        entry = self.get_queue_entry_by_id(entry_id)
        if not entry:
            return None
        
        if entry.status not in [QueueStatus.WAITING, QueueStatus.CALLED]:
            raise ValueError(f"Cannot cancel entry with status {entry.status}")
        
        entry.cancel()
        if reason:
            entry.notes = f"{entry.notes or ''}\nCancellation reason: {reason}".strip()
        
        self.db.commit()
        self.db.refresh(entry)
        
        logger.info(f"Cancelled entry for {entry.customer_name} (#{entry.queue_number})")
        return entry
    
    def update_entry(self, entry_id: str, entry_data: QueueEntryUpdate) -> Optional[QueueEntry]:
        """Update a queue entry."""
        entry = self.get_queue_entry_by_id(entry_id)
        if not entry:
            return None
        
        # Update fields
        update_data = entry_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(entry, field, value)
        
        self.db.commit()
        self.db.refresh(entry)
        
        logger.info(f"Updated entry for {entry.customer_name} (#{entry.queue_number})")
        return entry
    
    def get_customer_entries(self, customer_uid: str, bar_id: Optional[str] = None) -> List[QueueEntry]:
        """Get queue entries for a specific customer."""
        query = self.db.query(QueueEntry).filter(QueueEntry.customer_uid == customer_uid)
        
        if bar_id:
            query = query.filter(QueueEntry.bar_id == bar_id)
        
        return query.order_by(QueueEntry.created_at.desc()).all()
    
    def get_queue_position(self, entry_id: str) -> int:
        """Get the position of an entry in the queue."""
        entry = self.get_queue_entry_by_id(entry_id)
        if not entry or entry.status != QueueStatus.WAITING:
            return 0
        
        return entry.get_position_in_queue()
    
    def _estimate_wait_time(self, bar_id: str, average_service_time: int = 15) -> int:
        """Estimate wait time for new queue entry."""
        waiting_count = self.db.query(QueueEntry).filter(
            and_(
                QueueEntry.bar_id == bar_id,
                QueueEntry.status == QueueStatus.WAITING
            )
        ).count()
        
        return waiting_count * average_service_time
    
    def get_queue_stats(self, bar_id: str, target_date: Optional[date] = None) -> Dict[str, Any]:
        """Get queue statistics for a bar on a specific date."""
        if target_date is None:
            target_date = date.today()
        
        # Base query for the date
        base_query = self.db.query(QueueEntry).filter(
            and_(
                QueueEntry.bar_id == bar_id,
                func.date(QueueEntry.created_at) == target_date
            )
        )
        
        total_entries = base_query.count()
        completed_entries = base_query.filter(QueueEntry.status == QueueStatus.COMPLETED).count()
        cancelled_entries = base_query.filter(QueueEntry.status == QueueStatus.CANCELLED).count()
        
        # Calculate average wait time for completed entries
        completed_with_times = base_query.filter(
            and_(
                QueueEntry.status == QueueStatus.COMPLETED,
                QueueEntry.called_at.isnot(None)
            )
        ).all()
        
        avg_wait_time = None
        avg_service_time = None
        
        if completed_with_times:
            wait_times = [entry.wait_time_minutes for entry in completed_with_times if entry.wait_time_minutes]
            service_times = [entry.total_time_minutes for entry in completed_with_times if entry.total_time_minutes]
            
            if wait_times:
                avg_wait_time = sum(wait_times) / len(wait_times)
            if service_times:
                avg_service_time = sum(service_times) / len(service_times)
        
        # Get hourly statistics
        hourly_stats = []
        for hour in range(24):
            hour_entries = base_query.filter(
                extract('hour', QueueEntry.created_at) == hour
            ).count()
            
            if hour_entries > 0:
                hourly_stats.append({
                    "hour": hour,
                    "entries": hour_entries
                })
        
        # Find peak hour
        peak_hour = None
        if hourly_stats:
            peak_hour = max(hourly_stats, key=lambda x: x["entries"])["hour"]
        
        return {
            "bar_id": bar_id,
            "date": target_date.isoformat(),
            "total_entries": total_entries,
            "completed_entries": completed_entries,
            "cancelled_entries": cancelled_entries,
            "average_wait_time": avg_wait_time,
            "average_service_time": avg_service_time,
            "peak_hour": peak_hour,
            "hourly_stats": hourly_stats
        }
