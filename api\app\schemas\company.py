"""
Company schemas for the QueTeToca API.
"""
from typing import Optional, List, Dict, Any
from pydantic import Field, EmailStr

from .base import BaseSchema, TimestampMixin, ResponseBase


class CompanyBase(BaseSchema):
    """Base company schema."""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    website: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    country: Optional[str] = None
    postal_code: Optional[str] = None


class CompanyCreate(CompanyBase):
    """Schema for creating a company."""
    admin_ids: List[str] = Field(default_factory=list)
    settings: Dict[str, Any] = Field(default_factory=dict)


class CompanyUpdate(BaseSchema):
    """Schema for updating a company."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    website: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    country: Optional[str] = None
    postal_code: Optional[str] = None
    settings: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None


class CompanyInDB(CompanyBase, TimestampMixin):
    """Company schema as stored in database."""
    id: str
    admin_ids: List[str]
    settings: Dict[str, Any]
    is_active: str


class CompanyResponse(CompanyInDB):
    """Company response schema."""
    active: bool
    bars_count: Optional[int] = None
    users_count: Optional[int] = None


class CompanyListResponse(ResponseBase):
    """Company list response schema."""
    companies: List[CompanyResponse]


class CompanyDetailResponse(ResponseBase):
    """Company detail response schema."""
    company: CompanyResponse


class CompanyAdminRequest(BaseSchema):
    """Schema for adding/removing company admin."""
    user_uid: str = Field(..., min_length=1)


class CompanyAdminResponse(ResponseBase):
    """Company admin operation response."""
    company_id: str
    user_uid: str
    action: str  # "added" or "removed"


class CompanySettingsUpdate(BaseSchema):
    """Schema for updating company settings."""
    settings: Dict[str, Any]


class CompanyStatsResponse(ResponseBase):
    """Company statistics response."""
    company_id: str
    total_bars: int
    total_users: int
    total_queue_entries: int
    active_queue_entries: int
    completed_queue_entries: int
    cancelled_queue_entries: int
    average_wait_time: Optional[float] = None
    peak_hours: Optional[List[int]] = None
