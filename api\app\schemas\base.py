"""
Base schemas for the QueTeToca API.
"""
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, ConfigDict


class BaseSchema(BaseModel):
    """Base schema with common configuration."""
    
    model_config = ConfigDict(
        from_attributes=True,
        validate_assignment=True,
        arbitrary_types_allowed=True,
        str_strip_whitespace=True
    )


class TimestampMixin(BaseModel):
    """Mixin for timestamp fields."""
    created_at: datetime
    updated_at: datetime


class ResponseBase(BaseSchema):
    """Base response schema."""
    success: bool = True
    message: Optional[str] = None


class ErrorResponse(BaseSchema):
    """Error response schema."""
    success: bool = False
    message: str
    error_code: Optional[str] = None
    details: Optional[dict] = None


class PaginationParams(BaseSchema):
    """Pagination parameters."""
    page: int = 1
    size: int = 20
    
    def get_offset(self) -> int:
        """Get offset for database query."""
        return (self.page - 1) * self.size


class PaginatedResponse(ResponseBase):
    """Paginated response schema."""
    page: int
    size: int
    total: int
    pages: int
    has_next: bool
    has_prev: bool
