"""
Bars router for the QueTeToca API.
"""
import logging
from typing import Annotated, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from ..database import get_db
from ..schemas.bar import (
    BarCreate,
    BarUpdate,
    BarResponse,
    BarListResponse,
    BarDetailResponse,
    QRCodeResponse,
    BarStatsResponse,
    BarSettingsUpdate
)
from ..schemas.base import PaginationParams
from ..services.bar import BarService
from ..services.dependencies import (
    get_current_active_user,
    get_current_admin_user
)
from ..models import User

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/bars", tags=["Bars"])


@router.get("", response_model=BarListResponse)
async def list_bars(
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    pagination: Annotated[PaginationParams, Depends()],
    company_id: Optional[str] = Query(None, description="Filter by company ID"),
    is_active: Optional[bool] = Query(None, description="Filter by active status")
):
    """
    List bars with optional filtering.
    
    - **Super admins** can see all bars
    - **Company admins** can see bars from their company
    - **Bar managers** can see bars from their company
    - **Customers** can see active bars only
    
    Args:
        db: Database session
        current_user: Current authenticated user
        pagination: Pagination parameters
        company_id: Optional company ID filter
        is_active: Optional active status filter
        
    Returns:
        List of bars
    """
    try:
        bar_service = BarService(db)
        
        # Apply access control
        if current_user.role.value == "CUSTOMER":
            # Customers can only see active bars
            is_active = True
            company_id = None
        elif current_user.role.value in ["BAR_MANAGER", "COMPANY_ADMIN"]:
            # Managers and company admins can only see their company's bars
            if not company_id:
                company_id = current_user.company_id
            elif company_id != current_user.company_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access to this company's bars is not allowed"
                )
        # Super admins can see all bars without restrictions
        
        bars = bar_service.get_bars(
            skip=pagination.get_offset(),
            limit=pagination.size,
            company_id=company_id,
            is_active=is_active
        )
        
        bar_responses = [BarResponse.model_validate(bar) for bar in bars]
        
        return BarListResponse(
            success=True,
            message=f"Retrieved {len(bar_responses)} bars",
            bars=bar_responses
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing bars: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving bars"
        )


@router.post("", response_model=BarDetailResponse)
async def create_bar(
    bar_data: BarCreate,
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_admin_user)]
):
    """
    Create a new bar.
    
    Only company admins and super admins can create bars.
    Company admins can only create bars for their own company.
    
    Args:
        bar_data: Bar creation data
        db: Database session
        current_user: Current authenticated admin user
        
    Returns:
        Created bar information
    """
    try:
        # Check company access
        if (current_user.role.value == "COMPANY_ADMIN" and 
            current_user.company_id != bar_data.company_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot create bar for a different company"
            )
        
        bar_service = BarService(db)
        bar = bar_service.create_bar(bar_data)
        
        bar_response = BarResponse.model_validate(bar)
        
        return BarDetailResponse(
            success=True,
            message="Bar created successfully",
            bar=bar_response
        )
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating bar: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error creating bar"
        )


@router.get("/{bar_id}", response_model=BarDetailResponse)
async def get_bar(
    bar_id: str,
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    """
    Get a specific bar by ID.
    
    Args:
        bar_id: Bar ID
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Bar information
    """
    try:
        bar_service = BarService(db)
        bar = bar_service.get_bar_by_id(bar_id)
        
        if not bar:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Bar not found"
            )
        
        # Check access permissions
        if current_user.role.value == "CUSTOMER":
            # Customers can only see active bars
            if not bar.active:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Bar not found"
                )
        elif current_user.role.value in ["BAR_MANAGER", "COMPANY_ADMIN"]:
            # Check company access
            if current_user.company_id != bar.company_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access to this bar is not allowed"
                )
        
        bar_response = BarResponse.model_validate(bar)
        
        return BarDetailResponse(
            success=True,
            message="Bar retrieved successfully",
            bar=bar_response
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting bar {bar_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving bar"
        )


@router.put("/{bar_id}", response_model=BarDetailResponse)
async def update_bar(
    bar_id: str,
    bar_data: BarUpdate,
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    """
    Update a bar.

    Only users with access to the bar's company can update it.

    Args:
        bar_id: Bar ID
        bar_data: Bar update data
        db: Database session
        current_user: Current authenticated user

    Returns:
        Updated bar information
    """
    try:
        bar_service = BarService(db)
        bar = bar_service.get_bar_by_id(bar_id)

        if not bar:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Bar not found"
            )

        # Check access permissions
        if current_user.role.value == "CUSTOMER":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to update bar"
            )
        elif current_user.role.value in ["BAR_MANAGER", "COMPANY_ADMIN"]:
            if current_user.company_id != bar.company_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access to this bar is not allowed"
                )

        updated_bar = bar_service.update_bar(bar_id, bar_data)
        bar_response = BarResponse.model_validate(updated_bar)

        return BarDetailResponse(
            success=True,
            message="Bar updated successfully",
            bar=bar_response
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating bar {bar_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error updating bar"
        )


@router.delete("/{bar_id}")
async def delete_bar(
    bar_id: str,
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_admin_user)]
):
    """
    Delete a bar.

    Only company admins and super admins can delete bars.

    Args:
        bar_id: Bar ID
        db: Database session
        current_user: Current authenticated admin user

    Returns:
        Deletion confirmation
    """
    try:
        bar_service = BarService(db)
        bar = bar_service.get_bar_by_id(bar_id)

        if not bar:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Bar not found"
            )

        # Check company access for company admins
        if (current_user.role.value == "COMPANY_ADMIN" and
            current_user.company_id != bar.company_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access to this bar is not allowed"
            )

        bar_service.delete_bar(bar_id)

        return {
            "success": True,
            "message": "Bar deleted successfully"
        }

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting bar {bar_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error deleting bar"
        )


@router.post("/{bar_id}/qr", response_model=QRCodeResponse)
async def generate_qr_code(
    bar_id: str,
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    """
    Generate or regenerate QR code for a bar.

    Args:
        bar_id: Bar ID
        db: Database session
        current_user: Current authenticated user

    Returns:
        QR code information
    """
    try:
        bar_service = BarService(db)
        bar = bar_service.get_bar_by_id(bar_id)

        if not bar:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Bar not found"
            )

        # Check access permissions
        if current_user.role.value == "CUSTOMER":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to generate QR code"
            )
        elif current_user.role.value in ["BAR_MANAGER", "COMPANY_ADMIN"]:
            if current_user.company_id != bar.company_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access to this bar is not allowed"
                )

        qr_code, qr_url = bar_service.regenerate_qr_code(bar_id)

        return QRCodeResponse(
            success=True,
            message="QR code generated successfully",
            bar_id=bar_id,
            qr_code=qr_code,
            qr_code_url=qr_url,
            download_url=f"/bars/{bar_id}/qr/download"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating QR code for bar {bar_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error generating QR code"
        )


@router.get("/{bar_id}/stats", response_model=BarStatsResponse)
async def get_bar_stats(
    bar_id: str,
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    """
    Get statistics for a bar.

    Args:
        bar_id: Bar ID
        db: Database session
        current_user: Current authenticated user

    Returns:
        Bar statistics
    """
    try:
        bar_service = BarService(db)
        bar = bar_service.get_bar_by_id(bar_id)

        if not bar:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Bar not found"
            )

        # Check access permissions
        if current_user.role.value == "CUSTOMER":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to view bar statistics"
            )
        elif current_user.role.value in ["BAR_MANAGER", "COMPANY_ADMIN"]:
            if current_user.company_id != bar.company_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access to this bar is not allowed"
                )

        stats = bar_service.get_bar_stats(bar_id)

        return BarStatsResponse(
            success=True,
            message="Bar statistics retrieved successfully",
            **stats
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting stats for bar {bar_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving bar statistics"
        )
