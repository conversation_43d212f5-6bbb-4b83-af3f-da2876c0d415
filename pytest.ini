[tool:pytest]
testpaths = api/tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --cov=api/app
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-fail-under=80
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    auth: marks tests related to authentication
    queue: marks tests related to queue functionality
    bars: marks tests related to bar management
    companies: marks tests related to company management
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
