# FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Database
sqlalchemy
alembic==1.12.1
aiosqlite==0.19.0
# Authentication
firebase-admin==6.2.0  # Optional: only needed for Firebase auth
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
email-validator==2.1.0

# Validation and serialization
pydantic-settings==2.1.0

# WebSockets
websockets==12.0

# QR Code generation
qrcode[pil]==7.4.2

# HTTP client
httpx==0.25.2

# Rate limiting
slowapi==0.1.9

# Environment variables
python-dotenv==1.0.0

# Logging
structlog==23.2.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
httpx==0.25.2

# Development
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# CORS
python-multipart==0.0.6
