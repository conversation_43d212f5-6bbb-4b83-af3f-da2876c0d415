"""
Enums for the QueTeToca API models.
"""
import enum


class QueueStatus(str, enum.Enum):
    """Queue entry status enum."""
    WAITING = "WAITING"
    CALLED = "CALLED"
    COMPLETED = "COMPLETED"
    CANCELLED = "CANCELLED"


class UserRole(str, enum.Enum):
    """User role enum."""
    SUPER_ADMIN = "SUPER_ADMIN"
    COMPANY_ADMIN = "COMPANY_ADMIN"
    BAR_MANAGER = "BAR_MANAGER"
    CUSTOMER = "CUSTOMER"
