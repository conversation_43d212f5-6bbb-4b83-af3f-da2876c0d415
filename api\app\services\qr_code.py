"""
QR Code generation service for the QueTeToca API.
"""
import base64
import io
import logging
from typing import <PERSON>ple
import qrcode
from qrcode.image.styledpil import StyledPilImage
from qrcode.image.styles.moduledrawers import RoundedModuleDrawer
from PIL import Image, ImageDraw, ImageFont

from ..config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class QRCodeService:
    """Service for generating QR codes."""
    
    def __init__(self):
        self.base_url = settings.qr_code_base_url
    
    def generate_bar_qr_code(self, bar_id: str, bar_name: str) -> Tuple[str, str]:
        """
        Generate QR code for a bar.
        
        Args:
            bar_id: Bar ID
            bar_name: Bar name for display
            
        Returns:
            Tuple of (base64_encoded_image, qr_url)
        """
        try:
            # Create the URL that the QR code will point to
            qr_url = f"{self.base_url}/{bar_id}"
            
            # Create QR code instance
            qr = qrcode.QRCode(
                version=1,  # Controls the size of the QR Code
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            
            # Add data to QR code
            qr.add_data(qr_url)
            qr.make(fit=True)
            
            # Create QR code image with styling
            img = qr.make_image(
                image_factory=StyledPilImage,
                module_drawer=RoundedModuleDrawer(),
                fill_color="black",
                back_color="white"
            )
            
            # Convert PIL image to base64
            buffer = io.BytesIO()
            img.save(buffer, format='PNG')
            img_str = base64.b64encode(buffer.getvalue()).decode()
            
            logger.info(f"QR code generated for bar {bar_id}")
            return img_str, qr_url
        
        except Exception as e:
            logger.error(f"Error generating QR code for bar {bar_id}: {e}")
            raise
    
    def generate_styled_qr_code(self, bar_id: str, bar_name: str, 
                               logo_path: str = None) -> Tuple[str, str]:
        """
        Generate a styled QR code with bar branding.
        
        Args:
            bar_id: Bar ID
            bar_name: Bar name
            logo_path: Optional path to logo image
            
        Returns:
            Tuple of (base64_encoded_image, qr_url)
        """
        try:
            qr_url = f"{self.base_url}/{bar_id}"
            
            # Create QR code
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_H,  # High error correction for logo
                box_size=10,
                border=4,
            )
            
            qr.add_data(qr_url)
            qr.make(fit=True)
            
            # Create image
            img = qr.make_image(fill_color="black", back_color="white")
            
            # Convert to RGB if needed
            if img.mode != 'RGB':
                img = img.convert('RGB')
            
            # Add logo if provided
            if logo_path:
                try:
                    logo = Image.open(logo_path)
                    # Calculate logo size (10% of QR code)
                    qr_width, qr_height = img.size
                    logo_size = min(qr_width, qr_height) // 10
                    
                    # Resize logo
                    logo = logo.resize((logo_size, logo_size), Image.Resampling.LANCZOS)
                    
                    # Calculate position (center)
                    logo_pos = ((qr_width - logo_size) // 2, (qr_height - logo_size) // 2)
                    
                    # Paste logo
                    img.paste(logo, logo_pos)
                except Exception as e:
                    logger.warning(f"Could not add logo to QR code: {e}")
            
            # Add bar name text below QR code
            img_with_text = self._add_text_to_qr(img, bar_name)
            
            # Convert to base64
            buffer = io.BytesIO()
            img_with_text.save(buffer, format='PNG')
            img_str = base64.b64encode(buffer.getvalue()).decode()
            
            logger.info(f"Styled QR code generated for bar {bar_id}")
            return img_str, qr_url
        
        except Exception as e:
            logger.error(f"Error generating styled QR code for bar {bar_id}: {e}")
            raise
    
    def _add_text_to_qr(self, qr_img: Image.Image, text: str) -> Image.Image:
        """
        Add text below QR code image.
        
        Args:
            qr_img: QR code image
            text: Text to add
            
        Returns:
            Image with text
        """
        try:
            # Calculate new image size
            qr_width, qr_height = qr_img.size
            text_height = 60
            new_height = qr_height + text_height
            
            # Create new image
            new_img = Image.new('RGB', (qr_width, new_height), 'white')
            
            # Paste QR code
            new_img.paste(qr_img, (0, 0))
            
            # Add text
            draw = ImageDraw.Draw(new_img)
            
            # Try to use a nice font, fall back to default
            try:
                font = ImageFont.truetype("arial.ttf", 24)
            except:
                font = ImageFont.load_default()
            
            # Calculate text position (centered)
            text_bbox = draw.textbbox((0, 0), text, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_x = (qr_width - text_width) // 2
            text_y = qr_height + 10
            
            # Draw text
            draw.text((text_x, text_y), text, fill='black', font=font)
            
            return new_img
        
        except Exception as e:
            logger.warning(f"Could not add text to QR code: {e}")
            return qr_img
    
    def generate_queue_entry_qr(self, bar_id: str, entry_id: str) -> Tuple[str, str]:
        """
        Generate QR code for a specific queue entry.
        
        Args:
            bar_id: Bar ID
            entry_id: Queue entry ID
            
        Returns:
            Tuple of (base64_encoded_image, qr_url)
        """
        try:
            qr_url = f"{self.base_url}/{bar_id}/entry/{entry_id}"
            
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=8,
                border=2,
            )
            
            qr.add_data(qr_url)
            qr.make(fit=True)
            
            img = qr.make_image(fill_color="black", back_color="white")
            
            # Convert to base64
            buffer = io.BytesIO()
            img.save(buffer, format='PNG')
            img_str = base64.b64encode(buffer.getvalue()).decode()
            
            logger.info(f"Queue entry QR code generated for entry {entry_id}")
            return img_str, qr_url
        
        except Exception as e:
            logger.error(f"Error generating queue entry QR code: {e}")
            raise
