"""
CORS middleware configuration for the QueTeToca API.
"""
from fastapi.middleware.cors import CORSMiddleware
from ..config import get_settings

settings = get_settings()


def add_cors_middleware(app):
    """Add CORS middleware to the FastAPI app."""
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.allowed_origins,
        allow_credentials=True,
        allow_methods=settings.allowed_methods,
        allow_headers=settings.allowed_headers,
    )
