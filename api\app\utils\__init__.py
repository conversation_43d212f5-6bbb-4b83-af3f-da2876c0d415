"""
Utilities package for the QueTeToca API.
"""
from .logging import setup_logging, get_logger, log_user_action, log_queue_event, log_security_event
from .helpers import (
    generate_uuid,
    is_valid_uuid,
    is_valid_email,
    is_valid_phone,
    format_phone,
    utc_now,
    format_datetime,
    parse_datetime,
    sanitize_string,
    calculate_distance,
    paginate_query,
    create_pagination_response,
    mask_sensitive_data,
    generate_queue_number,
    validate_queue_data,
    estimate_wait_time
)

__all__ = [
    # Logging
    "setup_logging",
    "get_logger",
    "log_user_action",
    "log_queue_event",
    "log_security_event",

    # Helpers
    "generate_uuid",
    "is_valid_uuid",
    "is_valid_email",
    "is_valid_phone",
    "format_phone",
    "utc_now",
    "format_datetime",
    "parse_datetime",
    "sanitize_string",
    "calculate_distance",
    "paginate_query",
    "create_pagination_response",
    "mask_sensitive_data",
    "generate_queue_number",
    "validate_queue_data",
    "estimate_wait_time"
]