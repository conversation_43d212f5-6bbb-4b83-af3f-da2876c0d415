"""
Helper utilities for the QueTeToca API.
"""
import re
import uuid
from datetime import datetime, timezone
from typing import Optional, Dict, Any, List
from email_validator import validate_email, EmailNotValidError


def generate_uuid() -> str:
    """Generate a new UUID string."""
    return str(uuid.uuid4())


def is_valid_uuid(uuid_string: str) -> bool:
    """Check if a string is a valid UUID."""
    try:
        uuid.UUID(uuid_string)
        return True
    except ValueError:
        return False


def is_valid_email(email: str) -> bool:
    """Check if an email address is valid."""
    try:
        validate_email(email)
        return True
    except EmailNotValidError:
        return False


def is_valid_phone(phone: str) -> bool:
    """Check if a phone number is valid (basic validation)."""
    if not phone:
        return False
    
    # Remove all non-digit characters
    digits_only = re.sub(r'\D', '', phone)
    
    # Check if it has a reasonable length (7-15 digits)
    return 7 <= len(digits_only) <= 15


def format_phone(phone: str) -> str:
    """Format a phone number to a standard format."""
    if not phone:
        return ""
    
    # Remove all non-digit characters
    digits_only = re.sub(r'\D', '', phone)
    
    # Format based on length
    if len(digits_only) == 10:
        # US format: (XXX) XXX-XXXX
        return f"({digits_only[:3]}) {digits_only[3:6]}-{digits_only[6:]}"
    elif len(digits_only) == 11 and digits_only[0] == '1':
        # US format with country code: +1 (XXX) XXX-XXXX
        return f"+1 ({digits_only[1:4]}) {digits_only[4:7]}-{digits_only[7:]}"
    else:
        # International format: +XX XXXXXXXXX
        return f"+{digits_only}"


def utc_now() -> datetime:
    """Get current UTC datetime."""
    return datetime.now(timezone.utc)


def format_datetime(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """Format datetime to string."""
    if not dt:
        return ""
    return dt.strftime(format_str)


def parse_datetime(dt_string: str, format_str: str = "%Y-%m-%d %H:%M:%S") -> Optional[datetime]:
    """Parse datetime string."""
    try:
        return datetime.strptime(dt_string, format_str)
    except ValueError:
        return None


def sanitize_string(text: str, max_length: int = None) -> str:
    """Sanitize a string by removing dangerous characters."""
    if not text:
        return ""
    
    # Remove HTML tags
    text = re.sub(r'<[^>]+>', '', text)
    
    # Remove script tags and content
    text = re.sub(r'<script.*?</script>', '', text, flags=re.DOTALL | re.IGNORECASE)
    
    # Remove potentially dangerous characters
    text = re.sub(r'[<>"\']', '', text)
    
    # Trim whitespace
    text = text.strip()
    
    # Truncate if needed
    if max_length and len(text) > max_length:
        text = text[:max_length]
    
    return text


def calculate_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
    """
    Calculate the distance between two points on Earth using Haversine formula.
    Returns distance in kilometers.
    """
    import math
    
    # Convert latitude and longitude from degrees to radians
    lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])
    
    # Haversine formula
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
    c = 2 * math.asin(math.sqrt(a))
    
    # Radius of Earth in kilometers
    r = 6371
    
    return c * r


def paginate_query(query, page: int = 1, size: int = 20):
    """
    Paginate a SQLAlchemy query.
    
    Args:
        query: SQLAlchemy query object
        page: Page number (1-based)
        size: Items per page
        
    Returns:
        Tuple of (items, total_count, has_next, has_prev)
    """
    total = query.count()
    
    # Calculate offset
    offset = (page - 1) * size
    
    # Get items for current page
    items = query.offset(offset).limit(size).all()
    
    # Calculate pagination info
    has_next = offset + size < total
    has_prev = page > 1
    
    return items, total, has_next, has_prev


def create_pagination_response(items: List, page: int, size: int, total: int) -> Dict[str, Any]:
    """Create a standardized pagination response."""
    total_pages = (total + size - 1) // size  # Ceiling division
    
    return {
        "items": items,
        "pagination": {
            "page": page,
            "size": size,
            "total": total,
            "pages": total_pages,
            "has_next": page < total_pages,
            "has_prev": page > 1
        }
    }


def mask_sensitive_data(data: str, mask_char: str = "*", visible_chars: int = 4) -> str:
    """
    Mask sensitive data like phone numbers or emails.
    
    Args:
        data: The sensitive data to mask
        mask_char: Character to use for masking
        visible_chars: Number of characters to keep visible at the end
        
    Returns:
        Masked string
    """
    if not data or len(data) <= visible_chars:
        return data
    
    masked_length = len(data) - visible_chars
    return mask_char * masked_length + data[-visible_chars:]


def generate_queue_number(bar_id: str, date: datetime = None) -> str:
    """
    Generate a unique queue number for a bar.
    Format: YYYYMMDD-XXX (where XXX is a sequential number)
    """
    if date is None:
        date = utc_now()
    
    date_str = date.strftime("%Y%m%d")
    
    # In a real implementation, you would query the database
    # to get the next sequential number for this date
    # For now, we'll use a simple timestamp-based approach
    time_part = date.strftime("%H%M%S")
    
    return f"{date_str}-{time_part}"


def validate_queue_data(customer_name: str, party_size: int, phone_number: str = None) -> List[str]:
    """
    Validate queue entry data and return list of errors.
    
    Args:
        customer_name: Customer name
        party_size: Size of the party
        phone_number: Optional phone number
        
    Returns:
        List of validation error messages
    """
    errors = []
    
    # Validate customer name
    if not customer_name or not customer_name.strip():
        errors.append("Customer name is required")
    elif len(customer_name.strip()) < 2:
        errors.append("Customer name must be at least 2 characters")
    elif len(customer_name.strip()) > 100:
        errors.append("Customer name must be less than 100 characters")
    
    # Validate party size
    if party_size < 1:
        errors.append("Party size must be at least 1")
    elif party_size > 20:
        errors.append("Party size cannot exceed 20")
    
    # Validate phone number if provided
    if phone_number and not is_valid_phone(phone_number):
        errors.append("Invalid phone number format")
    
    return errors


def estimate_wait_time(position_in_queue: int, average_service_time: int = 15) -> int:
    """
    Estimate wait time based on queue position.
    
    Args:
        position_in_queue: Position in the queue (1-based)
        average_service_time: Average service time per customer in minutes
        
    Returns:
        Estimated wait time in minutes
    """
    if position_in_queue <= 1:
        return 0
    
    return (position_in_queue - 1) * average_service_time
