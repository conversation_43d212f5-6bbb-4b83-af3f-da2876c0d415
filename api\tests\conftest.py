"""
Test configuration and fixtures for the QueTeToca API tests.
"""
import pytest
import asyncio
from typing import As<PERSON><PERSON><PERSON>ator, Generator
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from api.app.main import app
from api.app.database import get_db, Base
from api.app.config import get_settings
from api.app.models import User, Company, Bar, QueueEntry
from api.app.models.enums import UserRole, QueueStatus

# Test database URL
TEST_DATABASE_URL = "sqlite:///./test.db"

# Create test engine
engine = create_engine(
    TEST_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)

TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """Override database dependency for testing."""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


# Override the dependency
app.dependency_overrides[get_db] = override_get_db


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def db_session():
    """Create a fresh database session for each test."""
    # Create tables
    Base.metadata.create_all(bind=engine)
    
    # Create session
    session = TestingSessionLocal()
    
    try:
        yield session
    finally:
        session.close()
        # Drop tables after test
        Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def client(db_session) -> Generator[TestClient, None, None]:
    """Create a test client."""
    with TestClient(app) as test_client:
        yield test_client


@pytest.fixture
def test_user(db_session) -> User:
    """Create a test user."""
    user = User(
        uid="test-user-uid",
        email="<EMAIL>",
        display_name="Test User",
        role=UserRole.CUSTOMER
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def test_admin_user(db_session) -> User:
    """Create a test admin user."""
    user = User(
        uid="test-admin-uid",
        email="<EMAIL>",
        display_name="Test Admin",
        role=UserRole.SUPER_ADMIN
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def test_company(db_session) -> Company:
    """Create a test company."""
    company = Company(
        name="Test Company",
        description="A test company",
        admin_ids=["test-admin-uid"]
    )
    db_session.add(company)
    db_session.commit()
    db_session.refresh(company)
    return company


@pytest.fixture
def test_company_admin(db_session, test_company) -> User:
    """Create a test company admin user."""
    user = User(
        uid="test-company-admin-uid",
        email="<EMAIL>",
        display_name="Test Company Admin",
        role=UserRole.COMPANY_ADMIN,
        company_id=test_company.id
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def test_bar(db_session, test_company) -> Bar:
    """Create a test bar."""
    bar = Bar(
        name="Test Bar",
        description="A test bar",
        address="123 Test Street",
        company_id=test_company.id,
        max_queue_size=50,
        estimated_wait_time=15
    )
    db_session.add(bar)
    db_session.commit()
    db_session.refresh(bar)
    return bar


@pytest.fixture
def test_queue_entry(db_session, test_bar, test_user) -> QueueEntry:
    """Create a test queue entry."""
    entry = QueueEntry(
        bar_id=test_bar.id,
        customer_uid=test_user.uid,
        customer_name="Test Customer",
        phone_number="+1234567890",
        party_size=2,
        queue_number=1,
        status=QueueStatus.WAITING
    )
    db_session.add(entry)
    db_session.commit()
    db_session.refresh(entry)
    return entry


@pytest.fixture
def auth_headers(test_user) -> dict:
    """Create authentication headers for test user."""
    # In a real test, you would generate a proper JWT token
    # For now, we'll mock it
    return {
        "Authorization": "Bearer test-token"
    }


@pytest.fixture
def admin_auth_headers(test_admin_user) -> dict:
    """Create authentication headers for admin user."""
    return {
        "Authorization": "Bearer admin-test-token"
    }


@pytest.fixture
def company_admin_auth_headers(test_company_admin) -> dict:
    """Create authentication headers for company admin user."""
    return {
        "Authorization": "Bearer company-admin-test-token"
    }


# Mock Firebase token verification for tests
@pytest.fixture(autouse=True)
def mock_firebase_auth(monkeypatch):
    """Mock Firebase authentication for tests."""
    async def mock_verify_firebase_token(token: str):
        if token == "test-token":
            return {
                "uid": "test-user-uid",
                "email": "<EMAIL>",
                "name": "Test User"
            }
        elif token == "admin-test-token":
            return {
                "uid": "test-admin-uid",
                "email": "<EMAIL>",
                "name": "Test Admin"
            }
        elif token == "company-admin-test-token":
            return {
                "uid": "test-company-admin-uid",
                "email": "<EMAIL>",
                "name": "Test Company Admin"
            }
        return None
    
    def mock_get_current_user(db, token: str):
        if token == "test-token":
            return db.query(User).filter(User.uid == "test-user-uid").first()
        elif token == "admin-test-token":
            return db.query(User).filter(User.uid == "test-admin-uid").first()
        elif token == "company-admin-test-token":
            return db.query(User).filter(User.uid == "test-company-admin-uid").first()
        return None
    
    # Mock the auth functions
    monkeypatch.setattr("api.app.services.auth.verify_firebase_token", mock_verify_firebase_token)
    monkeypatch.setattr("api.app.services.auth.get_current_user", mock_get_current_user)


# Test data factories
class TestDataFactory:
    """Factory for creating test data."""
    
    @staticmethod
    def create_user_data(role: UserRole = UserRole.CUSTOMER, company_id: str = None) -> dict:
        """Create user data for testing."""
        return {
            "uid": f"test-uid-{role.value}",
            "email": f"test-{role.value}@example.com",
            "display_name": f"Test {role.value}",
            "role": role,
            "company_id": company_id
        }
    
    @staticmethod
    def create_company_data() -> dict:
        """Create company data for testing."""
        return {
            "name": "Test Company",
            "description": "A test company",
            "email": "<EMAIL>",
            "phone": "+1234567890",
            "address": "123 Company Street",
            "city": "Test City",
            "state": "Test State",
            "country": "Test Country"
        }
    
    @staticmethod
    def create_bar_data(company_id: str) -> dict:
        """Create bar data for testing."""
        return {
            "name": "Test Bar",
            "description": "A test bar",
            "address": "123 Bar Street",
            "city": "Test City",
            "company_id": company_id,
            "max_queue_size": 50,
            "estimated_wait_time": 15
        }
    
    @staticmethod
    def create_queue_entry_data() -> dict:
        """Create queue entry data for testing."""
        return {
            "customer_name": "Test Customer",
            "phone_number": "+1234567890",
            "party_size": 2,
            "notes": "Test notes"
        }


@pytest.fixture
def test_data_factory():
    """Provide test data factory."""
    return TestDataFactory
