"""
Tests for bar endpoints.
"""
import pytest
from fastapi.testclient import TestClient


class TestBarEndpoints:
    """Test bar endpoints."""
    
    def test_list_bars_customer(self, client: TestClient, test_bar, auth_headers):
        """Test listing bars as a customer."""
        response = client.get(
            "/bars",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert len(data["bars"]) >= 0  # Customers can see active bars
    
    def test_list_bars_admin(self, client: TestClient, test_bar, admin_auth_headers):
        """Test listing bars as an admin."""
        response = client.get(
            "/bars",
            headers=admin_auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert len(data["bars"]) >= 0
    
    def test_list_bars_unauthorized(self, client: TestClient):
        """Test listing bars without authentication."""
        response = client.get("/bars")
        
        assert response.status_code == 401
    
    def test_create_bar_success(self, client: TestClient, test_company, admin_auth_headers, test_data_factory):
        """Test creating a bar successfully."""
        bar_data = test_data_factory.create_bar_data(test_company.id)
        
        response = client.post(
            "/bars",
            json=bar_data,
            headers=admin_auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["bar"]["name"] == bar_data["name"]
        assert data["bar"]["company_id"] == test_company.id
    
    def test_create_bar_unauthorized(self, client: TestClient, test_company, auth_headers, test_data_factory):
        """Test creating a bar without admin privileges."""
        bar_data = test_data_factory.create_bar_data(test_company.id)
        
        response = client.post(
            "/bars",
            json=bar_data,
            headers=auth_headers
        )
        
        assert response.status_code == 403
    
    def test_create_bar_invalid_company(self, client: TestClient, admin_auth_headers, test_data_factory):
        """Test creating a bar with invalid company ID."""
        bar_data = test_data_factory.create_bar_data("invalid-company-id")
        
        response = client.post(
            "/bars",
            json=bar_data,
            headers=admin_auth_headers
        )
        
        assert response.status_code == 400
    
    def test_get_bar_success(self, client: TestClient, test_bar, auth_headers):
        """Test getting a specific bar."""
        response = client.get(
            f"/bars/{test_bar.id}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["bar"]["id"] == test_bar.id
        assert data["bar"]["name"] == test_bar.name
    
    def test_get_bar_not_found(self, client: TestClient, auth_headers):
        """Test getting a non-existent bar."""
        response = client.get(
            "/bars/non-existent-id",
            headers=auth_headers
        )
        
        assert response.status_code == 404
    
    def test_update_bar_success(self, client: TestClient, test_bar, company_admin_auth_headers):
        """Test updating a bar."""
        update_data = {
            "name": "Updated Bar Name",
            "description": "Updated description"
        }
        
        response = client.put(
            f"/bars/{test_bar.id}",
            json=update_data,
            headers=company_admin_auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["bar"]["name"] == update_data["name"]
        assert data["bar"]["description"] == update_data["description"]
    
    def test_update_bar_unauthorized(self, client: TestClient, test_bar, auth_headers):
        """Test updating a bar without proper permissions."""
        update_data = {"name": "Updated Bar Name"}
        
        response = client.put(
            f"/bars/{test_bar.id}",
            json=update_data,
            headers=auth_headers
        )
        
        assert response.status_code == 403
    
    def test_delete_bar_success(self, client: TestClient, test_bar, admin_auth_headers):
        """Test deleting a bar."""
        response = client.delete(
            f"/bars/{test_bar.id}",
            headers=admin_auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
    
    def test_delete_bar_unauthorized(self, client: TestClient, test_bar, auth_headers):
        """Test deleting a bar without admin privileges."""
        response = client.delete(
            f"/bars/{test_bar.id}",
            headers=auth_headers
        )
        
        assert response.status_code == 403
    
    def test_generate_qr_code(self, client: TestClient, test_bar, company_admin_auth_headers):
        """Test generating QR code for a bar."""
        response = client.post(
            f"/bars/{test_bar.id}/qr",
            headers=company_admin_auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "qr_code" in data
        assert "qr_code_url" in data
        assert data["bar_id"] == test_bar.id
    
    def test_generate_qr_code_unauthorized(self, client: TestClient, test_bar, auth_headers):
        """Test generating QR code without proper permissions."""
        response = client.post(
            f"/bars/{test_bar.id}/qr",
            headers=auth_headers
        )
        
        assert response.status_code == 403
    
    def test_get_bar_stats(self, client: TestClient, test_bar, company_admin_auth_headers):
        """Test getting bar statistics."""
        response = client.get(
            f"/bars/{test_bar.id}/stats",
            headers=company_admin_auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "bar_id" in data
        assert "current_queue_size" in data
        assert "total_served_today" in data
    
    def test_get_bar_stats_unauthorized(self, client: TestClient, test_bar, auth_headers):
        """Test getting bar statistics without proper permissions."""
        response = client.get(
            f"/bars/{test_bar.id}/stats",
            headers=auth_headers
        )
        
        assert response.status_code == 403


class TestBarValidation:
    """Test bar data validation."""
    
    def test_create_bar_missing_required_fields(self, client: TestClient, admin_auth_headers):
        """Test creating a bar with missing required fields."""
        response = client.post(
            "/bars",
            json={},
            headers=admin_auth_headers
        )
        
        assert response.status_code == 422  # Validation error
    
    def test_create_bar_invalid_data_types(self, client: TestClient, test_company, admin_auth_headers):
        """Test creating a bar with invalid data types."""
        bar_data = {
            "name": "Test Bar",
            "address": "123 Test Street",
            "company_id": test_company.id,
            "max_queue_size": "invalid",  # Should be integer
            "estimated_wait_time": "invalid"  # Should be integer
        }
        
        response = client.post(
            "/bars",
            json=bar_data,
            headers=admin_auth_headers
        )
        
        assert response.status_code == 422  # Validation error
