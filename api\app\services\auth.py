"""
Authentication service for the QueTeToca API.
"""
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import firebase_admin
from firebase_admin import auth, credentials
from jose import JW<PERSON>rror, jwt
from sqlalchemy.orm import Session

from ..config import get_settings
from ..models import User
from ..models.enums import UserRole
from ..schemas.user import UserCreate

logger = logging.getLogger(__name__)
settings = get_settings()

# Firebase app instance
firebase_app = None


def initialize_firebase():
    """Initialize Firebase Admin SDK."""
    global firebase_app
    
    if firebase_app is not None:
        return firebase_app
    
    try:
        if settings.firebase_credentials_path:
            # Initialize with service account key file
            cred = credentials.Certificate(settings.firebase_credentials_path)
            firebase_app = firebase_admin.initialize_app(cred)
        else:
            # Initialize with default credentials (for Cloud Run, etc.)
            firebase_app = firebase_admin.initialize_app()
        
        logger.info("Firebase Admin SDK initialized successfully")
        return firebase_app
    
    except Exception as e:
        logger.error(f"Failed to initialize Firebase: {e}")
        raise


async def verify_firebase_token(token: str) -> Optional[Dict[str, Any]]:
    """
    Verify Firebase ID token and return user claims.
    
    Args:
        token: Firebase ID token
        
    Returns:
        User claims if token is valid, None otherwise
    """
    try:
        if firebase_app is None:
            initialize_firebase()
        
        # Verify the token
        decoded_token = auth.verify_id_token(token)
        
        logger.info(f"Firebase token verified for user: {decoded_token.get('uid')}")
        return decoded_token
    
    except auth.InvalidIdTokenError:
        logger.warning("Invalid Firebase ID token")
        return None
    except auth.ExpiredIdTokenError:
        logger.warning("Expired Firebase ID token")
        return None
    except Exception as e:
        logger.error(f"Error verifying Firebase token: {e}")
        return None


def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """
    Create JWT access token.
    
    Args:
        data: Data to encode in the token
        expires_delta: Token expiration time
        
    Returns:
        Encoded JWT token
    """
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)
    
    to_encode.update({"exp": expire})
    
    encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
    return encoded_jwt


def verify_access_token(token: str) -> Optional[Dict[str, Any]]:
    """
    Verify JWT access token.
    
    Args:
        token: JWT access token
        
    Returns:
        Token payload if valid, None otherwise
    """
    try:
        payload = jwt.decode(token, settings.secret_key, algorithms=[settings.algorithm])
        return payload
    except JWTError:
        return None


async def get_or_create_user(db: Session, firebase_claims: Dict[str, Any]) -> User:
    """
    Get existing user or create new user from Firebase claims.
    
    Args:
        db: Database session
        firebase_claims: Firebase user claims
        
    Returns:
        User instance
    """
    uid = firebase_claims.get("uid")
    email = firebase_claims.get("email")
    
    if not uid or not email:
        raise ValueError("Invalid Firebase claims: missing uid or email")
    
    # Try to get existing user
    user = db.query(User).filter(User.uid == uid).first()
    
    if user:
        # Update last login
        user.update_last_login()
        db.commit()
        logger.info(f"Existing user logged in: {user.email}")
        return user
    
    # Create new user
    user_data = UserCreate(
        uid=uid,
        email=email,
        display_name=firebase_claims.get("name"),
        photo_url=firebase_claims.get("picture"),
        role=UserRole.CUSTOMER
    )
    
    user = User(**user_data.model_dump())
    user.update_last_login()
    
    db.add(user)
    db.commit()
    db.refresh(user)
    
    logger.info(f"New user created: {user.email}")
    return user


def get_current_user(db: Session, token: str) -> Optional[User]:
    """
    Get current user from JWT token.
    
    Args:
        db: Database session
        token: JWT access token
        
    Returns:
        User instance if token is valid, None otherwise
    """
    payload = verify_access_token(token)
    if not payload:
        return None
    
    uid = payload.get("sub")
    if not uid:
        return None
    
    user = db.query(User).filter(User.uid == uid).first()
    return user


def check_user_permissions(user: User, required_role: UserRole = None, 
                         company_id: str = None, bar_id: str = None) -> bool:
    """
    Check if user has required permissions.
    
    Args:
        user: User instance
        required_role: Required user role
        company_id: Required company ID
        bar_id: Required bar ID (will check company association)
        
    Returns:
        True if user has permissions, False otherwise
    """
    # Super admin can do anything
    if user.role == UserRole.SUPER_ADMIN:
        return True
    
    # Check role requirement
    if required_role and user.role != required_role:
        # Allow higher roles
        role_hierarchy = {
            UserRole.CUSTOMER: 0,
            UserRole.BAR_MANAGER: 1,
            UserRole.COMPANY_ADMIN: 2,
            UserRole.SUPER_ADMIN: 3
        }
        
        user_level = role_hierarchy.get(user.role, 0)
        required_level = role_hierarchy.get(required_role, 0)
        
        if user_level < required_level:
            return False
    
    # Check company association
    if company_id and user.company_id != company_id:
        return False
    
    # Check bar association (through company)
    if bar_id:
        from ..models import Bar
        # This would need to be passed in or queried
        # For now, assume it's handled at the router level
        pass
    
    return True
