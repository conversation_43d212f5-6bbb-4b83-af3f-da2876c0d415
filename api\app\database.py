"""
Database configuration and session management.
"""
import logging
from typing import Async<PERSON>enerator
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool

from .config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()

# Create the SQLAlchemy base class
Base = declarative_base()

# Metadata for migrations
metadata = MetaData()

# Database engines
engine = None
async_engine = None
SessionLocal = None
AsyncSessionLocal = None


def get_database_url(async_mode: bool = False) -> str:
    """Get the appropriate database URL."""
    url = settings.database_url
    
    if async_mode:
        if url.startswith("sqlite"):
            return url.replace("sqlite://", "sqlite+aiosqlite://")
        elif url.startswith("postgresql"):
            return url.replace("postgresql://", "postgresql+asyncpg://")
    
    return url


def create_database_engine():
    """Create database engines and session factories."""
    global engine, async_engine, SessionLocal, AsyncSessionLocal
    
    # Synchronous engine
    sync_url = get_database_url(async_mode=False)
    
    if sync_url.startswith("sqlite"):
        engine = create_engine(
            sync_url,
            connect_args={"check_same_thread": False},
            poolclass=StaticPool,
            echo=settings.debug
        )
    else:
        engine = create_engine(
            sync_url,
            echo=settings.debug
        )
    
    SessionLocal = sessionmaker(
        autocommit=False,
        autoflush=False,
        bind=engine
    )
    
    # Asynchronous engine
    async_url = get_database_url(async_mode=True)
    
    if async_url.startswith("sqlite"):
        async_engine = create_async_engine(
            async_url,
            connect_args={"check_same_thread": False},
            poolclass=StaticPool,
            echo=settings.debug
        )
    else:
        async_engine = create_async_engine(
            async_url,
            echo=settings.debug
        )
    
    AsyncSessionLocal = async_sessionmaker(
        async_engine,
        class_=AsyncSession,
        expire_on_commit=False
    )
    
    logger.info(f"Database engines created for: {sync_url}")


def get_db() -> Session:
    """
    Dependency to get synchronous database session.
    """
    if SessionLocal is None:
        create_database_engine()
    
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency to get asynchronous database session.
    """
    if AsyncSessionLocal is None:
        create_database_engine()
    
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()


async def create_tables():
    """Create all database tables."""
    if async_engine is None:
        create_database_engine()
    
    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    logger.info("Database tables created successfully")


async def drop_tables():
    """Drop all database tables."""
    if async_engine is None:
        create_database_engine()
    
    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
    
    logger.info("Database tables dropped successfully")


async def init_database():
    """Initialize the database."""
    try:
        await create_tables()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise


async def close_database():
    """Close database connections."""
    global engine, async_engine
    
    if async_engine:
        await async_engine.dispose()
        logger.info("Async database engine disposed")
    
    if engine:
        engine.dispose()
        logger.info("Sync database engine disposed")


# Initialize engines on import
create_database_engine()
