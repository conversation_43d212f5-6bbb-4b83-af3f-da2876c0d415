"""
Company model for the QueTeToca API.
"""
from datetime import datetime
from typing import List, Optional
from sqlalchemy import Column, String, DateTime, Text, JSON
from sqlalchemy.orm import relationship
import uuid

from ..database import Base


class Company(Base):
    """Company model."""
    
    __tablename__ = "companies"
    
    # Primary key
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), index=True)
    
    # Basic company information
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    
    # Contact information
    email = Column(String(255), nullable=True)
    phone = Column(String(50), nullable=True)
    website = Column(String(255), nullable=True)
    
    # Address information
    address = Column(Text, nullable=True)
    city = Column(String(100), nullable=True)
    state = Column(String(100), nullable=True)
    country = Column(String(100), nullable=True)
    postal_code = Column(String(20), nullable=True)
    
    # Admin user IDs (Firebase UIDs)
    admin_ids = Column(JSON, default=list, nullable=False)
    
    # Settings
    settings = Column(JSON, default=dict, nullable=False)
    
    # Status
    is_active = Column(String(10), default="true", nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    users = relationship("User", back_populates="company")
    bars = relationship("Bar", back_populates="company", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Company(id='{self.id}', name='{self.name}')>"
    
    @property
    def active(self) -> bool:
        """Check if company is active."""
        return self.is_active == "true"
    
    def add_admin(self, user_uid: str) -> None:
        """Add an admin to the company."""
        if user_uid not in self.admin_ids:
            self.admin_ids = self.admin_ids + [user_uid]
    
    def remove_admin(self, user_uid: str) -> None:
        """Remove an admin from the company."""
        if user_uid in self.admin_ids:
            admin_list = list(self.admin_ids)
            admin_list.remove(user_uid)
            self.admin_ids = admin_list
    
    def is_admin(self, user_uid: str) -> bool:
        """Check if user is an admin of this company."""
        return user_uid in self.admin_ids
    
    def get_setting(self, key: str, default=None):
        """Get a company setting."""
        return self.settings.get(key, default)
    
    def set_setting(self, key: str, value) -> None:
        """Set a company setting."""
        settings_dict = dict(self.settings) if self.settings else {}
        settings_dict[key] = value
        self.settings = settings_dict
    
    def activate(self) -> None:
        """Activate the company."""
        self.is_active = "true"
    
    def deactivate(self) -> None:
        """Deactivate the company."""
        self.is_active = "false"
