{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://networkconnectivity.googleapis.com/", "batchPath": "batch", "canonicalName": "networkconnectivity", "description": "This API enables connectivity with and between Google Cloud resources.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/network-connectivity/docs/reference/networkconnectivity/rest", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "networkconnectivity:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://networkconnectivity.mtls.googleapis.com/", "name": "networkconnectivity", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"methods": {"get": {"description": "Gets information about a location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for the location.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Location"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1/projects/{projectsId}/locations", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.list", "parameterOrder": ["name"], "parameters": {"extraLocationTypes": {"description": "Optional. Do not use this field. It is unsupported and is ignored unless explicitly documented otherwise. This is primarily for internal usage.", "location": "query", "repeated": true, "type": "string"}, "filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like `\"displayName=tokyo\"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).", "location": "query", "type": "string"}, "name": {"description": "The resource that owns the locations collection, if applicable.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}}, "path": "v1/{+name}/locations", "response": {"$ref": "ListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"global": {"resources": {"hubs": {"methods": {"acceptSpoke": {"description": "Accepts a proposal to attach a Network Connectivity Center spoke to a hub.", "flatPath": "v1/projects/{projectsId}/locations/global/hubs/{hubsId}:acceptSpoke", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.global.hubs.acceptSpoke", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the hub into which to accept the spoke.", "location": "path", "pattern": "^projects/[^/]+/locations/global/hubs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:acceptSpoke", "request": {"$ref": "AcceptHubSpokeRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "acceptSpokeUpdate": {"description": "Accepts a proposal to update a Network Connectivity Center spoke in a hub.", "flatPath": "v1/projects/{projectsId}/locations/global/hubs/{hubsId}:acceptSpokeUpdate", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.global.hubs.acceptSpokeUpdate", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the hub to accept spoke update.", "location": "path", "pattern": "^projects/[^/]+/locations/global/hubs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:acceptSpokeUpdate", "request": {"$ref": "AcceptSpokeUpdateRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Creates a new Network Connectivity Center hub in the specified project.", "flatPath": "v1/projects/{projectsId}/locations/global/hubs", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.global.hubs.create", "parameterOrder": ["parent"], "parameters": {"hubId": {"description": "Required. A unique identifier for the hub.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/global$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check to see whether the original operation was received. If it was, the server ignores the second request. This behavior prevents clients from mistakenly creating duplicate commitments. The request ID must be a valid UUID, with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/hubs", "request": {"$ref": "<PERSON><PERSON>"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a Network Connectivity Center hub.", "flatPath": "v1/projects/{projectsId}/locations/global/hubs/{hubsId}", "httpMethod": "DELETE", "id": "networkconnectivity.projects.locations.global.hubs.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the hub to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/global/hubs/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check to see whether the original operation was received. If it was, the server ignores the second request. This behavior prevents clients from mistakenly creating duplicate commitments. The request ID must be a valid UUID, with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details about a Network Connectivity Center hub.", "flatPath": "v1/projects/{projectsId}/locations/global/hubs/{hubsId}", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.global.hubs.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the hub resource to get.", "location": "path", "pattern": "^projects/[^/]+/locations/global/hubs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "<PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/global/hubs/{hubsId}:getIamPolicy", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.global.hubs.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/global/hubs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists the Network Connectivity Center hubs associated with a given project.", "flatPath": "v1/projects/{projectsId}/locations/global/hubs", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.global.hubs.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "An expression that filters the list of results.", "location": "query", "type": "string"}, "orderBy": {"description": "Sort the results by a certain order.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of results per page to return.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource's name.", "location": "path", "pattern": "^projects/[^/]+/locations/global$", "required": true, "type": "string"}}, "path": "v1/{+parent}/hubs", "response": {"$ref": "ListHubsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "listSpokes": {"description": "Lists the Network Connectivity Center spokes associated with a specified hub and location. The list includes both spokes that are attached to the hub and spokes that have been proposed but not yet accepted.", "flatPath": "v1/projects/{projectsId}/locations/global/hubs/{hubsId}:listSpokes", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.global.hubs.listSpokes", "parameterOrder": ["name"], "parameters": {"filter": {"description": "An expression that filters the list of results.", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the hub.", "location": "path", "pattern": "^projects/[^/]+/locations/global/hubs/[^/]+$", "required": true, "type": "string"}, "orderBy": {"description": "Sort the results by name or create_time.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of results to return per page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The page token.", "location": "query", "type": "string"}, "spokeLocations": {"description": "A list of locations. Specify one of the following: `[global]`, a single region (for example, `[us-central1]`), or a combination of values (for example, `[global, us-central1, us-west1]`). If the spoke_locations field is populated, the list of results includes only spokes in the specified location. If the spoke_locations field is not populated, the list of results includes spokes in all locations.", "location": "query", "repeated": true, "type": "string"}, "view": {"description": "The view of the spoke to return. The view that you use determines which spoke fields are included in the response.", "enum": ["SPOKE_VIEW_UNSPECIFIED", "BASIC", "DETAILED"], "enumDescriptions": ["The spoke view is unspecified. When the spoke view is unspecified, the API returns the same fields as the `BASIC` view.", "Includes `name`, `create_time`, `hub`, `unique_id`, `state`, `reasons`, and `spoke_type`. This is the default value.", "Includes all spoke fields except `labels`. You can use the `DETAILED` view only when you set the `spoke_locations` field to `[global]`."], "location": "query", "type": "string"}}, "path": "v1/{+name}:listSpokes", "response": {"$ref": "ListHubSpokesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the description and/or labels of a Network Connectivity Center hub.", "flatPath": "v1/projects/{projectsId}/locations/global/hubs/{hubsId}", "httpMethod": "PATCH", "id": "networkconnectivity.projects.locations.global.hubs.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. The name of the hub. Hub names must be unique. They use the following form: `projects/{project_number}/locations/global/hubs/{hub_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/global/hubs/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check to see whether the original operation was received. If it was, the server ignores the second request. This behavior prevents clients from mistakenly creating duplicate commitments. The request ID must be a valid UUID, with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. In the case of an update to an existing hub, field mask is used to specify the fields to be overwritten. The fields specified in the update_mask are relative to the resource, not the full request. A field is overwritten if it is in the mask. If the user does not provide a mask, then all fields are overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "<PERSON><PERSON>"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "queryStatus": {"description": "Query the Private Service Connect propagation status of a Network Connectivity Center hub.", "flatPath": "v1/projects/{projectsId}/locations/global/hubs/{hubsId}:queryStatus", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.global.hubs.queryStatus", "parameterOrder": ["name"], "parameters": {"filter": {"description": "Optional. An expression that filters the list of results. The filter can be used to filter the results by the following fields: * `psc_propagation_status.source_spoke` * `psc_propagation_status.source_group` * `psc_propagation_status.source_forwarding_rule` * `psc_propagation_status.target_spoke` * `psc_propagation_status.target_group` * `psc_propagation_status.code` * `psc_propagation_status.message`", "location": "query", "type": "string"}, "groupBy": {"description": "Optional. Aggregate the results by the specified fields. A comma-separated list of any of these fields: * `psc_propagation_status.source_spoke` * `psc_propagation_status.source_group` * `psc_propagation_status.source_forwarding_rule` * `psc_propagation_status.target_spoke` * `psc_propagation_status.target_group` * `psc_propagation_status.code`", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the hub.", "location": "path", "pattern": "^projects/[^/]+/locations/global/hubs/[^/]+$", "required": true, "type": "string"}, "orderBy": {"description": "Optional. Sort the results in ascending order by the specified fields. A comma-separated list of any of these fields: * `psc_propagation_status.source_spoke` * `psc_propagation_status.source_group` * `psc_propagation_status.source_forwarding_rule` * `psc_propagation_status.target_spoke` * `psc_propagation_status.target_group` * `psc_propagation_status.code` If `group_by` is set, the value of the `order_by` field must be the same as or a subset of the `group_by` field.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of results to return per page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}:queryStatus", "response": {"$ref": "QueryHubStatusResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "rejectSpoke": {"description": "Rejects a Network Connectivity Center spoke from being attached to a hub. If the spoke was previously in the `ACTIVE` state, it transitions to the `INACTIVE` state and is no longer able to connect to other spokes that are attached to the hub.", "flatPath": "v1/projects/{projectsId}/locations/global/hubs/{hubsId}:rejectSpoke", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.global.hubs.rejectSpoke", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the hub from which to reject the spoke.", "location": "path", "pattern": "^projects/[^/]+/locations/global/hubs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:rejectSpoke", "request": {"$ref": "RejectHubSpokeRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "rejectSpokeUpdate": {"description": "Rejects a proposal to update a Network Connectivity Center spoke in a hub.", "flatPath": "v1/projects/{projectsId}/locations/global/hubs/{hubsId}:rejectSpokeUpdate", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.global.hubs.rejectSpokeUpdate", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the hub to reject spoke update.", "location": "path", "pattern": "^projects/[^/]+/locations/global/hubs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:rejectSpokeUpdate", "request": {"$ref": "RejectSpokeUpdateRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/global/hubs/{hubsId}:setIamPolicy", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.global.hubs.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/global/hubs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/global/hubs/{hubsId}:testIamPermissions", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.global.hubs.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/global/hubs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"groups": {"methods": {"get": {"description": "Gets details about a Network Connectivity Center group.", "flatPath": "v1/projects/{projectsId}/locations/global/hubs/{hubsId}/groups/{groupsId}", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.global.hubs.groups.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the route table resource.", "location": "path", "pattern": "^projects/[^/]+/locations/global/hubs/[^/]+/groups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Group"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/global/hubs/{hubsId}/groups/{groupsId}:getIamPolicy", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.global.hubs.groups.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/global/hubs/[^/]+/groups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists groups in a given hub.", "flatPath": "v1/projects/{projectsId}/locations/global/hubs/{hubsId}/groups", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.global.hubs.groups.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "An expression that filters the list of results.", "location": "query", "type": "string"}, "orderBy": {"description": "Sort the results by a certain order.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of results to return per page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource's name.", "location": "path", "pattern": "^projects/[^/]+/locations/global/hubs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/groups", "response": {"$ref": "ListGroupsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a Network Connectivity Center group.", "flatPath": "v1/projects/{projectsId}/locations/global/hubs/{hubsId}/groups/{groupsId}", "httpMethod": "PATCH", "id": "networkconnectivity.projects.locations.global.hubs.groups.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. The name of the group. Group names must be unique. They use the following form: `projects/{project_number}/locations/global/hubs/{hub}/groups/{group_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/global/hubs/[^/]+/groups/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check to see whether the original operation was received. If it was, the server ignores the second request. This behavior prevents clients from mistakenly creating duplicate commitments. The request ID must be a valid UUID, with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. In the case of an update to an existing group, field mask is used to specify the fields to be overwritten. The fields specified in the update_mask are relative to the resource, not the full request. A field is overwritten if it is in the mask. If the user does not provide a mask, then all fields are overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Group"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/global/hubs/{hubsId}/groups/{groupsId}:setIamPolicy", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.global.hubs.groups.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/global/hubs/[^/]+/groups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/global/hubs/{hubsId}/groups/{groupsId}:testIamPermissions", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.global.hubs.groups.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/global/hubs/[^/]+/groups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "routeTables": {"methods": {"get": {"description": "Gets details about a Network Connectivity Center route table.", "flatPath": "v1/projects/{projectsId}/locations/global/hubs/{hubsId}/routeTables/{routeTablesId}", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.global.hubs.routeTables.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the route table resource.", "location": "path", "pattern": "^projects/[^/]+/locations/global/hubs/[^/]+/routeTables/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "RouteTable"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists route tables in a given hub.", "flatPath": "v1/projects/{projectsId}/locations/global/hubs/{hubsId}/routeTables", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.global.hubs.routeTables.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "An expression that filters the list of results.", "location": "query", "type": "string"}, "orderBy": {"description": "Sort the results by a certain order.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of results to return per page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource's name.", "location": "path", "pattern": "^projects/[^/]+/locations/global/hubs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/routeTables", "response": {"$ref": "ListRouteTablesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"routes": {"methods": {"get": {"description": "Gets details about the specified route.", "flatPath": "v1/projects/{projectsId}/locations/global/hubs/{hubsId}/routeTables/{routeTablesId}/routes/{routesId}", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.global.hubs.routeTables.routes.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the route resource.", "location": "path", "pattern": "^projects/[^/]+/locations/global/hubs/[^/]+/routeTables/[^/]+/routes/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Route"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists routes in a given route table.", "flatPath": "v1/projects/{projectsId}/locations/global/hubs/{hubsId}/routeTables/{routeTablesId}/routes", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.global.hubs.routeTables.routes.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "An expression that filters the list of results.", "location": "query", "type": "string"}, "orderBy": {"description": "Sort the results by a certain order.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of results to return per page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource's name.", "location": "path", "pattern": "^projects/[^/]+/locations/global/hubs/[^/]+/routeTables/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/routes", "response": {"$ref": "ListRoutesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}, "policyBasedRoutes": {"methods": {"create": {"description": "Creates a new policy-based route in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/global/policyBasedRoutes", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.global.policyBasedRoutes.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource's name of the PolicyBasedRoute.", "location": "path", "pattern": "^projects/[^/]+/locations/global$", "required": true, "type": "string"}, "policyBasedRouteId": {"description": "Required. Unique id for the policy-based route to create. Provided by the client when the resource is created. The name must comply with https://google.aip.dev/122#resource-id-segments. Specifically, the name must be 1-63 characters long and match the regular expression [a-z]([a-z0-9-]*[a-z0-9])?. The first character must be a lowercase letter, and all following characters (except for the last character) must be a dash, lowercase letter, or digit. The last character must be a lowercase letter or digit.", "location": "query", "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/policyBasedRoutes", "request": {"$ref": "PolicyBasedRoute"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single policy-based route.", "flatPath": "v1/projects/{projectsId}/locations/global/policyBasedRoutes/{policyBasedRoutesId}", "httpMethod": "DELETE", "id": "networkconnectivity.projects.locations.global.policyBasedRoutes.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the policy-based route resource to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/global/policyBasedRoutes/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single policy-based route.", "flatPath": "v1/projects/{projectsId}/locations/global/policyBasedRoutes/{policyBasedRoutesId}", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.global.policyBasedRoutes.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the PolicyBasedRoute resource to get.", "location": "path", "pattern": "^projects/[^/]+/locations/global/policyBasedRoutes/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "PolicyBasedRoute"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/global/policyBasedRoutes/{policyBasedRoutesId}:getIamPolicy", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.global.policyBasedRoutes.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/global/policyBasedRoutes/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists policy-based routes in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/global/policyBasedRoutes", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.global.policyBasedRoutes.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter expression that filters the results listed in the response.", "location": "query", "type": "string"}, "orderBy": {"description": "Sort the results by a certain order.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of results per page that should be returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource's name.", "location": "path", "pattern": "^projects/[^/]+/locations/global$", "required": true, "type": "string"}}, "path": "v1/{+parent}/policyBasedRoutes", "response": {"$ref": "ListPolicyBasedRoutesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/global/policyBasedRoutes/{policyBasedRoutesId}:setIamPolicy", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.global.policyBasedRoutes.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/global/policyBasedRoutes/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/global/policyBasedRoutes/{policyBasedRoutesId}:testIamPermissions", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.global.policyBasedRoutes.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/global/policyBasedRoutes/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "internalRanges": {"methods": {"create": {"description": "Creates a new internal range in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/internalRanges", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.internalRanges.create", "parameterOrder": ["parent"], "parameters": {"internalRangeId": {"description": "Optional. Resource ID (i.e. 'foo' in '[...]/projects/p/locations/l/internalRanges/foo') See https://google.aip.dev/122#resource-id-segments Unique per location.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource's name of the internal range.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/internalRanges", "request": {"$ref": "InternalRange"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single internal range.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/internalRanges/{internalRangesId}", "httpMethod": "DELETE", "id": "networkconnectivity.projects.locations.internalRanges.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the internal range to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/internalRanges/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single internal range.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/internalRanges/{internalRangesId}", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.internalRanges.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the InternalRange to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/internalRanges/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "InternalRange"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/internalRanges/{internalRangesId}:getIamPolicy", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.internalRanges.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/internalRanges/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists internal ranges in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/internalRanges", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.internalRanges.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter expression that filters the results listed in the response.", "location": "query", "type": "string"}, "orderBy": {"description": "Sort the results by a certain order.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of results per page that should be returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource's name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/internalRanges", "response": {"$ref": "ListInternalRangesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single internal range.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/internalRanges/{internalRangesId}", "httpMethod": "PATCH", "id": "networkconnectivity.projects.locations.internalRanges.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The name of an internal range. Format: projects/{project}/locations/{location}/internalRanges/{internal_range} See: https://google.aip.dev/122#fields-representing-resource-names", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/internalRanges/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the InternalRange resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "InternalRange"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/internalRanges/{internalRangesId}:setIamPolicy", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.internalRanges.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/internalRanges/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/internalRanges/{internalRangesId}:testIamPermissions", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.internalRanges.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/internalRanges/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "multicloudDataTransferConfigs": {"methods": {"create": {"description": "Creates a `MulticloudDataTransferConfig` resource in a specified project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/multicloudDataTransferConfigs", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.multicloudDataTransferConfigs.create", "parameterOrder": ["parent"], "parameters": {"multicloudDataTransferConfigId": {"description": "Required. The ID to use for the `MulticloudDataTransferConfig` resource, which becomes the final component of the `MulticloudDataTransferConfig` resource name.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server can ignore the request if it has already been completed. The server waits for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, can ignore the second request. This prevents clients from accidentally creating duplicate `MulticloudDataTransferConfig` resources. The request ID must be a valid UUID with the exception that zero UUID (00000000-0000-0000-0000-000000000000) isn't supported.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/multicloudDataTransferConfigs", "request": {"$ref": "MulticloudDataTransferConfig"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a `MulticloudDataTransferConfig` resource.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/multicloudDataTransferConfigs/{multicloudDataTransferConfigsId}", "httpMethod": "DELETE", "id": "networkconnectivity.projects.locations.multicloudDataTransferConfigs.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "Optional. The etag is computed by the server, and might be sent with update and delete requests so that the client has an up-to-date value before proceeding.", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the `MulticloudDataTransferConfig` resource to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/multicloudDataTransferConfigs/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server can ignore the request if it has already been completed. The server waits for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, can ignore the second request. This prevents clients from accidentally creating duplicate `MulticloudDataTransferConfig` resources. The request ID must be a valid UUID with the exception that zero UUID (00000000-0000-0000-0000-000000000000) isn't supported.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the details of a `MulticloudDataTransferConfig` resource.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/multicloudDataTransferConfigs/{multicloudDataTransferConfigsId}", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.multicloudDataTransferConfigs.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the `MulticloudDataTransferConfig` resource to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/multicloudDataTransferConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "MulticloudDataTransferConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists the `MulticloudDataTransferConfig` resources in a specified project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/multicloudDataTransferConfigs", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.multicloudDataTransferConfigs.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. An expression that filters the results listed in the response.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. The sort order of the results.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of results listed per page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "returnPartialSuccess": {"description": "Optional. If `true`, allows partial responses for multi-regional aggregated list requests.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/multicloudDataTransferConfigs", "response": {"$ref": "ListMulticloudDataTransferConfigsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a `MulticloudDataTransferConfig` resource in a specified project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/multicloudDataTransferConfigs/{multicloudDataTransferConfigsId}", "httpMethod": "PATCH", "id": "networkconnectivity.projects.locations.multicloudDataTransferConfigs.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The name of the `MulticloudDataTransferConfig` resource. Format: `projects/{project}/locations/{location}/multicloudDataTransferConfigs/{multicloud_data_transfer_config}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/multicloudDataTransferConfigs/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server can ignore the request if it has already been completed. The server waits for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, can ignore the second request. This prevents clients from accidentally creating duplicate `MulticloudDataTransferConfig` resources. The request ID must be a valid UUID with the exception that zero UUID (00000000-0000-0000-0000-000000000000) isn't supported.", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. `FieldMask` is used to specify the fields in the `MulticloudDataTransferConfig` resource to be overwritten by the update. The fields specified in `update_mask` are relative to the resource, not the full request. A field is overwritten if it is in the mask. If you don't specify a mask, all fields are overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "MulticloudDataTransferConfig"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"destinations": {"methods": {"create": {"description": "Creates a `Destination` resource in a specified project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/multicloudDataTransferConfigs/{multicloudDataTransferConfigsId}/destinations", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.multicloudDataTransferConfigs.destinations.create", "parameterOrder": ["parent"], "parameters": {"destinationId": {"description": "Required. The ID to use for the `Destination` resource, which becomes the final component of the `Destination` resource name.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/multicloudDataTransferConfigs/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server can ignore the request if it has already been completed. The server waits for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, can ignore the second request. This prevents clients from accidentally creating duplicate `Destination` resources. The request ID must be a valid UUID with the exception that zero UUID (00000000-0000-0000-0000-000000000000) isn't supported.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/destinations", "request": {"$ref": "Destination"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a `Destination` resource.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/multicloudDataTransferConfigs/{multicloudDataTransferConfigsId}/destinations/{destinationsId}", "httpMethod": "DELETE", "id": "networkconnectivity.projects.locations.multicloudDataTransferConfigs.destinations.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "Optional. The etag is computed by the server, and might be sent with update and delete requests so that the client has an up-to-date value before proceeding.", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the `Destination` resource to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/multicloudDataTransferConfigs/[^/]+/destinations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server can ignore the request if it has already been completed. The server waits for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, can ignore the second request. The request ID must be a valid UUID with the exception that zero UUID (00000000-0000-0000-0000-000000000000) isn't supported.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the details of a `Destination` resource.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/multicloudDataTransferConfigs/{multicloudDataTransferConfigsId}/destinations/{destinationsId}", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.multicloudDataTransferConfigs.destinations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the `Destination` resource to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/multicloudDataTransferConfigs/[^/]+/destinations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Destination"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists the `Destination` resources in a specified project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/multicloudDataTransferConfigs/{multicloudDataTransferConfigsId}/destinations", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.multicloudDataTransferConfigs.destinations.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. An expression that filters the results listed in the response.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. The sort order of the results.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of results listed per page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/multicloudDataTransferConfigs/[^/]+$", "required": true, "type": "string"}, "returnPartialSuccess": {"description": "Optional. If `true`, allow partial responses for multi-regional aggregated list requests.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/destinations", "response": {"$ref": "ListDestinationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a `Destination` resource in a specified project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/multicloudDataTransferConfigs/{multicloudDataTransferConfigsId}/destinations/{destinationsId}", "httpMethod": "PATCH", "id": "networkconnectivity.projects.locations.multicloudDataTransferConfigs.destinations.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The name of the `Destination` resource. Format: `projects/{project}/locations/{location}/multicloudDataTransferConfigs/{multicloud_data_transfer_config}/destinations/{destination}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/multicloudDataTransferConfigs/[^/]+/destinations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server can ignore the request if it has already been completed. The server waits for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, can ignore the second request. The request ID must be a valid UUID with the exception that zero UUID (00000000-0000-0000-0000-000000000000) isn't supported.", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. `FieldMask is used to specify the fields to be overwritten in the `Destination` resource by the update. The fields specified in `update_mask` are relative to the resource, not the full request. A field is overwritten if it is in the mask. If you don't specify a mask, all fields are overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Destination"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "multicloudDataTransferSupportedServices": {"methods": {"get": {"description": "Gets the details of a service that is supported for Data Transfer Essentials.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/multicloudDataTransferSupportedServices/{multicloudDataTransferSupportedServicesId}", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.multicloudDataTransferSupportedServices.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the service.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/multicloudDataTransferSupportedServices/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "MulticloudDataTransferSupportedService"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists the services in the project for a region that are supported for Data Transfer Essentials.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/multicloudDataTransferSupportedServices", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.multicloudDataTransferSupportedServices.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of results listed per page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/multicloudDataTransferSupportedServices", "response": {"$ref": "ListMulticloudDataTransferSupportedServicesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "request": {"$ref": "GoogleLongrunningCancelOperationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "networkconnectivity.projects.locations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/operations", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "regionalEndpoints": {"methods": {"create": {"description": "Creates a new RegionalEndpoint in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/regionalEndpoints", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.regionalEndpoints.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource's name of the RegionalEndpoint.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "regionalEndpointId": {"description": "Required. Unique id of the Regional Endpoint to be created. @pattern: ^[-a-z0-9](?:[-a-z0-9]{0,44})[a-z0-9]$", "location": "query", "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/regionalEndpoints", "request": {"$ref": "RegionalEndpoint"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single RegionalEndpoint.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/regionalEndpoints/{regionalEndpointsId}", "httpMethod": "DELETE", "id": "networkconnectivity.projects.locations.regionalEndpoints.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the RegionalEndpoint to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/regionalEndpoints/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single RegionalEndpoint.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/regionalEndpoints/{regionalEndpointsId}", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.regionalEndpoints.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the RegionalEndpoint resource to get. Format: `projects/{project}/locations/{location}/regionalEndpoints/{regional_endpoint}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/regionalEndpoints/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "RegionalEndpoint"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists RegionalEndpoints in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/regionalEndpoints", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.regionalEndpoints.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter expression that filters the results listed in the response.", "location": "query", "type": "string"}, "orderBy": {"description": "Sort the results by a certain order.", "location": "query", "type": "string"}, "pageSize": {"description": "Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource's name of the RegionalEndpoint.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/regionalEndpoints", "response": {"$ref": "ListRegionalEndpointsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "remoteTransportProfiles": {"methods": {"get": {"description": "Gets details of a single RemoteTransportProfile.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/remoteTransportProfiles/{remoteTransportProfilesId}", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.remoteTransportProfiles.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/remoteTransportProfiles/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "RemoteTransportProfile"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists RemoteTransportProfiles in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/remoteTransportProfiles", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.remoteTransportProfiles.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filtering results", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint for how to order the results", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent value for ListRemoteTransportProfilesRequest", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/remoteTransportProfiles", "response": {"$ref": "ListRemoteTransportProfilesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "serviceClasses": {"methods": {"delete": {"description": "Deletes a single ServiceClass.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceClasses/{serviceClassesId}", "httpMethod": "DELETE", "id": "networkconnectivity.projects.locations.serviceClasses.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "Optional. The etag is computed by the server, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the ServiceClass to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serviceClasses/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single ServiceClass.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceClasses/{serviceClassesId}", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.serviceClasses.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the ServiceClass to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serviceClasses/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ServiceClass"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceClasses/{serviceClassesId}:getIamPolicy", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.serviceClasses.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serviceClasses/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists ServiceClasses in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceClasses", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.serviceClasses.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter expression that filters the results listed in the response.", "location": "query", "type": "string"}, "orderBy": {"description": "Sort the results by a certain order.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of results per page that should be returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource's name. ex. projects/123/locations/us-east1", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/serviceClasses", "response": {"$ref": "ListServiceClassesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single ServiceClass.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceClasses/{serviceClassesId}", "httpMethod": "PATCH", "id": "networkconnectivity.projects.locations.serviceClasses.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. The name of a ServiceClass resource. Format: projects/{project}/locations/{location}/serviceClasses/{service_class} See: https://google.aip.dev/122#fields-representing-resource-names", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serviceClasses/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the ServiceClass resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "ServiceClass"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceClasses/{serviceClassesId}:setIamPolicy", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.serviceClasses.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serviceClasses/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceClasses/{serviceClassesId}:testIamPermissions", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.serviceClasses.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serviceClasses/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "serviceConnectionMaps": {"methods": {"create": {"description": "Creates a new ServiceConnectionMap in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionMaps", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.serviceConnectionMaps.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource's name of the ServiceConnectionMap. ex. projects/123/locations/us-east1", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "serviceConnectionMapId": {"description": "Optional. Resource ID (i.e. 'foo' in '[...]/projects/p/locations/l/serviceConnectionMaps/foo') See https://google.aip.dev/122#resource-id-segments Unique per location. If one is not provided, one will be generated.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/serviceConnectionMaps", "request": {"$ref": "ServiceConnectionMap"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single ServiceConnectionMap.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionMaps/{serviceConnectionMapsId}", "httpMethod": "DELETE", "id": "networkconnectivity.projects.locations.serviceConnectionMaps.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "Optional. The etag is computed by the server, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the ServiceConnectionMap to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serviceConnectionMaps/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single ServiceConnectionMap.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionMaps/{serviceConnectionMapsId}", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.serviceConnectionMaps.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the ServiceConnectionMap to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serviceConnectionMaps/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ServiceConnectionMap"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionMaps/{serviceConnectionMapsId}:getIamPolicy", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.serviceConnectionMaps.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serviceConnectionMaps/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists ServiceConnectionMaps in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionMaps", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.serviceConnectionMaps.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter expression that filters the results listed in the response.", "location": "query", "type": "string"}, "orderBy": {"description": "Sort the results by a certain order.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of results per page that should be returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource's name. ex. projects/123/locations/us-east1", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/serviceConnectionMaps", "response": {"$ref": "ListServiceConnectionMapsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single ServiceConnectionMap.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionMaps/{serviceConnectionMapsId}", "httpMethod": "PATCH", "id": "networkconnectivity.projects.locations.serviceConnectionMaps.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. The name of a ServiceConnectionMap. Format: projects/{project}/locations/{location}/serviceConnectionMaps/{service_connection_map} See: https://google.aip.dev/122#fields-representing-resource-names", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serviceConnectionMaps/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the ServiceConnectionMap resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "ServiceConnectionMap"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionMaps/{serviceConnectionMapsId}:setIamPolicy", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.serviceConnectionMaps.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serviceConnectionMaps/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionMaps/{serviceConnectionMapsId}:testIamPermissions", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.serviceConnectionMaps.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serviceConnectionMaps/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "serviceConnectionPolicies": {"methods": {"create": {"description": "Creates a new ServiceConnectionPolicy in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionPolicies", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.serviceConnectionPolicies.create", "parameterOrder": ["parent"], "parameters": {"autoSubnetworkConfig.allocRangeSpace": {"description": "Optional. The space where we search for a free range to create a subnetwork. It can be narrow down or pick a different space. This is in standard CIDR format. If not specified, “10.0.0.0/8” is used. Only eligible for IPV4_ONLY and IPV4_IPV6 subnetwork.", "location": "query", "type": "string"}, "autoSubnetworkConfig.ipStack": {"description": "Optional. The requested IP stack for the subnetwork. If not specified, IPv4 is used.", "enum": ["SUBNET_IP_STACK_UNSPECIFIED", "IPV4_ONLY", "IPV6_ONLY", "IPV4_IPV6"], "enumDescriptions": ["Default value. Will create an IPV4_ONLY subnetwork by default.", "Will create an IPV4_ONLY subnetwork.", "Will create an IPV6_ONLY subnetwork.", "Will use IPv4 and IPv6 (dual stack)."], "location": "query", "type": "string"}, "autoSubnetworkConfig.prefixLength": {"description": "Optional. The desired prefix length for the subnet's IP address range. E.g., 24 for a /24. The actual range is allocated from available space. If not specified, 24 is used. Only eligible for IPV4_ONLY and IPV4_IPV6 subnetwork.", "format": "int32", "location": "query", "type": "integer"}, "parent": {"description": "Required. The parent resource's name of the ServiceConnectionPolicy. ex. projects/123/locations/us-east1", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "serviceConnectionPolicyId": {"description": "Optional. Resource ID (i.e. 'foo' in '[...]/projects/p/locations/l/serviceConnectionPolicies/foo') See https://google.aip.dev/122#resource-id-segments Unique per location.", "location": "query", "type": "string"}, "subnetworkMode": {"description": "Optional. If this field is not set, USER_PROVIDED is the inferred value to use.", "enum": ["SUBNETWORK_MODE_UNSPECIFIED", "USER_PROVIDED", "AUTO_CREATED"], "enumDescriptions": ["The default value if the enum is unset. Note user is not allowed to set the subnetwork mode to this value.", "Subnetworks are provided from the user input.", "Subnetwork list is empty from the user input. A subnetwork will be created automatically."], "location": "query", "type": "string"}}, "path": "v1/{+parent}/serviceConnectionPolicies", "request": {"$ref": "ServiceConnectionPolicy"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single ServiceConnectionPolicy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionPolicies/{serviceConnectionPoliciesId}", "httpMethod": "DELETE", "id": "networkconnectivity.projects.locations.serviceConnectionPolicies.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "Optional. The etag is computed by the server, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the ServiceConnectionPolicy to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serviceConnectionPolicies/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single ServiceConnectionPolicy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionPolicies/{serviceConnectionPoliciesId}", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.serviceConnectionPolicies.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the ServiceConnectionPolicy to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serviceConnectionPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ServiceConnectionPolicy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionPolicies/{serviceConnectionPoliciesId}:getIamPolicy", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.serviceConnectionPolicies.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serviceConnectionPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists ServiceConnectionPolicies in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionPolicies", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.serviceConnectionPolicies.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter expression that filters the results listed in the response.", "location": "query", "type": "string"}, "orderBy": {"description": "Sort the results by a certain order.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of results per page that should be returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource's name. ex. projects/123/locations/us-east1", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/serviceConnectionPolicies", "response": {"$ref": "ListServiceConnectionPoliciesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single ServiceConnectionPolicy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionPolicies/{serviceConnectionPoliciesId}", "httpMethod": "PATCH", "id": "networkconnectivity.projects.locations.serviceConnectionPolicies.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. The name of a ServiceConnectionPolicy. Format: projects/{project}/locations/{location}/serviceConnectionPolicies/{service_connection_policy} See: https://google.aip.dev/122#fields-representing-resource-names", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serviceConnectionPolicies/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the ServiceConnectionPolicy resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "ServiceConnectionPolicy"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionPolicies/{serviceConnectionPoliciesId}:setIamPolicy", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.serviceConnectionPolicies.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serviceConnectionPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionPolicies/{serviceConnectionPoliciesId}:testIamPermissions", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.serviceConnectionPolicies.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serviceConnectionPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "serviceConnectionTokens": {"methods": {"create": {"description": "Creates a new ServiceConnectionToken in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionTokens", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.serviceConnectionTokens.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource's name of the ServiceConnectionToken. ex. projects/123/locations/us-east1", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "serviceConnectionTokenId": {"description": "Optional. Resource ID (i.e. 'foo' in '[...]/projects/p/locations/l/ServiceConnectionTokens/foo') See https://google.aip.dev/122#resource-id-segments Unique per location. If one is not provided, one will be generated.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/serviceConnectionTokens", "request": {"$ref": "ServiceConnectionToken"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single ServiceConnectionToken.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionTokens/{serviceConnectionTokensId}", "httpMethod": "DELETE", "id": "networkconnectivity.projects.locations.serviceConnectionTokens.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "Optional. The etag is computed by the server, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the ServiceConnectionToken to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serviceConnectionTokens/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single ServiceConnectionToken.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionTokens/{serviceConnectionTokensId}", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.serviceConnectionTokens.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the ServiceConnectionToken to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serviceConnectionTokens/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ServiceConnectionToken"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists ServiceConnectionTokens in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionTokens", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.serviceConnectionTokens.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter expression that filters the results listed in the response.", "location": "query", "type": "string"}, "orderBy": {"description": "Sort the results by a certain order.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of results per page that should be returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource's name. ex. projects/123/locations/us-east1", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/serviceConnectionTokens", "response": {"$ref": "ListServiceConnectionTokensResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "spokes": {"methods": {"create": {"description": "Creates a Network Connectivity Center spoke.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/spokes", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.spokes.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check to see whether the original operation was received. If it was, the server ignores the second request. This behavior prevents clients from mistakenly creating duplicate commitments. The request ID must be a valid UUID, with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "spokeId": {"description": "Required. Unique id for the spoke to create.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/spokes", "request": {"$ref": "Spoke"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a Network Connectivity Center spoke.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/spokes/{spokesId}", "httpMethod": "DELETE", "id": "networkconnectivity.projects.locations.spokes.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the spoke to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/spokes/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check to see whether the original operation was received. If it was, the server ignores the second request. This behavior prevents clients from mistakenly creating duplicate commitments. The request ID must be a valid UUID, with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details about a Network Connectivity Center spoke.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/spokes/{spokesId}", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.spokes.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the spoke resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/spokes/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Spoke"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/spokes/{spokesId}:getIamPolicy", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.spokes.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/spokes/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists the Network Connectivity Center spokes in a specified project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/spokes", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.spokes.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "An expression that filters the list of results.", "location": "query", "type": "string"}, "orderBy": {"description": "Sort the results by a certain order.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of results to return per page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/spokes", "response": {"$ref": "ListSpokesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a Network Connectivity Center spoke.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/spokes/{spokesId}", "httpMethod": "PATCH", "id": "networkconnectivity.projects.locations.spokes.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. The name of the spoke. Spoke names must be unique. They use the following form: `projects/{project_number}/locations/{region}/spokes/{spoke_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/spokes/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check to see whether the original operation was received. If it was, the server ignores the second request. This behavior prevents clients from mistakenly creating duplicate commitments. The request ID must be a valid UUID, with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. In the case of an update to an existing spoke, field mask is used to specify the fields to be overwritten. The fields specified in the update_mask are relative to the resource, not the full request. A field is overwritten if it is in the mask. If the user does not provide a mask, then all fields are overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Spoke"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/spokes/{spokesId}:setIamPolicy", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.spokes.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/spokes/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/spokes/{spokesId}:testIamPermissions", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.spokes.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/spokes/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "transports": {"methods": {"create": {"description": "Creates a new Transport in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/transports", "httpMethod": "POST", "id": "networkconnectivity.projects.locations.transports.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Value for parent.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "transportId": {"description": "Required. Id of the requesting object", "location": "query", "type": "string"}}, "path": "v1/{+parent}/transports", "request": {"$ref": "Transport"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single Transport.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/transports/{transportsId}", "httpMethod": "DELETE", "id": "networkconnectivity.projects.locations.transports.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/transports/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single Transport.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/transports/{transportsId}", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.transports.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/transports/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Transport"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Transports in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/transports", "httpMethod": "GET", "id": "networkconnectivity.projects.locations.transports.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filtering results", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint for how to order the results", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent value for ListTransportsRequest", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/transports", "response": {"$ref": "ListTransportsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single Transport.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/transports/{transportsId}", "httpMethod": "PATCH", "id": "networkconnectivity.projects.locations.transports.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. name of resource", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/transports/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the Transport resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields present in the request will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Transport"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "20250829", "rootUrl": "https://networkconnectivity.googleapis.com/", "schemas": {"AcceptHubSpokeRequest": {"description": "The request for HubService.AcceptHubSpoke.", "id": "AcceptHubSpokeRequest", "properties": {"requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check to see whether the original operation was received. If it was, the server ignores the second request. This behavior prevents clients from mistakenly creating duplicate commitments. The request ID must be a valid UUID, with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}, "spokeUri": {"description": "Required. The URI of the spoke to accept into the hub.", "type": "string"}}, "type": "object"}, "AcceptHubSpokeResponse": {"description": "The response for HubService.AcceptHubSpoke.", "id": "AcceptHubSpokeResponse", "properties": {"spoke": {"$ref": "Spoke", "description": "The spoke that was operated on."}}, "type": "object"}, "AcceptSpokeUpdateRequest": {"description": "The request for HubService.AcceptSpokeUpdate.", "id": "AcceptSpokeUpdateRequest", "properties": {"requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check to see whether the original operation was received. If it was, the server ignores the second request. This behavior prevents clients from mistakenly creating duplicate commitments. The request ID must be a valid UUID, with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}, "spokeEtag": {"description": "Required. The etag of the spoke to accept update.", "type": "string"}, "spokeUri": {"description": "Required. The URI of the spoke to accept update.", "type": "string"}}, "type": "object"}, "AllocationOptions": {"description": "Range auto-allocation options, to be optionally used when CIDR block is not explicitly set.", "id": "AllocationOptions", "properties": {"allocationStrategy": {"description": "Optional. Allocation strategy Not setting this field when the allocation is requested means an implementation defined strategy is used.", "enum": ["ALLOCATION_STRATEGY_UNSPECIFIED", "RANDOM", "FIRST_AVAILABLE", "RANDOM_FIRST_N_AVAILABLE", "FIRST_SMALLEST_FITTING"], "enumDescriptions": ["Unspecified is the only valid option when the range is specified explicitly by ip_cidr_range field. Otherwise unspefified means using the default strategy.", "Random strategy, the legacy algorithm, used for backwards compatibility. This allocation strategy remains efficient in the case of concurrent allocation requests in the same peered network space and doesn't require providing the level of concurrency in an explicit parameter, but it is prone to fragmenting available address space.", "Pick the first available address range. This strategy is deterministic and the result is easy to predict.", "Pick an arbitrary range out of the first N available ones. The N will be set in the first_available_ranges_lookup_size field. This strategy should be used when concurrent allocation requests are made in the same space of peered networks while the fragmentation of the addrress space is reduced.", "Pick the smallest but fitting available range. This deterministic strategy minimizes fragmentation of the address space."], "type": "string"}, "firstAvailableRangesLookupSize": {"description": "Optional. This field must be set only when allocation_strategy is set to RANDOM_FIRST_N_AVAILABLE. The value should be the maximum expected parallelism of range creation requests issued to the same space of peered netwroks.", "format": "int32", "type": "integer"}}, "type": "object"}, "AuditConfig": {"description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.", "id": "AuditConfig", "properties": {"auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "items": {"$ref": "AuditLogConfig"}, "type": "array"}, "service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}}, "type": "object"}, "AuditLogConfig": {"description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "id": "AuditLogConfig", "properties": {"exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "items": {"type": "string"}, "type": "array"}, "logType": {"description": "The log type that this config enables.", "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"], "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "type": "string"}}, "type": "object"}, "AutoAccept": {"description": "The auto-accept setting for a group controls whether proposed spokes are automatically attached to the hub. If auto-accept is enabled, the spoke immediately is attached to the hub and becomes part of the group. In this case, the new spoke is in the ACTIVE state. If auto-accept is disabled, the spoke goes to the INACTIVE state, and it must be reviewed and accepted by a hub administrator.", "id": "AutoAccept", "properties": {"autoAcceptProjects": {"description": "Optional. A list of project ids or project numbers for which you want to enable auto-accept. The auto-accept setting is applied to spokes being created or updated in these projects.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "AutoCreatedSubnetworkInfo": {"description": "Information for the automatically created subnetwork and its associated IR.", "id": "AutoCreatedSubnetworkInfo", "properties": {"internalRange": {"description": "Output only. URI of the automatically created Internal Range. Only set if the subnetwork mode is AUTO_CREATED during creation.", "readOnly": true, "type": "string"}, "internalRangeRef": {"description": "Output only. URI of the automatically created Internal Range reference. Only set if the subnetwork mode is AUTO_CREATED during creation.", "readOnly": true, "type": "string"}, "subnetwork": {"description": "Output only. URI of the automatically created subnetwork. Only set if the subnetwork mode is AUTO_CREATED during creation.", "readOnly": true, "type": "string"}, "subnetworkRef": {"description": "Output only. URI of the automatically created subnetwork reference. Only set if the subnetwork mode is AUTO_CREATED during creation.", "readOnly": true, "type": "string"}}, "type": "object"}, "Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "Binding", "properties": {"condition": {"$ref": "Expr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "ConsumerPscConfig": {"description": "Allow the producer to specify which consumers can connect to it.", "id": "ConsumerPscConfig", "properties": {"consumerInstanceProject": {"description": "Required. The project ID or project number of the consumer project. This project is the one that the consumer uses to interact with the producer instance. From the perspective of a consumer who's created a producer instance, this is the project of the producer instance. Format: 'projects/' Eg. 'projects/consumer-project' or 'projects/1234'", "type": "string"}, "disableGlobalAccess": {"description": "This is used in PSC consumer ForwardingRule to control whether the PSC endpoint can be accessed from another region.", "type": "boolean"}, "ipVersion": {"description": "The requested IP version for the PSC connection.", "enum": ["IP_VERSION_UNSPECIFIED", "IPV4", "IPV6"], "enumDescriptions": ["Default value. We will use IPv4 or IPv6 depending on the IP version of first available subnetwork.", "Will use IPv4 only.", "Will use IPv6 only."], "type": "string"}, "network": {"description": "The resource path of the consumer network where PSC connections are allowed to be created in. Note, this network does not need be in the ConsumerPscConfig.project in the case of SharedVPC. Example: projects/{projectNumOrId}/global/networks/{networkId}.", "type": "string"}, "producerInstanceId": {"deprecated": true, "description": "Immutable. Deprecated. Use producer_instance_metadata instead. An immutable identifier for the producer instance.", "type": "string"}, "producerInstanceMetadata": {"additionalProperties": {"type": "string"}, "description": "Immutable. An immutable map for the producer instance metadata.", "type": "object"}, "project": {"description": "The consumer project where PSC connections are allowed to be created in.", "type": "string"}, "serviceAttachmentIpAddressMap": {"additionalProperties": {"type": "string"}, "description": "Output only. A map to store mapping between customer vip and target service attachment. Only service attachment with producer specified ip addresses are stored here.", "readOnly": true, "type": "object"}, "state": {"description": "Output only. Overall state of PSC Connections management for this consumer psc config.", "enum": ["STATE_UNSPECIFIED", "VALID", "CONNECTION_POLICY_MISSING", "POLICY_LIMIT_REACHED", "CONSUMER_INSTANCE_PROJECT_NOT_ALLOWLISTED"], "enumDescriptions": ["Default state, when Connection Map is created initially.", "Set when policy and map configuration is valid, and their matching can lead to allowing creation of PSC Connections subject to other constraints like connections limit.", "No Service Connection Policy found for this network and Service Class", "Service Connection Policy limit reached for this network and Service Class", "The consumer instance project is not in AllowedGoogleProducersResourceHierarchyLevels of the matching ServiceConnectionPolicy."], "readOnly": true, "type": "string"}}, "type": "object"}, "ConsumerPscConnection": {"description": "PSC connection details on consumer side.", "id": "ConsumerPscConnection", "properties": {"error": {"$ref": "GoogleRpcStatus", "deprecated": true, "description": "The most recent error during operating this connection."}, "errorInfo": {"$ref": "GoogleRpcErrorInfo", "description": "Output only. The error info for the latest error during operating this connection.", "readOnly": true}, "errorType": {"deprecated": true, "description": "The error type indicates whether the error is consumer facing, producer facing or system internal.", "enum": ["CONNECTION_ERROR_TYPE_UNSPECIFIED", "ERROR_INTERNAL", "ERROR_CONSUMER_SIDE", "ERROR_PRODUCER_SIDE"], "enumDescriptions": ["An invalid error type as the default case.", "The error is due to Service Automation system internal.", "The error is due to the setup on consumer side.", "The error is due to the setup on producer side."], "type": "string"}, "forwardingRule": {"description": "The URI of the consumer forwarding rule created. Example: projects/{projectNumOrId}/regions/us-east1/networks/{resourceId}.", "type": "string"}, "gceOperation": {"description": "The last Compute Engine operation to setup PSC connection.", "type": "string"}, "ip": {"description": "The IP literal allocated on the consumer network for the PSC forwarding rule that is created to connect to the producer service attachment in this service connection map.", "type": "string"}, "ipVersion": {"description": "The requested IP version for the PSC connection.", "enum": ["IP_VERSION_UNSPECIFIED", "IPV4", "IPV6"], "enumDescriptions": ["Default value. We will use IPv4 or IPv6 depending on the IP version of first available subnetwork.", "Will use IPv4 only.", "Will use IPv6 only."], "type": "string"}, "network": {"description": "The consumer network whose PSC forwarding rule is connected to the service attachments in this service connection map. Note that the network could be on a different project (shared VPC).", "type": "string"}, "producerInstanceId": {"deprecated": true, "description": "Immutable. Deprecated. Use producer_instance_metadata instead. An immutable identifier for the producer instance.", "type": "string"}, "producerInstanceMetadata": {"additionalProperties": {"type": "string"}, "description": "Immutable. An immutable map for the producer instance metadata.", "type": "object"}, "project": {"description": "The consumer project whose PSC forwarding rule is connected to the service attachments in this service connection map.", "type": "string"}, "pscConnectionId": {"description": "The PSC connection id of the PSC forwarding rule connected to the service attachments in this service connection map.", "type": "string"}, "selectedSubnetwork": {"description": "Output only. The URI of the selected subnetwork selected to allocate IP address for this connection.", "readOnly": true, "type": "string"}, "serviceAttachmentUri": {"description": "The URI of a service attachment which is the target of the PSC connection.", "type": "string"}, "state": {"description": "The state of the PSC connection.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "FAILED", "CREATING", "DELETING", "CREATE_REPAIRING", "DELETE_REPAIRING"], "enumDescriptions": ["An invalid state as the default case.", "The connection has been created successfully. However, for the up-to-date connection status, please use the service attachment's \"ConnectedEndpoint.status\" as the source of truth.", "The connection is not functional since some resources on the connection fail to be created.", "The connection is being created.", "The connection is being deleted.", "The connection is being repaired to complete creation.", "The connection is being repaired to complete deletion."], "type": "string"}}, "type": "object"}, "Destination": {"description": "The `Destination` resource. It specifies the IP prefix and the associated autonomous system numbers (ASN) that you want to include in a `MulticloudDataTransferConfig` resource.", "id": "Destination", "properties": {"createTime": {"description": "Output only. Time when the `Destination` resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. A description of this resource.", "type": "string"}, "endpoints": {"description": "Required. Unordered list. The list of `DestinationEndpoint` resources configured for the IP prefix.", "items": {"$ref": "DestinationEndpoint"}, "type": "array"}, "etag": {"description": "The etag is computed by the server, and might be sent with update and delete requests so that the client has an up-to-date value before proceeding.", "type": "string"}, "ipPrefix": {"description": "Required. Immutable. The IP prefix that represents your workload on another CSP.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. User-defined labels.", "type": "object"}, "name": {"description": "Identifier. The name of the `Destination` resource. Format: `projects/{project}/locations/{location}/multicloudDataTransferConfigs/{multicloud_data_transfer_config}/destinations/{destination}`.", "type": "string"}, "stateTimeline": {"$ref": "StateTimeline", "description": "Output only. The timeline of the expected `Destination` states or the current rest state. If a state change is expected, the value is `ADDING`, `DELETING` or `SUSPENDING`, depending on the action specified. Example: \"state_timeline\": { \"states\": [ { // The time when the `Destination` resource will be activated. \"effectiveTime\": \"2024-12-01T08:00:00Z\", \"state\": \"ADDING\" }, { // The time when the `Destination` resource will be suspended. \"effectiveTime\": \"2024-12-01T20:00:00Z\", \"state\": \"SUSPENDING\" } ] }", "readOnly": true}, "uid": {"description": "Output only. The Google-generated unique ID for the `Destination` resource. This value is unique across all `Destination` resources. If a resource is deleted and another with the same name is created, the new resource is assigned a different and unique ID.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Time when the `Destination` resource was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "DestinationEndpoint": {"description": "The metadata for a `DestinationEndpoint` resource.", "id": "DestinationEndpoint", "properties": {"asn": {"description": "Required. The ASN of the remote IP prefix.", "format": "int64", "type": "string"}, "csp": {"description": "Required. The CSP of the remote IP prefix.", "type": "string"}, "state": {"description": "Output only. The state of the `DestinationEndpoint` resource.", "enum": ["STATE_UNSPECIFIED", "VALID", "INVALID"], "enumDescriptions": ["An invalid state, which is the default case.", "The `DestinationEndpoint` resource is valid.", "The `DestinationEndpoint` resource is invalid."], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Time when the `DestinationEndpoint` resource was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "Filter": {"description": "Filter matches L4 traffic.", "id": "Filter", "properties": {"destRange": {"description": "Optional. The destination IP range of outgoing packets that this policy-based route applies to. Default is \"0.0.0.0/0\" if protocol version is IPv4 and \"::/0\" if protocol version is IPv6.", "type": "string"}, "ipProtocol": {"description": "Optional. The IP protocol that this policy-based route applies to. Valid values are 'TCP', 'UDP', and 'ALL'. Default is 'ALL'.", "type": "string"}, "protocolVersion": {"description": "Required. Internet protocol versions this policy-based route applies to. IPV4 and IPV6 is supported.", "enum": ["PROTOCOL_VERSION_UNSPECIFIED", "IPV4", "IPV6"], "enumDescriptions": ["Default value.", "The PBR is for IPv4 internet protocol traffic.", "The PBR is for IPv6 internet protocol traffic."], "type": "string"}, "srcRange": {"description": "Optional. The source IP range of outgoing packets that this policy-based route applies to. Default is \"0.0.0.0/0\" if protocol version is IPv4 and \"::/0\" if protocol version is IPv6.", "type": "string"}}, "type": "object"}, "GoogleLongrunningCancelOperationRequest": {"description": "The request message for Operations.CancelOperation.", "id": "GoogleLongrunningCancelOperationRequest", "properties": {}, "type": "object"}, "GoogleLongrunningListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "GoogleLongrunningListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "GoogleLongrunningOperation"}, "type": "array"}}, "type": "object"}, "GoogleLongrunningOperation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "GoogleLongrunningOperation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "GoogleRpcStatus", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "GoogleRpcErrorInfo": {"description": "Describes the cause of the error with structured details. Example of an error when contacting the \"pubsub.googleapis.com\" API when it is not enabled: { \"reason\": \"API_DISABLED\" \"domain\": \"googleapis.com\" \"metadata\": { \"resource\": \"projects/123\", \"service\": \"pubsub.googleapis.com\" } } This response indicates that the pubsub.googleapis.com API is not enabled. Example of an error that is returned when attempting to create a Spanner instance in a region that is out of stock: { \"reason\": \"STOCKOUT\" \"domain\": \"spanner.googleapis.com\", \"metadata\": { \"availableRegions\": \"us-central1,us-east2\" } }", "id": "GoogleRpcErrorInfo", "properties": {"domain": {"description": "The logical grouping to which the \"reason\" belongs. The error domain is typically the registered service name of the tool or product that generates the error. Example: \"pubsub.googleapis.com\". If the error is generated by some common infrastructure, the error domain must be a globally unique value that identifies the infrastructure. For Google API infrastructure, the error domain is \"googleapis.com\".", "type": "string"}, "metadata": {"additionalProperties": {"type": "string"}, "description": "Additional structured details about this error. Keys must match a regular expression of `a-z+` but should ideally be lowerCamelCase. Also, they must be limited to 64 characters in length. When identifying the current value of an exceeded limit, the units should be contained in the key, not the value. For example, rather than `{\"instanceLimit\": \"100/request\"}`, should be returned as, `{\"instanceLimitPerRequest\": \"100\"}`, if the client exceeds the number of instances that can be created in a single (batch) request.", "type": "object"}, "reason": {"description": "The reason of the error. This is a constant value that identifies the proximate cause of the error. Error reasons are unique within a particular domain of errors. This should be at most 63 characters and match a regular expression of `A-Z+[A-Z0-9]`, which represents UPPER_SNAKE_CASE.", "type": "string"}}, "type": "object"}, "GoogleRpcStatus": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "GoogleRpcStatus", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "Group": {"description": "A group represents a subset of spokes attached to a hub.", "id": "Group", "properties": {"autoAccept": {"$ref": "AutoAccept", "description": "Optional. The auto-accept setting for this group."}, "createTime": {"description": "Output only. The time the group was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. The description of the group.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels in key-value pair format. For more information about labels, see [Requirements for labels](https://cloud.google.com/resource-manager/docs/creating-managing-labels#requirements).", "type": "object"}, "name": {"description": "Immutable. The name of the group. Group names must be unique. They use the following form: `projects/{project_number}/locations/global/hubs/{hub}/groups/{group_id}`", "type": "string"}, "routeTable": {"description": "Output only. The name of the route table that corresponds to this group. They use the following form: `projects/{project_number}/locations/global/hubs/{hub_id}/routeTables/{route_table_id}`", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The current lifecycle state of this group.", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "DELETING", "ACCEPTING", "REJECTING", "UPDATING", "INACTIVE", "OBSOLETE", "FAILED"], "enumDescriptions": ["No state information available", "The resource's create operation is in progress.", "The resource is active", "The resource's delete operation is in progress.", "The resource's accept operation is in progress.", "The resource's reject operation is in progress.", "The resource's update operation is in progress.", "The resource is inactive.", "The hub associated with this spoke resource has been deleted. This state applies to spoke resources only.", "The resource is in an undefined state due to resource creation or deletion failure. You can try to delete the resource later or contact support for help."], "readOnly": true, "type": "string"}, "uid": {"description": "Output only. The Google-generated UUID for the group. This value is unique across all group resources. If a group is deleted and another with the same name is created, the new route table is assigned a different unique_id.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The time the group was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "Hub": {"description": "A Network Connectivity Center hub is a global management resource to which you attach spokes. A single hub can contain spokes from multiple regions. However, if any of a hub's spokes use the site-to-site data transfer feature, the resources associated with those spokes must all be in the same VPC network. Spokes that do not use site-to-site data transfer can be associated with any VPC network in your project.", "id": "<PERSON><PERSON>", "properties": {"createTime": {"description": "Output only. The time the hub was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. An optional description of the hub.", "type": "string"}, "exportPsc": {"description": "Optional. Whether Private Service Connect connection propagation is enabled for the hub. If true, Private Service Connect endpoints in VPC spokes attached to the hub are made accessible to other VPC spokes attached to the hub. The default value is false.", "type": "boolean"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional labels in key-value pair format. For more information about labels, see [Requirements for labels](https://cloud.google.com/resource-manager/docs/creating-managing-labels#requirements).", "type": "object"}, "name": {"description": "Immutable. The name of the hub. Hub names must be unique. They use the following form: `projects/{project_number}/locations/global/hubs/{hub_id}`", "type": "string"}, "policyMode": {"description": "Optional. The policy mode of this hub. This field can be either PRESET or CUSTOM. If unspecified, the policy_mode defaults to PRESET.", "enum": ["POLICY_MODE_UNSPECIFIED", "PRESET"], "enumDescriptions": ["Policy mode is unspecified. It defaults to PRESET with preset_topology = MESH.", "<PERSON><PERSON> uses one of the preset topologies."], "type": "string"}, "presetTopology": {"description": "Optional. The topology implemented in this hub. Currently, this field is only used when policy_mode = PRESET. The available preset topologies are MESH and STAR. If preset_topology is unspecified and policy_mode = PRESET, the preset_topology defaults to MESH. When policy_mode = CUSTOM, the preset_topology is set to PRESET_TOPOLOGY_UNSPECIFIED.", "enum": ["PRESET_TOPOLOGY_UNSPECIFIED", "MESH", "STAR"], "enumDescriptions": ["Preset topology is unspecified. When policy_mode = PRESET, it defaults to MESH.", "Mesh topology is implemented. Group `default` is automatically created. All spokes in the hub are added to group `default`.", "Star topology is implemented. Two groups, `center` and `edge`, are automatically created along with hub creation. Spokes have to join one of the groups during creation."], "type": "string"}, "routeTables": {"description": "Output only. The route tables that belong to this hub. They use the following form: `projects/{project_number}/locations/global/hubs/{hub_id}/routeTables/{route_table_id}` This field is read-only. Network Connectivity Center automatically populates it based on the route tables nested under the hub.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "routingVpcs": {"description": "Output only. The VPC networks associated with this hub's spokes. This field is read-only. Network Connectivity Center automatically populates it based on the set of spokes attached to the hub.", "items": {"$ref": "RoutingVPC"}, "readOnly": true, "type": "array"}, "spokeSummary": {"$ref": "SpokeSummary", "description": "Output only. A summary of the spokes associated with a hub. The summary includes a count of spokes according to type and according to state. If any spokes are inactive, the summary also lists the reasons they are inactive, including a count for each reason.", "readOnly": true}, "state": {"description": "Output only. The current lifecycle state of this hub.", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "DELETING", "ACCEPTING", "REJECTING", "UPDATING", "INACTIVE", "OBSOLETE", "FAILED"], "enumDescriptions": ["No state information available", "The resource's create operation is in progress.", "The resource is active", "The resource's delete operation is in progress.", "The resource's accept operation is in progress.", "The resource's reject operation is in progress.", "The resource's update operation is in progress.", "The resource is inactive.", "The hub associated with this spoke resource has been deleted. This state applies to spoke resources only.", "The resource is in an undefined state due to resource creation or deletion failure. You can try to delete the resource later or contact support for help."], "readOnly": true, "type": "string"}, "uniqueId": {"description": "Output only. The Google-generated UUID for the hub. This value is unique across all hub resources. If a hub is deleted and another with the same name is created, the new hub is assigned a different unique_id.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The time the hub was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "HubStatusEntry": {"description": "A hub status entry represents the status of a set of propagated Private Service Connect connections grouped by certain fields.", "id": "HubStatusEntry", "properties": {"count": {"description": "The number of propagated Private Service Connect connections with this status. If the `group_by` field was not set in the request message, the value of this field is 1.", "format": "int32", "type": "integer"}, "groupBy": {"description": "The fields that this entry is grouped by. This has the same value as the `group_by` field in the request message.", "type": "string"}, "pscPropagationStatus": {"$ref": "PscPropagationStatus", "description": "The Private Service Connect propagation status."}}, "type": "object"}, "InterconnectAttachment": {"description": "InterconnectAttachment that this route applies to.", "id": "InterconnectAttachment", "properties": {"region": {"description": "Optional. Cloud region to install this policy-based route on interconnect attachment. Use `all` to install it on all interconnect attachments.", "type": "string"}}, "type": "object"}, "InternalRange": {"description": "The internal range resource for IPAM operations within a VPC network. Used to represent a private address range along with behavioral characteristics of that range (its usage and peering behavior). Networking resources can link to this range if they are created as belonging to it.", "id": "InternalRange", "properties": {"allocationOptions": {"$ref": "AllocationOptions", "description": "Optional. Range auto-allocation options, may be set only when auto-allocation is selected by not setting ip_cidr_range (and setting prefix_length)."}, "createTime": {"description": "Time when the internal range was created.", "format": "google-datetime", "type": "string"}, "description": {"description": "Optional. A description of this resource.", "type": "string"}, "excludeCidrRanges": {"description": "Optional. ExcludeCidrRanges flag. Specifies a set of CIDR blocks that allows exclusion of particular CIDR ranges from the auto-allocation process, without having to reserve these blocks", "items": {"type": "string"}, "type": "array"}, "immutable": {"description": "Optional. Immutable ranges cannot have their fields modified, except for labels and description.", "type": "boolean"}, "ipCidrRange": {"description": "Optional. The IP range that this internal range defines. NOTE: IPv6 ranges are limited to usage=EXTERNAL_TO_VPC and peering=FOR_SELF. NOTE: For IPv6 Ranges this field is compulsory, i.e. the address range must be specified explicitly.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "User-defined labels.", "type": "object"}, "migration": {"$ref": "Migration", "description": "Optional. Must be present if usage is set to FOR_MIGRATION."}, "name": {"description": "Identifier. The name of an internal range. Format: projects/{project}/locations/{location}/internalRanges/{internal_range} See: https://google.aip.dev/122#fields-representing-resource-names", "type": "string"}, "network": {"description": "Immutable. The URL or resource ID of the network in which to reserve the internal range. The network cannot be deleted if there are any reserved internal ranges referring to it. Legacy networks are not supported. For example: https://www.googleapis.com/compute/v1/projects/{project}/locations/global/networks/{network} projects/{project}/locations/global/networks/{network} {network}", "type": "string"}, "overlaps": {"description": "Optional. Types of resources that are allowed to overlap with the current internal range.", "items": {"enum": ["OVERLAP_UNSPECIFIED", "OVERLAP_ROUTE_RANGE", "OVERLAP_EXISTING_SUBNET_RANGE"], "enumDescriptions": ["No overlap overrides.", "Allow creation of static routes more specific that the current internal range.", "Allow creation of internal ranges that overlap with existing subnets."], "type": "string"}, "type": "array"}, "peering": {"description": "Optional. The type of peering set for this internal range.", "enum": ["PEERING_UNSPECIFIED", "FOR_SELF", "FOR_PEER", "NOT_SHARED"], "enumDescriptions": ["If Peering is left unspecified in CreateInternalRange or UpdateInternalRange, it will be defaulted to FOR_SELF.", "This is the default behavior and represents the case that this internal range is intended to be used in the VPC in which it is created and is accessible from its peers. This implies that peers or peers-of-peers cannot use this range.", "This behavior can be set when the internal range is being reserved for usage by peers. This means that no resource within the VPC in which it is being created can use this to associate with a VPC resource, but one of the peers can. This represents donating a range for peers to use.", "This behavior can be set when the internal range is being reserved for usage by the VPC in which it is created, but not shared with peers. In a sense, it is local to the VPC. This can be used to create internal ranges for various purposes like HTTP_INTERNAL_LOAD_BALANCER or for Interconnect routes that are not shared with peers. This also implies that peers cannot use this range in a way that is visible to this VPC, but can re-use this range as long as it is NOT_SHARED from the peer VPC, too."], "type": "string"}, "prefixLength": {"description": "Optional. An alternate to ip_cidr_range. Can be set when trying to create an IPv4 reservation that automatically finds a free range of the given size. If both ip_cidr_range and prefix_length are set, there is an error if the range sizes do not match. Can also be used during updates to change the range size. NOTE: For IPv6 this field only works if ip_cidr_range is set as well, and both fields must match. In other words, with IPv6 this field only works as a redundant parameter.", "format": "int32", "type": "integer"}, "targetCidrRange": {"description": "Optional. Can be set to narrow down or pick a different address space while searching for a free range. If not set, defaults to the [\"10.0.0.0/8\", \"**********/12\", \"***********/16\"] address space (for auto-mode networks, the \"10.0.0.0/9\" range is used instead of \"10.0.0.0/8\"). This can be used to target the search in other rfc-1918 address spaces like \"**********/12\" and \"***********/16\" or non-rfc-1918 address spaces used in the VPC.", "items": {"type": "string"}, "type": "array"}, "updateTime": {"description": "Time when the internal range was updated.", "format": "google-datetime", "type": "string"}, "usage": {"description": "Optional. The type of usage set for this InternalRange.", "enum": ["USAGE_UNSPECIFIED", "FOR_VPC", "EXTERNAL_TO_VPC", "FOR_MIGRATION"], "enumDescriptions": ["Unspecified usage is allowed in calls which identify the resource by other fields and do not need Usage set to complete. These are, i.e.: GetInternalRange and DeleteInternalRange. Usage needs to be specified explicitly in CreateInternalRange or UpdateInternalRange calls.", "A VPC resource can use the reserved CIDR block by associating it with the internal range resource if usage is set to FOR_VPC.", "Ranges created with EXTERNAL_TO_VPC cannot be associated with VPC resources and are meant to block out address ranges for various use cases, like for example, usage on-prem, with dynamic route announcements via interconnect.", "Ranges created FOR_MIGRATION can be used to lock a CIDR range between a source and target subnet. If usage is set to FOR_MIGRATION, the peering value has to be set to FOR_SELF or default to FOR_SELF when unset."], "type": "string"}, "users": {"description": "Output only. The list of resources that refer to this internal range. Resources that use the internal range for their range allocation are referred to as users of the range. Other resources mark themselves as users while doing so by creating a reference to this internal range. Having a user, based on this reference, prevents deletion of the internal range referred to. Can be empty.", "items": {"type": "string"}, "readOnly": true, "type": "array"}}, "type": "object"}, "LinkedInterconnectAttachments": {"description": "A collection of VLAN attachment resources. These resources should be redundant attachments that all advertise the same prefixes to Google Cloud. Alternatively, in active/passive configurations, all attachments should be capable of advertising the same prefixes.", "id": "LinkedInterconnectAttachments", "properties": {"includeImportRanges": {"description": "Optional. Hub routes fully encompassed by include import ranges are included during import from hub.", "items": {"type": "string"}, "type": "array"}, "siteToSiteDataTransfer": {"description": "A value that controls whether site-to-site data transfer is enabled for these resources. Data transfer is available only in [supported locations](https://cloud.google.com/network-connectivity/docs/network-connectivity-center/concepts/locations).", "type": "boolean"}, "uris": {"description": "The URIs of linked interconnect attachment resources", "items": {"type": "string"}, "type": "array"}, "vpcNetwork": {"description": "Output only. The VPC network where these VLAN attachments are located.", "readOnly": true, "type": "string"}}, "type": "object"}, "LinkedProducerVpcNetwork": {"id": "LinkedProducerVpcNetwork", "properties": {"excludeExportRanges": {"description": "Optional. IP ranges encompassing the subnets to be excluded from peering.", "items": {"type": "string"}, "type": "array"}, "includeExportRanges": {"description": "Optional. IP ranges allowed to be included from peering.", "items": {"type": "string"}, "type": "array"}, "network": {"description": "Immutable. The URI of the Service Consumer VPC that the Producer VPC is peered with.", "type": "string"}, "peering": {"description": "Immutable. The name of the VPC peering between the Service Consumer VPC and the Producer VPC (defined in the Tenant project) which is added to the NCC hub. This peering must be in ACTIVE state.", "type": "string"}, "producerNetwork": {"description": "Output only. The URI of the Producer VPC.", "readOnly": true, "type": "string"}, "proposedExcludeExportRanges": {"description": "Output only. The proposed exclude export IP ranges waiting for hub administration's approval.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "proposedIncludeExportRanges": {"description": "Output only. The proposed include export IP ranges waiting for hub administration's approval.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "serviceConsumerVpcSpoke": {"description": "Output only. The Service Consumer Network spoke.", "readOnly": true, "type": "string"}}, "type": "object"}, "LinkedRouterApplianceInstances": {"description": "A collection of router appliance instances. If you configure multiple router appliance instances to receive data from the same set of sites outside of Google Cloud, we recommend that you associate those instances with the same spoke.", "id": "LinkedRouterApplianceInstances", "properties": {"includeImportRanges": {"description": "Optional. Hub routes fully encompassed by include import ranges are included during import from hub.", "items": {"type": "string"}, "type": "array"}, "instances": {"description": "The list of router appliance instances.", "items": {"$ref": "RouterApplianceInstance"}, "type": "array"}, "siteToSiteDataTransfer": {"description": "A value that controls whether site-to-site data transfer is enabled for these resources. Data transfer is available only in [supported locations](https://cloud.google.com/network-connectivity/docs/network-connectivity-center/concepts/locations).", "type": "boolean"}, "vpcNetwork": {"description": "Output only. The VPC network where these router appliance instances are located.", "readOnly": true, "type": "string"}}, "type": "object"}, "LinkedVpcNetwork": {"description": "An existing VPC network.", "id": "LinkedVpcNetwork", "properties": {"excludeExportRanges": {"description": "Optional. IP ranges encompassing the subnets to be excluded from peering.", "items": {"type": "string"}, "type": "array"}, "includeExportRanges": {"description": "Optional. IP ranges allowed to be included from peering.", "items": {"type": "string"}, "type": "array"}, "producerVpcSpokes": {"description": "Output only. The list of Producer VPC spokes that this VPC spoke is a service consumer VPC spoke for. These producer VPCs are connected through VPC peering to this spoke's backing VPC network. Because they are directly connected through VPC peering, NCC export filters do not apply between the service consumer VPC spoke and any of its producer VPC spokes. This VPC spoke cannot be deleted as long as any of these producer VPC spokes are connected to the NCC Hub.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "proposedExcludeExportRanges": {"description": "Output only. The proposed exclude export IP ranges waiting for hub administration's approval.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "proposedIncludeExportRanges": {"description": "Output only. The proposed include export IP ranges waiting for hub administration's approval.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "uri": {"description": "Required. The URI of the VPC network resource.", "type": "string"}}, "type": "object"}, "LinkedVpnTunnels": {"description": "A collection of Cloud VPN tunnel resources. These resources should be redundant HA VPN tunnels that all advertise the same prefixes to Google Cloud. Alternatively, in a passive/active configuration, all tunnels should be capable of advertising the same prefixes.", "id": "LinkedVpnTunnels", "properties": {"includeImportRanges": {"description": "Optional. Hub routes fully encompassed by include import ranges are included during import from hub.", "items": {"type": "string"}, "type": "array"}, "siteToSiteDataTransfer": {"description": "A value that controls whether site-to-site data transfer is enabled for these resources. Data transfer is available only in [supported locations](https://cloud.google.com/network-connectivity/docs/network-connectivity-center/concepts/locations).", "type": "boolean"}, "uris": {"description": "The URIs of linked VPN tunnel resources.", "items": {"type": "string"}, "type": "array"}, "vpcNetwork": {"description": "Output only. The VPC network where these VPN tunnels are located.", "readOnly": true, "type": "string"}}, "type": "object"}, "ListDestinationsResponse": {"description": "Response message to list `Destination` resources.", "id": "ListDestinationsResponse", "properties": {"destinations": {"description": "The list of `Destination` resources to be listed.", "items": {"$ref": "Destination"}, "type": "array"}, "nextPageToken": {"description": "The next page token.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListGroupsResponse": {"description": "Response for HubService.ListGroups method.", "id": "ListGroupsResponse", "properties": {"groups": {"description": "The requested groups.", "items": {"$ref": "Group"}, "type": "array"}, "nextPageToken": {"description": "The token for the next page of the response. To see more results, use this value as the page_token for your next request. If this value is empty, there are no more results.", "type": "string"}, "unreachable": {"description": "Hubs that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListHubSpokesResponse": {"description": "The response for HubService.ListHubSpokes.", "id": "ListHubSpokesResponse", "properties": {"nextPageToken": {"description": "The token for the next page of the response. To see more results, use this value as the page_token for your next request. If this value is empty, there are no more results.", "type": "string"}, "spokes": {"description": "The requested spokes. The spoke fields can be partially populated based on the `view` field in the request message.", "items": {"$ref": "Spoke"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListHubsResponse": {"description": "Response for HubService.ListHubs method.", "id": "ListHubsResponse", "properties": {"hubs": {"description": "The requested hubs.", "items": {"$ref": "<PERSON><PERSON>"}, "type": "array"}, "nextPageToken": {"description": "The token for the next page of the response. To see more results, use this value as the page_token for your next request. If this value is empty, there are no more results.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListInternalRangesResponse": {"description": "Response for InternalRange.ListInternalRanges", "id": "ListInternalRangesResponse", "properties": {"internalRanges": {"description": "Internal ranges to be returned.", "items": {"$ref": "InternalRange"}, "type": "array"}, "nextPageToken": {"description": "The next pagination token in the List response. It should be used as page_token for the following request. An empty value means no more result.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListLocationsResponse": {"description": "The response message for Locations.ListLocations.", "id": "ListLocationsResponse", "properties": {"locations": {"description": "A list of locations that matches the specified filter in the request.", "items": {"$ref": "Location"}, "type": "array"}, "nextPageToken": {"description": "The standard List next-page token.", "type": "string"}}, "type": "object"}, "ListMulticloudDataTransferConfigsResponse": {"description": "Response message to list `MulticloudDataTransferConfig` resources.", "id": "ListMulticloudDataTransferConfigsResponse", "properties": {"multicloudDataTransferConfigs": {"description": "The list of `MulticloudDataTransferConfig` resources to be listed.", "items": {"$ref": "MulticloudDataTransferConfig"}, "type": "array"}, "nextPageToken": {"description": "The next page token.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListMulticloudDataTransferSupportedServicesResponse": {"description": "Response message to list the services in your project in regions that are eligible for Data Transfer Essentials configuration.", "id": "ListMulticloudDataTransferSupportedServicesResponse", "properties": {"multicloudDataTransferSupportedServices": {"description": "The list of supported services.", "items": {"$ref": "MulticloudDataTransferSupportedService"}, "type": "array"}, "nextPageToken": {"description": "The next page token.", "type": "string"}}, "type": "object"}, "ListPolicyBasedRoutesResponse": {"description": "Response for PolicyBasedRoutingService.ListPolicyBasedRoutes method.", "id": "ListPolicyBasedRoutesResponse", "properties": {"nextPageToken": {"description": "The next pagination token in the List response. It should be used as page_token for the following request. An empty value means no more result.", "type": "string"}, "policyBasedRoutes": {"description": "Policy-based routes to be returned.", "items": {"$ref": "PolicyBasedRoute"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListRegionalEndpointsResponse": {"description": "Response for ListRegionalEndpoints.", "id": "ListRegionalEndpointsResponse", "properties": {"nextPageToken": {"description": "The next pagination token in the List response. It should be used as page_token for the following request. An empty value means no more result.", "type": "string"}, "regionalEndpoints": {"description": "Regional endpoints to be returned.", "items": {"$ref": "RegionalEndpoint"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListRemoteTransportProfilesResponse": {"description": "Message for response to listing RemoteTransportProfiles", "id": "ListRemoteTransportProfilesResponse", "properties": {"nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "remoteTransportProfiles": {"description": "The list of RemoteTransportProfiles", "items": {"$ref": "RemoteTransportProfile"}, "type": "array"}, "unreachable": {"description": "Unordered list. Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListRouteTablesResponse": {"description": "Response for HubService.ListRouteTables method.", "id": "ListRouteTablesResponse", "properties": {"nextPageToken": {"description": "The token for the next page of the response. To see more results, use this value as the page_token for your next request. If this value is empty, there are no more results.", "type": "string"}, "routeTables": {"description": "The requested route tables.", "items": {"$ref": "RouteTable"}, "type": "array"}, "unreachable": {"description": "Hubs that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListRoutesResponse": {"description": "Response for HubService.ListRoutes method.", "id": "ListRoutesResponse", "properties": {"nextPageToken": {"description": "The token for the next page of the response. To see more results, use this value as the page_token for your next request. If this value is empty, there are no more results.", "type": "string"}, "routes": {"description": "The requested routes.", "items": {"$ref": "Route"}, "type": "array"}, "unreachable": {"description": "RouteTables that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListServiceClassesResponse": {"description": "Response for ListServiceClasses.", "id": "ListServiceClassesResponse", "properties": {"nextPageToken": {"description": "The next pagination token in the List response. It should be used as page_token for the following request. An empty value means no more result.", "type": "string"}, "serviceClasses": {"description": "ServiceClasses to be returned.", "items": {"$ref": "ServiceClass"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListServiceConnectionMapsResponse": {"description": "Response for ListServiceConnectionMaps.", "id": "ListServiceConnectionMapsResponse", "properties": {"nextPageToken": {"description": "The next pagination token in the List response. It should be used as page_token for the following request. An empty value means no more result.", "type": "string"}, "serviceConnectionMaps": {"description": "ServiceConnectionMaps to be returned.", "items": {"$ref": "ServiceConnectionMap"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListServiceConnectionPoliciesResponse": {"description": "Response for ListServiceConnectionPolicies.", "id": "ListServiceConnectionPoliciesResponse", "properties": {"nextPageToken": {"description": "The next pagination token in the List response. It should be used as page_token for the following request. An empty value means no more result.", "type": "string"}, "serviceConnectionPolicies": {"description": "ServiceConnectionPolicies to be returned.", "items": {"$ref": "ServiceConnectionPolicy"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListServiceConnectionTokensResponse": {"description": "Response for ListServiceConnectionTokens.", "id": "ListServiceConnectionTokensResponse", "properties": {"nextPageToken": {"description": "The next pagination token in the List response. It should be used as page_token for the following request. An empty value means no more result.", "type": "string"}, "serviceConnectionTokens": {"description": "ServiceConnectionTokens to be returned.", "items": {"$ref": "ServiceConnectionToken"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListSpokesResponse": {"description": "The response for HubService.ListSpokes.", "id": "ListSpokesResponse", "properties": {"nextPageToken": {"description": "The token for the next page of the response. To see more results, use this value as the page_token for your next request. If this value is empty, there are no more results.", "type": "string"}, "spokes": {"description": "The requested spokes.", "items": {"$ref": "Spoke"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListTransportsResponse": {"description": "Message for response to listing Transports", "id": "ListTransportsResponse", "properties": {"nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "transports": {"description": "The list of Transport", "items": {"$ref": "Transport"}, "type": "array"}, "unreachable": {"description": "Unordered list. Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Location": {"description": "A resource that represents a Google Cloud location.", "id": "Location", "properties": {"displayName": {"description": "The friendly name for this location, typically a nearby city name. For example, \"Tokyo\".", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cross-service attributes for the location. For example {\"cloud.googleapis.com/region\": \"us-east1\"}", "type": "object"}, "locationId": {"description": "The canonical id for this location. For example: `\"us-east1\"`.", "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata. For example the available capacity at the given location.", "type": "object"}, "name": {"description": "Resource name for the location, which may vary between implementations. For example: `\"projects/example-project/locations/us-east1\"`", "type": "string"}}, "type": "object"}, "LocationMetadata": {"description": "Metadata about locations", "id": "LocationMetadata", "properties": {"locationFeatures": {"description": "List of supported features", "items": {"enum": ["LOCATION_FEATURE_UNSPECIFIED", "SITE_TO_CLOUD_SPOKES", "SITE_TO_SITE_SPOKES", "GATEWAY_SPOKES"], "enumDescriptions": ["No publicly supported feature in this location", "Site-to-cloud spokes are supported in this location", "Site-to-site spokes are supported in this location", "Gateway spokes are supported in this location."], "type": "string"}, "type": "array"}}, "type": "object"}, "Migration": {"description": "Specification for migration with source and target resource names.", "id": "Migration", "properties": {"source": {"description": "Immutable. Resource path as an URI of the source resource, for example a subnet. The project for the source resource should match the project for the InternalRange. An example: /projects/{project}/regions/{region}/subnetworks/{subnet}", "type": "string"}, "target": {"description": "Immutable. Resource path of the target resource. The target project can be different, as in the cases when migrating to peer networks. For example: /projects/{project}/regions/{region}/subnetworks/{subnet}", "type": "string"}}, "type": "object"}, "MulticloudDataTransferConfig": {"description": "The `MulticloudDataTransferConfig` resource. It lists the services that you configure for Data Transfer Essentials billing and metering.", "id": "MulticloudDataTransferConfig", "properties": {"createTime": {"description": "Output only. Time when the `MulticloudDataTransferConfig` resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. A description of this resource.", "type": "string"}, "destinationsActiveCount": {"description": "Output only. The number of `Destination` resources in use with the `MulticloudDataTransferConfig` resource.", "format": "int32", "readOnly": true, "type": "integer"}, "destinationsCount": {"description": "Output only. The number of `Destination` resources configured for the `MulticloudDataTransferConfig` resource.", "format": "int32", "readOnly": true, "type": "integer"}, "etag": {"description": "The etag is computed by the server, and might be sent with update and delete requests so that the client has an up-to-date value before proceeding.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. User-defined labels.", "type": "object"}, "name": {"description": "Identifier. The name of the `MulticloudDataTransferConfig` resource. Format: `projects/{project}/locations/{location}/multicloudDataTransferConfigs/{multicloud_data_transfer_config}`.", "type": "string"}, "services": {"additionalProperties": {"$ref": "StateTimeline"}, "description": "Optional. Maps services to their current or planned states. Service names are keys, and the associated values describe the state of the service. If a state change is expected, the value is either `ADDING` or `DELETING`, depending on the actions taken. Sample output: \"services\": { \"big-query\": { \"states\": [ { \"effectiveTime\": \"2024-12-12T08:00:00Z\" \"state\": \"ADDING\", }, ] }, \"cloud-storage\": { \"states\": [ { \"state\": \"ACTIVE\", } ] } }", "type": "object"}, "uid": {"description": "Output only. The Google-generated unique ID for the `MulticloudDataTransferConfig` resource. This value is unique across all `MulticloudDataTransferConfig` resources. If a resource is deleted and another with the same name is created, the new resource is assigned a different and unique ID.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Time when the `MulticloudDataTransferConfig` resource was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "MulticloudDataTransferSupportedService": {"description": "A service in your project in a region that is eligible for Data Transfer Essentials configuration.", "id": "MulticloudDataTransferSupportedService", "properties": {"name": {"description": "Identifier. The name of the service.", "type": "string"}, "serviceConfigs": {"description": "Output only. The network service tier or regional endpoint supported for the service.", "items": {"$ref": "ServiceConfig"}, "readOnly": true, "type": "array"}}, "type": "object"}, "NextHopInterconnectAttachment": {"description": "A route next hop that leads to an interconnect attachment resource.", "id": "NextHopInterconnectAttachment", "properties": {"siteToSiteDataTransfer": {"description": "Indicates whether site-to-site data transfer is allowed for this interconnect attachment resource. Data transfer is available only in [supported locations](https://cloud.google.com/network-connectivity/docs/network-connectivity-center/concepts/locations).", "type": "boolean"}, "uri": {"description": "The URI of the interconnect attachment resource.", "type": "string"}, "vpcNetwork": {"description": "The VPC network where this interconnect attachment is located.", "type": "string"}}, "type": "object"}, "NextHopRouterApplianceInstance": {"description": "A route next hop that leads to a Router appliance instance.", "id": "NextHopRouterApplianceInstance", "properties": {"siteToSiteDataTransfer": {"description": "Indicates whether site-to-site data transfer is allowed for this Router appliance instance resource. Data transfer is available only in [supported locations](https://cloud.google.com/network-connectivity/docs/network-connectivity-center/concepts/locations).", "type": "boolean"}, "uri": {"description": "The URI of the Router appliance instance.", "type": "string"}, "vpcNetwork": {"description": "The VPC network where this VM is located.", "type": "string"}}, "type": "object"}, "NextHopSpoke": {"description": "A route next hop that leads to a spoke resource.", "id": "NextHopSpoke", "properties": {"siteToSiteDataTransfer": {"description": "Indicates whether site-to-site data transfer is allowed for this spoke resource. Data transfer is available only in [supported locations](https://cloud.google.com/network-connectivity/docs/network-connectivity-center/concepts/locations). Whether this route is accessible to other hybrid spokes with site-to-site data transfer enabled. If this is false, the route is only accessible to VPC spokes of the connected Hub.", "type": "boolean"}, "uri": {"description": "The URI of the spoke resource.", "type": "string"}}, "type": "object"}, "NextHopVPNTunnel": {"description": "A route next hop that leads to a VPN tunnel resource.", "id": "NextHopVPNTunnel", "properties": {"siteToSiteDataTransfer": {"description": "Indicates whether site-to-site data transfer is allowed for this VPN tunnel resource. Data transfer is available only in [supported locations](https://cloud.google.com/network-connectivity/docs/network-connectivity-center/concepts/locations).", "type": "boolean"}, "uri": {"description": "The URI of the VPN tunnel resource.", "type": "string"}, "vpcNetwork": {"description": "The VPC network where this VPN tunnel is located.", "type": "string"}}, "type": "object"}, "NextHopVpcNetwork": {"id": "NextHopVpcNetwork", "properties": {"uri": {"description": "The URI of the VPC network resource", "type": "string"}}, "type": "object"}, "OperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "OperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have been cancelled successfully have google.longrunning.Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "Policy", "properties": {"auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "items": {"$ref": "AuditConfig"}, "type": "array"}, "bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "PolicyBasedRoute": {"description": "Policy-based routes route L4 network traffic based on not just destination IP address, but also source IP address, protocol, and more. If a policy-based route conflicts with other types of routes, the policy-based route always takes precedence.", "id": "PolicyBasedRoute", "properties": {"createTime": {"description": "Output only. Time when the policy-based route was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. An optional description of this resource. Provide this field when you create the resource.", "type": "string"}, "filter": {"$ref": "Filter", "description": "Required. The filter to match L4 traffic."}, "interconnectAttachment": {"$ref": "InterconnectAttachment", "description": "Optional. The interconnect attachments that this policy-based route applies to."}, "kind": {"description": "Output only. Type of this resource. Always networkconnectivity#policyBasedRoute for policy-based Route resources.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "User-defined labels.", "type": "object"}, "name": {"description": "Immutable. A unique name of the resource in the form of `projects/{project_number}/locations/global/PolicyBasedRoutes/{policy_based_route_id}`", "type": "string"}, "network": {"description": "Required. Fully-qualified URL of the network that this route applies to, for example: projects/my-project/global/networks/my-network.", "type": "string"}, "nextHopIlbIp": {"description": "Optional. The IP address of a global-access-enabled L4 ILB that is the next hop for matching packets. For this version, only nextHopIlbIp is supported.", "type": "string"}, "nextHopOtherRoutes": {"description": "Optional. Other routes that will be referenced to determine the next hop of the packet.", "enum": ["OTHER_ROUTES_UNSPECIFIED", "DEFAULT_ROUTING"], "enumDescriptions": ["Default value.", "Use the routes from the default routing tables (system-generated routes, custom routes, peering route) to determine the next hop. This effectively excludes matching packets being applied on other PBRs with a lower priority."], "type": "string"}, "priority": {"description": "Optional. The priority of this policy-based route. Priority is used to break ties in cases where there are more than one matching policy-based routes found. In cases where multiple policy-based routes are matched, the one with the lowest-numbered priority value wins. The default value is 1000. The priority value must be from 1 to 65535, inclusive.", "format": "int32", "type": "integer"}, "selfLink": {"description": "Output only. Server-defined fully-qualified URL for this resource.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Time when the policy-based route was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "virtualMachine": {"$ref": "VirtualMachine", "description": "Optional. VM instances that this policy-based route applies to."}, "warnings": {"description": "Output only. If potential misconfigurations are detected for this route, this field will be populated with warning messages.", "items": {"$ref": "Warnings"}, "readOnly": true, "type": "array"}}, "type": "object"}, "ProducerPscConfig": {"description": "The PSC configurations on producer side.", "id": "ProducerPscConfig", "properties": {"serviceAttachmentUri": {"description": "The resource path of a service attachment. Example: projects/{projectNumOrId}/regions/{region}/serviceAttachments/{resourceId}.", "type": "string"}}, "type": "object"}, "PscConfig": {"description": "Configuration used for Private Service Connect connections. Used when Infrastructure is PSC.", "id": "PscConfig", "properties": {"allowedGoogleProducersResourceHierarchyLevel": {"description": "Optional. List of Projects, Folders, or Organizations from where the Producer instance can be within. For example, a network administrator can provide both 'organizations/foo' and 'projects/bar' as allowed_google_producers_resource_hierarchy_levels. This allowlists this network to connect with any Producer instance within the 'foo' organization or the 'bar' project. By default, allowed_google_producers_resource_hierarchy_level is empty. The format for each allowed_google_producers_resource_hierarchy_level is / where is one of 'projects', 'folders', or 'organizations' and is either the ID or the number of the resource type. Format for each allowed_google_producers_resource_hierarchy_level value: 'projects/' or 'folders/' or 'organizations/' Eg. [projects/my-project-id, projects/567, folders/891, organizations/123]", "items": {"type": "string"}, "type": "array"}, "limit": {"description": "Optional. Max number of PSC connections for this policy.", "format": "int64", "type": "string"}, "producerInstanceLocation": {"description": "Optional. ProducerInstanceLocation is used to specify which authorization mechanism to use to determine which projects the Producer instance can be within.", "enum": ["PRODUCER_INSTANCE_LOCATION_UNSPECIFIED", "CUSTOM_RESOURCE_HIERARCHY_LEVELS"], "enumDescriptions": ["Producer instance location is not specified. When this option is chosen, then the PSC connections created by this ServiceConnectionPolicy must be within the same project as the Producer instance. This is the default ProducerInstanceLocation value. To allow for PSC connections from this network to other networks, use the CUSTOM_RESOURCE_HIERARCHY_LEVELS option.", "Producer instance must be within one of the values provided in allowed_google_producers_resource_hierarchy_level."], "type": "string"}, "subnetworks": {"description": "The resource paths of subnetworks to use for IP address management. Example: projects/{projectNumOrId}/regions/{region}/subnetworks/{resourceId}.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "PscConnection": {"description": "Information about a specific Private Service Connect connection.", "id": "PscConnection", "properties": {"consumerAddress": {"description": "The resource reference of the consumer address.", "type": "string"}, "consumerForwardingRule": {"description": "The resource reference of the PSC Forwarding Rule within the consumer VPC.", "type": "string"}, "consumerTargetProject": {"description": "The project where the PSC connection is created.", "type": "string"}, "error": {"$ref": "GoogleRpcStatus", "deprecated": true, "description": "The most recent error during operating this connection. Deprecated, please use error_info instead."}, "errorInfo": {"$ref": "GoogleRpcErrorInfo", "description": "Output only. The error info for the latest error during operating this connection.", "readOnly": true}, "errorType": {"deprecated": true, "description": "The error type indicates whether the error is consumer facing, producer facing or system internal.", "enum": ["CONNECTION_ERROR_TYPE_UNSPECIFIED", "ERROR_INTERNAL", "ERROR_CONSUMER_SIDE", "ERROR_PRODUCER_SIDE"], "enumDescriptions": ["An invalid error type as the default case.", "The error is due to Service Automation system internal.", "The error is due to the setup on consumer side.", "The error is due to the setup on producer side."], "type": "string"}, "gceOperation": {"description": "The last Compute Engine operation to setup PSC connection.", "type": "string"}, "ipVersion": {"description": "The requested IP version for the PSC connection.", "enum": ["IP_VERSION_UNSPECIFIED", "IPV4", "IPV6"], "enumDescriptions": ["Default value. We will use IPv4 or IPv6 depending on the IP version of first available subnetwork.", "Will use IPv4 only.", "Will use IPv6 only."], "type": "string"}, "producerInstanceId": {"deprecated": true, "description": "Immutable. Deprecated. Use producer_instance_metadata instead. An immutable identifier for the producer instance.", "type": "string"}, "producerInstanceMetadata": {"additionalProperties": {"type": "string"}, "description": "Immutable. An immutable map for the producer instance metadata.", "type": "object"}, "pscConnectionId": {"description": "The PSC connection id of the PSC forwarding rule.", "type": "string"}, "selectedSubnetwork": {"description": "Output only. The URI of the subnetwork selected to allocate IP address for this connection.", "readOnly": true, "type": "string"}, "serviceClass": {"description": "Output only. [Output only] The service class associated with this PSC Connection. The value is derived from the SCPolicy and matches the service class name provided by the customer.", "readOnly": true, "type": "string"}, "state": {"description": "State of the PSC Connection", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "FAILED", "CREATING", "DELETING", "CREATE_REPAIRING", "DELETE_REPAIRING"], "enumDescriptions": ["An invalid state as the default case.", "The connection has been created successfully. However, for the up-to-date connection status, please use the created forwarding rule's \"PscConnectionStatus\" as the source of truth.", "The connection is not functional since some resources on the connection fail to be created.", "The connection is being created.", "The connection is being deleted.", "The connection is being repaired to complete creation.", "The connection is being repaired to complete deletion."], "type": "string"}}, "type": "object"}, "PscPropagationStatus": {"description": "The status of one or more propagated Private Service Connect connections in a hub.", "id": "PscPropagationStatus", "properties": {"code": {"description": "The propagation status.", "enum": ["CODE_UNSPECIFIED", "READY", "PROPAGATING", "ERROR_PRODUCER_PROPAGATED_CONNECTION_LIMIT_EXCEEDED", "ERROR_PRODUCER_NAT_IP_SPACE_EXHAUSTED", "ERROR_PRODUCER_QUOTA_EXCEEDED", "ERROR_CONSUMER_QUOTA_EXCEEDED"], "enumDescriptions": ["The code is unspecified.", "The propagated Private Service Connect connection is ready.", "The Private Service Connect connection is propagating. This is a transient state.", "The Private Service Connect connection propagation failed because the VPC network or the project of the target spoke has exceeded the connection limit set by the producer.", "The Private Service Connect connection propagation failed because the NAT IP subnet space has been exhausted. It is equivalent to the `Needs attention` status of the Private Service Connect connection. See https://cloud.google.com/vpc/docs/about-accessing-vpc-hosted-services-endpoints#connection-statuses.", "The Private Service Connect connection propagation failed because the `PSC_ILB_CONSUMER_FORWARDING_RULES_PER_PRODUCER_NETWORK` quota in the producer VPC network has been exceeded.", "The Private Service Connect connection propagation failed because the `PSC_PROPAGATED_CONNECTIONS_PER_VPC_NETWORK` quota in the consumer VPC network has been exceeded."], "type": "string"}, "message": {"description": "The human-readable summary of the Private Service Connect connection propagation status.", "type": "string"}, "sourceForwardingRule": {"description": "The name of the forwarding rule exported to the hub.", "type": "string"}, "sourceGroup": {"description": "The name of the group that the source spoke belongs to.", "type": "string"}, "sourceSpoke": {"description": "The name of the spoke that the source forwarding rule belongs to.", "type": "string"}, "targetGroup": {"description": "The name of the group that the target spoke belongs to.", "type": "string"}, "targetSpoke": {"description": "The name of the spoke that the source forwarding rule propagates to.", "type": "string"}}, "type": "object"}, "QueryHubStatusResponse": {"description": "The response for HubService.QueryHubStatus.", "id": "QueryHubStatusResponse", "properties": {"hubStatusEntries": {"description": "The list of hub status.", "items": {"$ref": "HubStatusEntry"}, "type": "array"}, "nextPageToken": {"description": "The token for the next page of the response. To see more results, use this value as the page_token for your next request. If this value is empty, there are no more results.", "type": "string"}}, "type": "object"}, "RegionalEndpoint": {"description": "The RegionalEndpoint resource.", "id": "RegionalEndpoint", "properties": {"accessType": {"description": "Required. The access type of this regional endpoint. This field is reflected in the PSC Forwarding Rule configuration to enable global access.", "enum": ["ACCESS_TYPE_UNSPECIFIED", "GLOBAL", "REGIONAL"], "enumDescriptions": ["An invalid type as the default case.", "This regional endpoint is accessible from all regions.", "This regional endpoint is only accessible from the same region where it resides."], "type": "string"}, "address": {"description": "Optional. The IP Address of the Regional Endpoint. When no address is provided, an IP from the subnetwork is allocated. Use one of the following formats: * IPv4 address as in `********` * Address resource URI as in `projects/{project}/regions/{region}/addresses/{address_name}` for an IPv4 or IPv6 address.", "type": "string"}, "createTime": {"description": "Output only. Time when the RegionalEndpoint was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. A description of this resource.", "type": "string"}, "ipAddress": {"deprecated": true, "description": "Output only. The literal IP address of the PSC Forwarding Rule created on behalf of the customer. This field is deprecated. Use address instead.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "User-defined labels.", "type": "object"}, "name": {"description": "Output only. The name of a RegionalEndpoint. Pattern: `projects/{project}/locations/{location}/regionalEndpoints/^[-a-z0-9](?:[-a-z0-9]{0,44})[a-z0-9]$`.", "readOnly": true, "type": "string"}, "network": {"description": "The name of the VPC network for this private regional endpoint. Format: `projects/{project}/global/networks/{network}`", "type": "string"}, "pscForwardingRule": {"description": "Output only. The resource reference of the PSC Forwarding Rule created on behalf of the customer. Format: `//compute.googleapis.com/projects/{project}/regions/{region}/forwardingRules/{forwarding_rule_name}`", "readOnly": true, "type": "string"}, "subnetwork": {"description": "The name of the subnetwork from which the IP address will be allocated. Format: `projects/{project}/regions/{region}/subnetworks/{subnetwork}`", "type": "string"}, "targetGoogleApi": {"description": "Required. The service endpoint this private regional endpoint connects to. Format: `{apiname}.{region}.p.rep.googleapis.com` Example: \"cloudkms.us-central1.p.rep.googleapis.com\".", "type": "string"}, "updateTime": {"description": "Output only. Time when the RegionalEndpoint was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "RejectHubSpokeRequest": {"description": "The request for HubService.RejectHubSpoke.", "id": "RejectHubSpokeRequest", "properties": {"details": {"description": "Optional. Additional information provided by the hub administrator.", "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check to see whether the original operation was received. If it was, the server ignores the second request. This behavior prevents clients from mistakenly creating duplicate commitments. The request ID must be a valid UUID, with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}, "spokeUri": {"description": "Required. The URI of the spoke to reject from the hub.", "type": "string"}}, "type": "object"}, "RejectHubSpokeResponse": {"description": "The response for HubService.RejectHubSpoke.", "id": "RejectHubSpokeResponse", "properties": {"spoke": {"$ref": "Spoke", "description": "The spoke that was operated on."}}, "type": "object"}, "RejectSpokeUpdateRequest": {"description": "The request for HubService.RejectSpokeUpdate.", "id": "RejectSpokeUpdateRequest", "properties": {"details": {"description": "Optional. Additional information provided by the hub administrator.", "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check to see whether the original operation was received. If it was, the server ignores the second request. This behavior prevents clients from mistakenly creating duplicate commitments. The request ID must be a valid UUID, with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}, "spokeEtag": {"description": "Required. The etag of the spoke to reject update.", "type": "string"}, "spokeUri": {"description": "Required. The URI of the spoke to reject update.", "type": "string"}}, "type": "object"}, "RemoteTransportProfile": {"description": "Message describing RemoteTransportProfile object", "id": "RemoteTransportProfile", "properties": {"createTime": {"description": "Output only. [Output only] Create time stamp", "format": "google-datetime", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels as key value pairs", "type": "object"}, "name": {"description": "Identifier. name of resource", "type": "string"}, "updateTime": {"description": "Output only. [Output only] Update time stamp", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "Route": {"description": "A route defines a path from VM instances within a spoke to a specific destination resource. Only VPC spokes have routes.", "id": "Route", "properties": {"createTime": {"description": "Output only. The time the route was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "An optional description of the route.", "type": "string"}, "ipCidrRange": {"description": "The destination IP address range.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional labels in key-value pair format. For more information about labels, see [Requirements for labels](https://cloud.google.com/resource-manager/docs/creating-managing-labels#requirements).", "type": "object"}, "location": {"description": "Output only. The origin location of the route. Uses the following form: \"projects/{project}/locations/{location}\" Example: projects/1234/locations/us-central1", "readOnly": true, "type": "string"}, "name": {"description": "Immutable. The name of the route. Route names must be unique. Route names use the following form: `projects/{project_number}/locations/global/hubs/{hub}/routeTables/{route_table_id}/routes/{route_id}`", "type": "string"}, "nextHopInterconnectAttachment": {"$ref": "NextHopInterconnectAttachment", "description": "Immutable. The next-hop VLAN attachment for packets on this route."}, "nextHopRouterApplianceInstance": {"$ref": "NextHopRouterApplianceInstance", "description": "Immutable. The next-hop Router appliance instance for packets on this route."}, "nextHopSpoke": {"$ref": "NextHopSpoke", "description": "Immutable. The next-hop spoke for packets on this route."}, "nextHopVpcNetwork": {"$ref": "NextHopVpcNetwork", "description": "Immutable. The destination VPC network for packets on this route."}, "nextHopVpnTunnel": {"$ref": "NextHopVPNTunnel", "description": "Immutable. The next-hop VPN tunnel for packets on this route."}, "priority": {"description": "Output only. The priority of this route. Priority is used to break ties in cases where a destination matches more than one route. In these cases the route with the lowest-numbered priority value wins.", "format": "int64", "readOnly": true, "type": "string"}, "spoke": {"description": "Immutable. The spoke that this route leads to. Example: projects/12345/locations/global/spokes/SPOKE", "type": "string"}, "state": {"description": "Output only. The current lifecycle state of the route.", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "DELETING", "ACCEPTING", "REJECTING", "UPDATING", "INACTIVE", "OBSOLETE", "FAILED"], "enumDescriptions": ["No state information available", "The resource's create operation is in progress.", "The resource is active", "The resource's delete operation is in progress.", "The resource's accept operation is in progress.", "The resource's reject operation is in progress.", "The resource's update operation is in progress.", "The resource is inactive.", "The hub associated with this spoke resource has been deleted. This state applies to spoke resources only.", "The resource is in an undefined state due to resource creation or deletion failure. You can try to delete the resource later or contact support for help."], "readOnly": true, "type": "string"}, "type": {"description": "Output only. The route's type. Its type is determined by the properties of its IP address range.", "enum": ["ROUTE_TYPE_UNSPECIFIED", "VPC_PRIMARY_SUBNET", "VPC_SECONDARY_SUBNET", "DYNAMIC_ROUTE"], "enumDescriptions": ["No route type information specified", "The route leads to a destination within the primary address range of the VPC network's subnet.", "The route leads to a destination within the secondary address range of the VPC network's subnet.", "The route leads to a destination in a dynamic route. Dynamic routes are derived from Border Gateway Protocol (BGP) advertisements received from an NCC hybrid spoke."], "readOnly": true, "type": "string"}, "uid": {"description": "Output only. The Google-generated UUID for the route. This value is unique across all Network Connectivity Center route resources. If a route is deleted and another with the same name is created, the new route is assigned a different `uid`.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The time the route was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "RouteTable": {"id": "RouteTable", "properties": {"createTime": {"description": "Output only. The time the route table was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "An optional description of the route table.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional labels in key-value pair format. For more information about labels, see [Requirements for labels](https://cloud.google.com/resource-manager/docs/creating-managing-labels#requirements).", "type": "object"}, "name": {"description": "Immutable. The name of the route table. Route table names must be unique. They use the following form: `projects/{project_number}/locations/global/hubs/{hub}/routeTables/{route_table_id}`", "type": "string"}, "state": {"description": "Output only. The current lifecycle state of this route table.", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "DELETING", "ACCEPTING", "REJECTING", "UPDATING", "INACTIVE", "OBSOLETE", "FAILED"], "enumDescriptions": ["No state information available", "The resource's create operation is in progress.", "The resource is active", "The resource's delete operation is in progress.", "The resource's accept operation is in progress.", "The resource's reject operation is in progress.", "The resource's update operation is in progress.", "The resource is inactive.", "The hub associated with this spoke resource has been deleted. This state applies to spoke resources only.", "The resource is in an undefined state due to resource creation or deletion failure. You can try to delete the resource later or contact support for help."], "readOnly": true, "type": "string"}, "uid": {"description": "Output only. The Google-generated UUID for the route table. This value is unique across all route table resources. If a route table is deleted and another with the same name is created, the new route table is assigned a different `uid`.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The time the route table was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "RouterApplianceInstance": {"description": "A router appliance instance is a Compute Engine virtual machine (VM) instance that acts as a BGP speaker. A router appliance instance is specified by the URI of the VM and the internal IP address of one of the VM's network interfaces.", "id": "RouterApplianceInstance", "properties": {"ipAddress": {"description": "The IP address on the VM to use for peering.", "type": "string"}, "virtualMachine": {"description": "The URI of the VM.", "type": "string"}}, "type": "object"}, "RoutingVPC": {"description": "RoutingVPC contains information about the VPC networks associated with the spokes of a Network Connectivity Center hub.", "id": "RoutingVPC", "properties": {"requiredForNewSiteToSiteDataTransferSpokes": {"description": "Output only. If true, indicates that this VPC network is currently associated with spokes that use the data transfer feature (spokes where the site_to_site_data_transfer field is set to true). If you create new spokes that use data transfer, they must be associated with this VPC network. At most, one VPC network will have this field set to true.", "readOnly": true, "type": "boolean"}, "uri": {"description": "The URI of the VPC network.", "type": "string"}}, "type": "object"}, "ServiceClass": {"description": "The ServiceClass resource.", "id": "ServiceClass", "properties": {"createTime": {"description": "Output only. Time when the ServiceClass was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "A description of this resource.", "type": "string"}, "etag": {"description": "Optional. The etag is computed by the server, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "User-defined labels.", "type": "object"}, "name": {"description": "Immutable. The name of a ServiceClass resource. Format: projects/{project}/locations/{location}/serviceClasses/{service_class} See: https://google.aip.dev/122#fields-representing-resource-names", "type": "string"}, "serviceClass": {"description": "Output only. The generated service class name. Use this name to refer to the Service class in Service Connection Maps and Service Connection Policies.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Time when the ServiceClass was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ServiceConfig": {"description": "Specifies eligibility information for the service.", "id": "ServiceConfig", "properties": {"eligibilityCriteria": {"description": "Output only. The eligibility criteria for the service.", "enum": ["ELIGIBILITY_CRITERIA_UNSPECIFIED", "NETWORK_SERVICE_TIER_PREMIUM_ONLY", "NETWORK_SERVICE_TIER_STANDARD_ONLY", "REQUEST_ENDPOINT_REGIONAL_ENDPOINT_ONLY"], "enumDescriptions": ["The service is not eligible for Data Transfer Essentials configuration. This is the default case.", "The service is eligible for Data Transfer Essentials configuration only for Premium Tier.", "The service is eligible for Data Transfer Essentials configuration only for Standard Tier.", "The service is eligible for Data Transfer Essentials configuration only for the regional endpoint."], "readOnly": true, "type": "string"}, "supportEndTime": {"description": "Output only. The end time for eligibility criteria support. If not specified, no planned end time is set.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ServiceConnectionMap": {"description": "The ServiceConnectionMap resource.", "id": "ServiceConnectionMap", "properties": {"consumerPscConfigs": {"description": "The PSC configurations on consumer side.", "items": {"$ref": "ConsumerPscConfig"}, "type": "array"}, "consumerPscConnections": {"description": "Output only. PSC connection details on consumer side.", "items": {"$ref": "ConsumerPscConnection"}, "readOnly": true, "type": "array"}, "createTime": {"description": "Output only. Time when the ServiceConnectionMap was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "A description of this resource.", "type": "string"}, "etag": {"description": "Optional. The etag is computed by the server, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.", "type": "string"}, "infrastructure": {"description": "Output only. The infrastructure used for connections between consumers/producers.", "enum": ["INFRASTRUCTURE_UNSPECIFIED", "PSC"], "enumDescriptions": ["An invalid infrastructure as the default case.", "Private Service Connect is used for connections."], "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "User-defined labels.", "type": "object"}, "name": {"description": "Immutable. The name of a ServiceConnectionMap. Format: projects/{project}/locations/{location}/serviceConnectionMaps/{service_connection_map} See: https://google.aip.dev/122#fields-representing-resource-names", "type": "string"}, "producerPscConfigs": {"description": "The PSC configurations on producer side.", "items": {"$ref": "ProducerPscConfig"}, "type": "array"}, "serviceClass": {"description": "The service class identifier this ServiceConnectionMap is for. The user of ServiceConnectionMap create API needs to have networkconnectivity.serviceClasses.use IAM permission for the service class.", "type": "string"}, "serviceClassUri": {"description": "Output only. The service class uri this ServiceConnectionMap is for.", "readOnly": true, "type": "string"}, "token": {"description": "The token provided by the consumer. This token authenticates that the consumer can create a connection within the specified project and network.", "type": "string"}, "updateTime": {"description": "Output only. Time when the ServiceConnectionMap was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ServiceConnectionPolicy": {"description": "The ServiceConnectionPolicy resource.", "id": "ServiceConnectionPolicy", "properties": {"autoCreatedSubnetInfo": {"$ref": "AutoCreatedSubnetworkInfo", "description": "Output only. Information for the automatically created subnetwork and its associated IR.", "readOnly": true}, "createTime": {"description": "Output only. Time when the ServiceConnectionPolicy was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "A description of this resource.", "type": "string"}, "etag": {"description": "Optional. The etag is computed by the server, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.", "type": "string"}, "infrastructure": {"description": "Output only. The type of underlying resources used to create the connection.", "enum": ["INFRASTRUCTURE_UNSPECIFIED", "PSC"], "enumDescriptions": ["An invalid infrastructure as the default case.", "Private Service Connect is used for connections."], "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "User-defined labels.", "type": "object"}, "name": {"description": "Immutable. The name of a ServiceConnectionPolicy. Format: projects/{project}/locations/{location}/serviceConnectionPolicies/{service_connection_policy} See: https://google.aip.dev/122#fields-representing-resource-names", "type": "string"}, "network": {"description": "The resource path of the consumer network. Example: - projects/{projectNumOrId}/global/networks/{resourceId}.", "type": "string"}, "pscConfig": {"$ref": "PscConfig", "description": "Configuration used for Private Service Connect connections. Used when Infrastructure is PSC."}, "pscConnections": {"description": "Output only. [Output only] Information about each Private Service Connect connection.", "items": {"$ref": "PscConnection"}, "readOnly": true, "type": "array"}, "serviceClass": {"description": "The service class identifier for which this ServiceConnectionPolicy is for. The service class identifier is a unique, symbolic representation of a ServiceClass. It is provided by the Service Producer. Google services have a prefix of gcp or google-cloud. For example, gcp-memorystore-redis or google-cloud-sql. 3rd party services do not. For example, test-service-a3dfcx.", "type": "string"}, "updateTime": {"description": "Output only. Time when the ServiceConnectionPolicy was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ServiceConnectionToken": {"description": "The ServiceConnectionToken resource.", "id": "ServiceConnectionToken", "properties": {"createTime": {"description": "Output only. Time when the ServiceConnectionToken was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "A description of this resource.", "type": "string"}, "etag": {"description": "Optional. The etag is computed by the server, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.", "type": "string"}, "expireTime": {"description": "Output only. The time to which this token is valid.", "format": "google-datetime", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "User-defined labels.", "type": "object"}, "name": {"description": "Immutable. The name of a ServiceConnectionToken. Format: projects/{project}/locations/{location}/ServiceConnectionTokens/{service_connection_token} See: https://google.aip.dev/122#fields-representing-resource-names", "type": "string"}, "network": {"description": "The resource path of the network associated with this token. Example: projects/{projectNumOrId}/global/networks/{resourceId}.", "type": "string"}, "token": {"description": "Output only. The token generated by Automation.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Time when the ServiceConnectionToken was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "SetIamPolicyRequest": {"description": "Request message for `SetIamPolicy` method.", "id": "SetIamPolicyRequest", "properties": {"policy": {"$ref": "Policy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}, "updateMask": {"description": "OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: \"bindings, etag\"`", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "Spoke": {"description": "A Network Connectivity Center spoke represents one or more network connectivity resources. When you create a spoke, you associate it with a hub. You must also identify a value for exactly one of the following fields: * linked_vpn_tunnels * linked_interconnect_attachments * linked_router_appliance_instances * linked_vpc_network", "id": "Spoke", "properties": {"createTime": {"description": "Output only. The time the spoke was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. An optional description of the spoke.", "type": "string"}, "etag": {"description": "Optional. This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.", "type": "string"}, "fieldPathsPendingUpdate": {"description": "Optional. The list of fields waiting for hub administration's approval.", "items": {"type": "string"}, "type": "array"}, "group": {"description": "Optional. The name of the group that this spoke is associated with.", "type": "string"}, "hub": {"description": "Immutable. The name of the hub that this spoke is attached to.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional labels in key-value pair format. For more information about labels, see [Requirements for labels](https://cloud.google.com/resource-manager/docs/creating-managing-labels#requirements).", "type": "object"}, "linkedInterconnectAttachments": {"$ref": "LinkedInterconnectAttachments", "description": "Optional. VLAN attachments that are associated with the spoke."}, "linkedProducerVpcNetwork": {"$ref": "LinkedProducerVpcNetwork", "description": "Optional. The linked producer VPC that is associated with the spoke."}, "linkedRouterApplianceInstances": {"$ref": "LinkedRouterApplianceInstances", "description": "Optional. Router appliance instances that are associated with the spoke."}, "linkedVpcNetwork": {"$ref": "LinkedVpcNetwork", "description": "Optional. VPC network that is associated with the spoke."}, "linkedVpnTunnels": {"$ref": "LinkedVpnTunnels", "description": "Optional. VPN tunnels that are associated with the spoke."}, "name": {"description": "Immutable. The name of the spoke. Spoke names must be unique. They use the following form: `projects/{project_number}/locations/{region}/spokes/{spoke_id}`", "type": "string"}, "reasons": {"description": "Output only. The reasons for current state of the spoke.", "items": {"$ref": "StateReason"}, "readOnly": true, "type": "array"}, "spokeType": {"description": "Output only. The type of resource associated with the spoke.", "enum": ["SPOKE_TYPE_UNSPECIFIED", "VPN_TUNNEL", "INTERCONNECT_ATTACHMENT", "ROUTER_APPLIANCE", "VPC_NETWORK", "PRODUCER_VPC_NETWORK"], "enumDescriptions": ["Unspecified spoke type.", "Spokes associated with VPN tunnels.", "Spokes associated with VLAN attachments.", "Spokes associated with router appliance instances.", "Spokes associated with VPC networks.", "Spokes that are backed by a producer VPC network."], "readOnly": true, "type": "string"}, "state": {"description": "Output only. The current lifecycle state of this spoke.", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "DELETING", "ACCEPTING", "REJECTING", "UPDATING", "INACTIVE", "OBSOLETE", "FAILED"], "enumDescriptions": ["No state information available", "The resource's create operation is in progress.", "The resource is active", "The resource's delete operation is in progress.", "The resource's accept operation is in progress.", "The resource's reject operation is in progress.", "The resource's update operation is in progress.", "The resource is inactive.", "The hub associated with this spoke resource has been deleted. This state applies to spoke resources only.", "The resource is in an undefined state due to resource creation or deletion failure. You can try to delete the resource later or contact support for help."], "readOnly": true, "type": "string"}, "uniqueId": {"description": "Output only. The Google-generated UUID for the spoke. This value is unique across all spoke resources. If a spoke is deleted and another with the same name is created, the new spoke is assigned a different `unique_id`.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The time the spoke was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "SpokeStateCount": {"description": "The number of spokes that are in a particular state and associated with a given hub.", "id": "SpokeStateCount", "properties": {"count": {"description": "Output only. The total number of spokes that are in this state and associated with a given hub.", "format": "int64", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The state of the spokes.", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "DELETING", "ACCEPTING", "REJECTING", "UPDATING", "INACTIVE", "OBSOLETE", "FAILED"], "enumDescriptions": ["No state information available", "The resource's create operation is in progress.", "The resource is active", "The resource's delete operation is in progress.", "The resource's accept operation is in progress.", "The resource's reject operation is in progress.", "The resource's update operation is in progress.", "The resource is inactive.", "The hub associated with this spoke resource has been deleted. This state applies to spoke resources only.", "The resource is in an undefined state due to resource creation or deletion failure. You can try to delete the resource later or contact support for help."], "readOnly": true, "type": "string"}}, "type": "object"}, "SpokeStateReasonCount": {"description": "The number of spokes in the hub that are inactive for this reason.", "id": "SpokeStateReasonCount", "properties": {"count": {"description": "Output only. The total number of spokes that are inactive for a particular reason and associated with a given hub.", "format": "int64", "readOnly": true, "type": "string"}, "stateReasonCode": {"description": "Output only. The reason that a spoke is inactive.", "enum": ["CODE_UNSPECIFIED", "PENDING_REVIEW", "REJECTED", "PAUSED", "FAILED", "UPDATE_PENDING_REVIEW", "UPDATE_REJECTED", "UPDATE_FAILED"], "enumDescriptions": ["No information available.", "The proposed spoke is pending review.", "The proposed spoke has been rejected by the hub administrator.", "The spoke has been deactivated internally.", "Network Connectivity Center encountered errors while accepting the spoke.", "The proposed spoke update is pending review.", "The proposed spoke update has been rejected by the hub administrator.", "Network Connectivity Center encountered errors while accepting the spoke update."], "readOnly": true, "type": "string"}}, "type": "object"}, "SpokeSummary": {"description": "Summarizes information about the spokes associated with a hub. The summary includes a count of spokes according to type and according to state. If any spokes are inactive, the summary also lists the reasons they are inactive, including a count for each reason.", "id": "SpokeSummary", "properties": {"spokeStateCounts": {"description": "Output only. Counts the number of spokes that are in each state and associated with a given hub.", "items": {"$ref": "SpokeStateCount"}, "readOnly": true, "type": "array"}, "spokeStateReasonCounts": {"description": "Output only. Counts the number of spokes that are inactive for each possible reason and associated with a given hub.", "items": {"$ref": "SpokeStateReasonCount"}, "readOnly": true, "type": "array"}, "spokeTypeCounts": {"description": "Output only. Counts the number of spokes of each type that are associated with a specific hub.", "items": {"$ref": "SpokeTypeCount"}, "readOnly": true, "type": "array"}}, "type": "object"}, "SpokeTypeCount": {"description": "The number of spokes of a given type that are associated with a specific hub. The type indicates what kind of resource is associated with the spoke.", "id": "SpokeTypeCount", "properties": {"count": {"description": "Output only. The total number of spokes of this type that are associated with the hub.", "format": "int64", "readOnly": true, "type": "string"}, "spokeType": {"description": "Output only. The type of the spokes.", "enum": ["SPOKE_TYPE_UNSPECIFIED", "VPN_TUNNEL", "INTERCONNECT_ATTACHMENT", "ROUTER_APPLIANCE", "VPC_NETWORK", "PRODUCER_VPC_NETWORK"], "enumDescriptions": ["Unspecified spoke type.", "Spokes associated with VPN tunnels.", "Spokes associated with VLAN attachments.", "Spokes associated with router appliance instances.", "Spokes associated with VPC networks.", "Spokes that are backed by a producer VPC network."], "readOnly": true, "type": "string"}}, "type": "object"}, "StateMetadata": {"description": "The state and activation time details of the resource state.", "id": "StateMetadata", "properties": {"effectiveTime": {"description": "Output only. Accompanies only the transient states, which include `ADDING`, `DELETING`, and `SUSPENDING`, to denote the time until which the transient state of the resource will be effective. For instance, if the state is `ADDING`, this field shows the time when the resource state transitions to `ACTIVE`.", "format": "google-datetime", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The state of the resource.", "enum": ["STATE_UNSPECIFIED", "ADDING", "ACTIVE", "DELETING", "SUSPENDING", "SUSPENDED"], "enumDescriptions": ["An invalid state, which is the default case.", "The resource is being added.", "The resource is in use.", "The resource is being deleted.", "The resource is being suspended.", "The resource is suspended and not in use."], "readOnly": true, "type": "string"}}, "type": "object"}, "StateReason": {"description": "The reason a spoke is inactive.", "id": "StateReason", "properties": {"code": {"description": "The code associated with this reason.", "enum": ["CODE_UNSPECIFIED", "PENDING_REVIEW", "REJECTED", "PAUSED", "FAILED", "UPDATE_PENDING_REVIEW", "UPDATE_REJECTED", "UPDATE_FAILED"], "enumDescriptions": ["No information available.", "The proposed spoke is pending review.", "The proposed spoke has been rejected by the hub administrator.", "The spoke has been deactivated internally.", "Network Connectivity Center encountered errors while accepting the spoke.", "The proposed spoke update is pending review.", "The proposed spoke update has been rejected by the hub administrator.", "Network Connectivity Center encountered errors while accepting the spoke update."], "type": "string"}, "message": {"description": "Human-readable details about this reason.", "type": "string"}, "userDetails": {"description": "Additional information provided by the user in the RejectSpoke call.", "type": "string"}}, "type": "object"}, "StateTimeline": {"description": "The timeline of the pending states for a resource.", "id": "StateTimeline", "properties": {"states": {"description": "Output only. The state and activation time details of the resource state.", "items": {"$ref": "StateMetadata"}, "readOnly": true, "type": "array"}}, "type": "object"}, "TestIamPermissionsRequest": {"description": "Request message for `TestIamPermissions` method.", "id": "TestIamPermissionsRequest", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TestIamPermissionsResponse": {"description": "Response message for `TestIamPermissions` method.", "id": "TestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Transport": {"description": "Message describing Transport object", "id": "Transport", "properties": {"createTime": {"description": "Output only. [Output only] Create time stamp", "format": "google-datetime", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels as key value pairs", "type": "object"}, "name": {"description": "Identifier. name of resource", "type": "string"}, "updateTime": {"description": "Output only. [Output only] Update time stamp", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "VirtualMachine": {"description": "VM instances that this policy-based route applies to.", "id": "VirtualMachine", "properties": {"tags": {"description": "Optional. A list of VM instance tags that this policy-based route applies to. VM instances that have ANY of tags specified here installs this PBR.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Warnings": {"description": "Informational warning message.", "id": "Warnings", "properties": {"code": {"description": "Output only. A warning code, if applicable.", "enum": ["WARNING_UNSPECIFIED", "RESOURCE_NOT_ACTIVE", "RESOURCE_BEING_MODIFIED"], "enumDescriptions": ["Default value.", "The policy-based route is not active and functioning. Common causes are that the dependent network was deleted or the resource project was turned off.", "The policy-based route is being modified (e.g. created/deleted) at this time."], "readOnly": true, "type": "string"}, "data": {"additionalProperties": {"type": "string"}, "description": "Output only. Metadata about this warning in key: value format. The key should provides more detail on the warning being returned. For example, for warnings where there are no results in a list request for a particular zone, this key might be scope and the key value might be the zone name. Other examples might be a key indicating a deprecated resource and a suggested replacement.", "readOnly": true, "type": "object"}, "warningMessage": {"description": "Output only. A human-readable description of the warning code.", "readOnly": true, "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Network Connectivity API", "version": "v1", "version_module": true}