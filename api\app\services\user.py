"""
User service for the QueTeToca API.
"""
import logging
from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from ..models import User, Company
from ..models.enums import UserRole
from ..schemas.user import UserCreate, UserUpdate

logger = logging.getLogger(__name__)


class UserService:
    """Service for user operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_user_by_uid(self, uid: str) -> Optional[User]:
        """Get user by Firebase UID."""
        return self.db.query(User).filter(User.uid == uid).first()
    
    def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email."""
        return self.db.query(User).filter(User.email == email).first()
    
    def get_users(self, skip: int = 0, limit: int = 100, 
                  company_id: Optional[str] = None,
                  role: Optional[UserRole] = None) -> List[User]:
        """Get list of users with optional filters."""
        query = self.db.query(User)
        
        if company_id:
            query = query.filter(User.company_id == company_id)
        
        if role:
            query = query.filter(User.role == role)
        
        return query.offset(skip).limit(limit).all()
    
    def create_user(self, user_data: UserCreate) -> User:
        """Create a new user."""
        # Check if user already exists
        existing_user = self.get_user_by_uid(user_data.uid)
        if existing_user:
            raise ValueError(f"User with UID {user_data.uid} already exists")
        
        existing_email = self.get_user_by_email(user_data.email)
        if existing_email:
            raise ValueError(f"User with email {user_data.email} already exists")
        
        # Validate company if provided
        if user_data.company_id:
            company = self.db.query(Company).filter(Company.id == user_data.company_id).first()
            if not company:
                raise ValueError(f"Company with ID {user_data.company_id} not found")
        
        user = User(**user_data.model_dump())
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)
        
        logger.info(f"User created: {user.email}")
        return user
    
    def update_user(self, uid: str, user_data: UserUpdate) -> Optional[User]:
        """Update an existing user."""
        user = self.get_user_by_uid(uid)
        if not user:
            return None
        
        # Validate company if being updated
        if user_data.company_id:
            company = self.db.query(Company).filter(Company.id == user_data.company_id).first()
            if not company:
                raise ValueError(f"Company with ID {user_data.company_id} not found")
        
        # Update fields
        update_data = user_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(user, field, value)
        
        self.db.commit()
        self.db.refresh(user)
        
        logger.info(f"User updated: {user.email}")
        return user
    
    def delete_user(self, uid: str) -> bool:
        """Delete a user."""
        user = self.get_user_by_uid(uid)
        if not user:
            return False
        
        self.db.delete(user)
        self.db.commit()
        
        logger.info(f"User deleted: {user.email}")
        return True
    
    def assign_to_company(self, uid: str, company_id: str, role: UserRole = UserRole.BAR_MANAGER) -> Optional[User]:
        """Assign user to a company with a specific role."""
        user = self.get_user_by_uid(uid)
        if not user:
            return None
        
        company = self.db.query(Company).filter(Company.id == company_id).first()
        if not company:
            raise ValueError(f"Company with ID {company_id} not found")
        
        user.company_id = company_id
        user.role = role
        
        self.db.commit()
        self.db.refresh(user)
        
        logger.info(f"User {user.email} assigned to company {company_id} with role {role}")
        return user
    
    def remove_from_company(self, uid: str) -> Optional[User]:
        """Remove user from their current company."""
        user = self.get_user_by_uid(uid)
        if not user:
            return None
        
        old_company_id = user.company_id
        user.company_id = None
        user.role = UserRole.CUSTOMER
        
        self.db.commit()
        self.db.refresh(user)
        
        logger.info(f"User {user.email} removed from company {old_company_id}")
        return user
    
    def get_company_users(self, company_id: str) -> List[User]:
        """Get all users for a specific company."""
        return self.db.query(User).filter(User.company_id == company_id).all()
    
    def get_company_admins(self, company_id: str) -> List[User]:
        """Get all admin users for a specific company."""
        return self.db.query(User).filter(
            and_(
                User.company_id == company_id,
                or_(
                    User.role == UserRole.COMPANY_ADMIN,
                    User.role == UserRole.SUPER_ADMIN
                )
            )
        ).all()
    
    def promote_to_admin(self, uid: str) -> Optional[User]:
        """Promote user to company admin."""
        user = self.get_user_by_uid(uid)
        if not user:
            return None
        
        if not user.company_id:
            raise ValueError("User must be assigned to a company first")
        
        user.role = UserRole.COMPANY_ADMIN
        
        # Add to company admin list
        company = self.db.query(Company).filter(Company.id == user.company_id).first()
        if company:
            company.add_admin(user.uid)
        
        self.db.commit()
        self.db.refresh(user)
        
        logger.info(f"User {user.email} promoted to company admin")
        return user
    
    def demote_from_admin(self, uid: str) -> Optional[User]:
        """Demote user from company admin."""
        user = self.get_user_by_uid(uid)
        if not user:
            return None
        
        if user.role == UserRole.SUPER_ADMIN:
            raise ValueError("Cannot demote super admin")
        
        user.role = UserRole.BAR_MANAGER
        
        # Remove from company admin list
        if user.company_id:
            company = self.db.query(Company).filter(Company.id == user.company_id).first()
            if company:
                company.remove_admin(user.uid)
        
        self.db.commit()
        self.db.refresh(user)
        
        logger.info(f"User {user.email} demoted from company admin")
        return user
