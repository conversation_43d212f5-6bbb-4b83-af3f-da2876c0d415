"""
Bar service for the QueTeToca API.
"""
import logging
from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_

from ..models import Bar, Company, QueueEntry
from ..models.enums import QueueStatus
from ..schemas.bar import BarCreate, BarUpdate
from .qr_code import QRCodeService

logger = logging.getLogger(__name__)


class BarService:
    """Service for bar operations."""
    
    def __init__(self, db: Session):
        self.db = db
        self.qr_service = QRCodeService()
    
    def get_bar_by_id(self, bar_id: str) -> Optional[Bar]:
        """Get bar by ID."""
        return self.db.query(Bar).filter(Bar.id == bar_id).first()
    
    def get_bars(self, skip: int = 0, limit: int = 100, 
                 company_id: Optional[str] = None,
                 is_active: Optional[bool] = None) -> List[Bar]:
        """Get list of bars with optional filters."""
        query = self.db.query(Bar)
        
        if company_id:
            query = query.filter(Bar.company_id == company_id)
        
        if is_active is not None:
            active_value = "true" if is_active else "false"
            query = query.filter(Bar.is_active == active_value)
        
        return query.offset(skip).limit(limit).all()
    
    def get_company_bars(self, company_id: str) -> List[Bar]:
        """Get all bars for a specific company."""
        return self.db.query(Bar).filter(Bar.company_id == company_id).all()
    
    def create_bar(self, bar_data: BarCreate) -> Bar:
        """Create a new bar."""
        # Validate company exists
        company = self.db.query(Company).filter(Company.id == bar_data.company_id).first()
        if not company:
            raise ValueError(f"Company with ID {bar_data.company_id} not found")
        
        # Create bar
        bar = Bar(**bar_data.model_dump())
        self.db.add(bar)
        self.db.flush()  # Get the ID without committing
        
        # Generate QR code
        try:
            qr_code, qr_url = self.qr_service.generate_bar_qr_code(bar.id, bar.name)
            bar.update_qr_code(qr_code, qr_url)
        except Exception as e:
            logger.warning(f"Could not generate QR code for bar {bar.id}: {e}")
        
        self.db.commit()
        self.db.refresh(bar)
        
        logger.info(f"Bar created: {bar.name} (ID: {bar.id})")
        return bar
    
    def update_bar(self, bar_id: str, bar_data: BarUpdate) -> Optional[Bar]:
        """Update an existing bar."""
        bar = self.get_bar_by_id(bar_id)
        if not bar:
            return None
        
        # Update fields
        update_data = bar_data.model_dump(exclude_unset=True)
        
        # Handle is_active boolean to string conversion
        if 'is_active' in update_data:
            update_data['is_active'] = "true" if update_data['is_active'] else "false"
        
        for field, value in update_data.items():
            setattr(bar, field, value)
        
        # Regenerate QR code if name changed
        if 'name' in update_data:
            try:
                qr_code, qr_url = self.qr_service.generate_bar_qr_code(bar.id, bar.name)
                bar.update_qr_code(qr_code, qr_url)
            except Exception as e:
                logger.warning(f"Could not regenerate QR code for bar {bar.id}: {e}")
        
        self.db.commit()
        self.db.refresh(bar)
        
        logger.info(f"Bar updated: {bar.name} (ID: {bar.id})")
        return bar
    
    def delete_bar(self, bar_id: str) -> bool:
        """Delete a bar."""
        bar = self.get_bar_by_id(bar_id)
        if not bar:
            return False
        
        # Check if bar has active queue entries
        active_entries = self.db.query(QueueEntry).filter(
            and_(
                QueueEntry.bar_id == bar_id,
                QueueEntry.status.in_([QueueStatus.WAITING, QueueStatus.CALLED])
            )
        ).count()
        
        if active_entries > 0:
            raise ValueError(f"Cannot delete bar with {active_entries} active queue entries")
        
        self.db.delete(bar)
        self.db.commit()
        
        logger.info(f"Bar deleted: {bar.name} (ID: {bar.id})")
        return True
    
    def activate_bar(self, bar_id: str) -> Optional[Bar]:
        """Activate a bar."""
        bar = self.get_bar_by_id(bar_id)
        if not bar:
            return None
        
        bar.activate()
        self.db.commit()
        self.db.refresh(bar)
        
        logger.info(f"Bar activated: {bar.name} (ID: {bar.id})")
        return bar
    
    def deactivate_bar(self, bar_id: str) -> Optional[Bar]:
        """Deactivate a bar."""
        bar = self.get_bar_by_id(bar_id)
        if not bar:
            return None
        
        bar.deactivate()
        self.db.commit()
        self.db.refresh(bar)
        
        logger.info(f"Bar deactivated: {bar.name} (ID: {bar.id})")
        return bar
    
    def regenerate_qr_code(self, bar_id: str) -> Optional[tuple[str, str]]:
        """Regenerate QR code for a bar."""
        bar = self.get_bar_by_id(bar_id)
        if not bar:
            return None
        
        try:
            qr_code, qr_url = self.qr_service.generate_bar_qr_code(bar.id, bar.name)
            bar.update_qr_code(qr_code, qr_url)
            
            self.db.commit()
            self.db.refresh(bar)
            
            logger.info(f"QR code regenerated for bar: {bar.name} (ID: {bar.id})")
            return qr_code, qr_url
        
        except Exception as e:
            logger.error(f"Error regenerating QR code for bar {bar_id}: {e}")
            raise
    
    def get_bar_stats(self, bar_id: str) -> dict:
        """Get statistics for a bar."""
        bar = self.get_bar_by_id(bar_id)
        if not bar:
            return {}
        
        # Get queue statistics
        total_entries = self.db.query(QueueEntry).filter(QueueEntry.bar_id == bar_id).count()
        
        waiting_entries = self.db.query(QueueEntry).filter(
            and_(QueueEntry.bar_id == bar_id, QueueEntry.status == QueueStatus.WAITING)
        ).count()
        
        called_entries = self.db.query(QueueEntry).filter(
            and_(QueueEntry.bar_id == bar_id, QueueEntry.status == QueueStatus.CALLED)
        ).count()
        
        completed_entries = self.db.query(QueueEntry).filter(
            and_(QueueEntry.bar_id == bar_id, QueueEntry.status == QueueStatus.COMPLETED)
        ).count()
        
        cancelled_entries = self.db.query(QueueEntry).filter(
            and_(QueueEntry.bar_id == bar_id, QueueEntry.status == QueueStatus.CANCELLED)
        ).count()
        
        # Calculate average wait time for completed entries
        completed_with_times = self.db.query(QueueEntry).filter(
            and_(
                QueueEntry.bar_id == bar_id,
                QueueEntry.status == QueueStatus.COMPLETED,
                QueueEntry.called_at.isnot(None)
            )
        ).all()
        
        avg_wait_time = None
        if completed_with_times:
            total_wait_time = sum(entry.wait_time_minutes or 0 for entry in completed_with_times)
            avg_wait_time = total_wait_time / len(completed_with_times)
        
        return {
            "bar_id": bar_id,
            "total_entries": total_entries,
            "current_queue_size": waiting_entries,
            "called_entries": called_entries,
            "completed_entries": completed_entries,
            "cancelled_entries": cancelled_entries,
            "average_wait_time": avg_wait_time,
            "queue_utilization": (waiting_entries / bar.max_queue_size) * 100 if bar.max_queue_size > 0 else 0
        }
    
    def update_bar_settings(self, bar_id: str, settings: dict) -> Optional[Bar]:
        """Update bar settings."""
        bar = self.get_bar_by_id(bar_id)
        if not bar:
            return None
        
        # Update settings
        for key, value in settings.items():
            bar.set_setting(key, value)
        
        self.db.commit()
        self.db.refresh(bar)
        
        logger.info(f"Settings updated for bar: {bar.name} (ID: {bar.id})")
        return bar
